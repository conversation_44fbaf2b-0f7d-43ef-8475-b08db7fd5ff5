﻿<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI"
 xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys"
 xmlns:iaw="clr-namespace:Infor.Activities.Workflow;assembly=Infor.Activities.Workflow"
 xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities"
 xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json"
 xmlns:s="clr-namespace:System;assembly=mscorlib"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib"
 xmlns:sl="clr-namespace:System.Linq;assembly=System.Core"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="colemanAPI" Type="InArgument(x:String)" />
    <x:Property Name="commentStatus" Type="OutArgument(x:String)" />
    <x:Property Name="logFile" Type="InArgument(x:String)" />
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="status" Type="OutArgument(x:String)" />
    <x:Property Name="projectPath" Type="InArgument(x:String)" />
    <x:Property Name="vendorId" Type="OutArgument(x:String)" />
    <x:Property Name="vendorName" Type="OutArgument(x:String)" />
    <x:Property Name="company" Type="OutArgument(x:String)" />
    <x:Property Name="chargeCode" Type="InArgument(x:String)" />
    <x:Property Name="discountCode" Type="InArgument(x:String)" />
    <x:Property Name="authUser" Type="InArgument(x:String)" />
    <x:Property Name="division" Type="OutArgument(x:String)" />
    <x:Property Name="vatCodeConfig" Type="InArgument(x:String)" />
    <x:Property Name="poDiscountsHandlingConfig" Type="InArgument(x:Boolean)" />
    <x:Property Name="businessRule" Type="InArgument(x:String)" />
    <x:Property Name="ocrText" Type="InArgument(njl:JToken)" />
    <x:Property Name="matchVendorItemCode" Type="InArgument(x:Boolean)" />
    <x:Property Name="documentPath" Type="InArgument(x:String)" />
    <x:Property Name="approvalRequired" Type="InArgument(x:Boolean)" />
    <x:Property Name="approvalWorkflow" Type="InArgument(x:String)" />
    <x:Property Name="checkAmountBussinessRule" Type="InArgument(x:String)" />
    <x:Property Name="miscValues" Type="InArgument(scg:Dictionary(x:String, x:Object))" />
    <x:Property Name="DictOcrValues" Type="InArgument(scg:Dictionary(x:String, x:Object))" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Infor.Activities.IONAPI</x:String>
      <x:String>Infor.Activities.Sys</x:String>
      <x:String>Infor.RPA.Utilities</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>System</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>Microsoft.VisualBasic.CompilerServices</x:String>
      <x:String>System.Linq</x:String>
      <x:String>System.Text.RegularExpressions</x:String>
      <x:String>System.Globalization</x:String>
      <x:String>Microsoft.VisualBasic</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>Infor.Activities.IONAPI</AssemblyReference>
      <AssemblyReference>Infor.Activities.Sys</AssemblyReference>
      <AssemblyReference>Infor.RPA.Utilities</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence DisplayName="ProcessPOInvoiceAPISequence" sap2010:WorkflowViewState.IdRef="Sequence_17">
    <Sequence.Variables>
      <Variable x:TypeArguments="iru:ResponseObject" Name="respObj1" />
      <Variable x:TypeArguments="njl:JToken" Name="out1" />
      <Variable x:TypeArguments="x:String" Name="req1" />
      <Variable x:TypeArguments="x:String" Name="pono" />
      <Variable x:TypeArguments="x:Int32" Name="count1" />
      <Variable x:TypeArguments="x:String" Name="req2" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="respObj2" />
      <Variable x:TypeArguments="njl:JToken" Name="out2" />
      <Variable x:TypeArguments="scg:List(s:String[])" Name="M3TotalTableRows" />
      <Variable x:TypeArguments="x:Int32" Name="i" />
      <Variable x:TypeArguments="s:String[]" Name="M3Values" />
      <Variable x:TypeArguments="x:String" Name="cono" />
      <Variable x:TypeArguments="x:String" Name="cucd" />
      <Variable x:TypeArguments="x:String" Name="SupplierNo" />
      <Variable x:TypeArguments="x:String" Name="APIString" />
      <Variable x:TypeArguments="x:Int32" Name="StatusCode1" />
      <Variable x:TypeArguments="x:String" Name="pyme" />
      <Variable x:TypeArguments="scg:List(x:String)" Name="invoicesItemNumbers" />
      <Variable x:TypeArguments="x:Boolean" Name="allLinesReceived" />
      <Variable x:TypeArguments="scg:IEnumerable(sl:IGrouping(x:String, s:String[]))" Name="transDateGroups" />
      <Variable x:TypeArguments="scg:Dictionary(x:String, scg:List(s:String[]))" Name="transDateDictionary" />
    </Sequence.Variables>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_1">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[pono]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">[DictOcrValues("PO_NUMBER").Tostring]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_136">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[vendorId]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">
          <Literal x:TypeArguments="x:String" Value="" />
        </InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_137">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[vendorName]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">
          <Literal x:TypeArguments="x:String" Value="" />
        </InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_158">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[company]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">
          <Literal x:TypeArguments="x:String" Value="" />
        </InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_157">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[division]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">
          <Literal x:TypeArguments="x:String" Value="" />
        </InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_2">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[req1]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">["IACONO,IADIVI,IASUNO,IACUCD,IATEPY,IAPYME from MPHEAD where IAPUNO = " + pono]</InArgument>
      </Assign.Value>
    </Assign>
    <iai:IONAPIRequestWizard FileAttachments="{x:Null}" Headers="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="ExportMI for comp, div IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_1" Response="[respObj1]" StatusCode="[StatusCode1]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/EXPORTMI/Select?SEPC=~&amp;HDRS=0&amp;dateformat=YMD8&amp;excludeempty=false&amp;righttrim=true&amp;format=PRETTY&amp;extendedresult=false&quot;]">
      <iai:IONAPIRequestWizard.QueryParameters>
        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>QERY</x:String>
          </scg:List>
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>req1</x:String>
          </scg:List>
        </scg:List>
      </iai:IONAPIRequestWizard.QueryParameters>
    </iai:IONAPIRequestWizard>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_240">
      <Assign.To>
        <OutArgument x:TypeArguments="scg:List(x:String)">[invoicesItemNumbers]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="scg:List(x:String)">[New List(Of String)()]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_271">
      <Assign.To>
        <OutArgument x:TypeArguments="scg:Dictionary(x:String, scg:List(s:String[]))">[transDateDictionary]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="scg:Dictionary(x:String, scg:List(s:String[]))">[New Dictionary(Of String,List(Of String()))()]</InArgument>
      </Assign.Value>
    </Assign>
    <If Condition="[StatusCode1 = 200]" DisplayName="If" sap2010:WorkflowViewState.IdRef="If_14">
      <If.Then>
        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_22">
          <Assign sap2010:WorkflowViewState.IdRef="Assign_3">
            <Assign.To>
              <OutArgument x:TypeArguments="njl:JToken">[out1]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj1.ReadAsText)]</InArgument>
            </Assign.Value>
          </Assign>
          <If Condition="[out1(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_11">
            <If.Then>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_1">
                <Assign sap2010:WorkflowViewState.IdRef="Assign_7">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">PONOTAVAILABLE</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_111">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">["PO number is not available"]</InArgument>
                  </Assign.Value>
                </Assign>
                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_1" Line="[commentStatus]" Source="[logFile]" />
              </Sequence>
            </If.Then>
            <If.Else>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_16">
                <Sequence.Variables>
                  <Variable x:TypeArguments="njl:JToken" Name="httpOut" />
                  <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="ColemanOutPut" />
                  <Variable x:TypeArguments="x:Int32" Name="ColemanRespCode" />
                  <Variable x:TypeArguments="x:String" Name="inbnValue" />
                  <Variable x:TypeArguments="x:Int32" Name="StatusCode2" />
                  <Variable x:TypeArguments="x:String" Name="tepy" />
                  <Variable x:TypeArguments="x:Int32" Name="StatusCode3" />
                  <Variable x:TypeArguments="iru:ResponseObject" Name="RespObj3" />
                  <Variable x:TypeArguments="njl:JToken" Name="out3" />
                  <Variable x:TypeArguments="x:String" Name="colAmount" />
                  <Variable x:TypeArguments="x:String" Name="discountHandling" />
                  <Variable x:TypeArguments="iru:ResponseObject" Name="poLinesResponseObject" />
                  <Variable x:TypeArguments="scg:List(x:String)" Name="notReceivedItems" />
                </Sequence.Variables>
                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_19" Line="Extracted the company and division." Source="[logfile]" />
                <Assign sap2010:WorkflowViewState.IdRef="Assign_8">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[division]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[out1("results")(0)("records")(0)("REPL").ToString().split("~"C)(1).ToString()]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_141">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[company]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[out1("results")(0)("records")(0)("REPL").ToString().split("~"C)(0).ToString()]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_142">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[vendorId]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[out1("results")(0)("records")(0)("REPL").ToString().split("~"C)(2).ToString()]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_235">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[colAmount]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[DictOcrValues("SUBTOTAL").Tostring]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_239">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[discountHandling]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">False</InArgument>
                  </Assign.Value>
                </Assign>
                <If Condition="[poDiscountsHandlingConfig]" sap2010:WorkflowViewState.IdRef="If_88">
                  <If.Then>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_101">
                      <Sequence.Variables>
                        <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="vendorOut" />
                      </Sequence.Variables>
                      <iaw:InvokeWorkflow ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;vendorID&quot;,vendorId},{&quot;businessRulesAPIURL&quot;,TenantID +&quot;IONSERVICES/businessrules/decision/execute/&quot;+businessRule}}]" ContinueOnError="True" DisplayName="Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_2" OutputArguments="[vendorOut]" WorkflowFile="[projectPath + &quot;\vendorIDMatch.xaml&quot;]" />
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_238">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[discountHandling]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[CType(vendorOut("discountHandling"), string)]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <If Condition="[discountHandling = &quot;True&quot;]" sap2010:WorkflowViewState.IdRef="If_94">
                        <If.Then>
                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_102">
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_234">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[colAmount]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[DictOcrValues("TOTAL").Tostring]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <If Condition="[DictOcrValues(&quot;SHIPPING_AND_HANDLING_AMOUNT&quot;).Tostring &lt;&gt; &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_90">
                              <If.Then>
                                <If Condition="[Double.Parse(DictOcrValues(&quot;SHIPPING_AND_HANDLING_AMOUNT&quot;).Tostring)  &lt;&gt; 0]" sap2010:WorkflowViewState.IdRef="If_91">
                                  <If.Then>
                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_236">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="x:String">[colAmount]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="x:String">[(Double.Parse(colAmount) - Double.Parse(DictOcrValues("SHIPPING_AND_HANDLING_AMOUNT").Tostring)).ToString]</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                  </If.Then>
                                </If>
                              </If.Then>
                            </If>
                            <If Condition="[DictOcrValues(&quot;VAT_AMOUNT&quot;).Tostring &lt;&gt; &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_93">
                              <If.Then>
                                <If Condition="[Double.Parse(DictOcrValues(&quot;VAT_AMOUNT&quot;).Tostring)  &lt;&gt; 0]" sap2010:WorkflowViewState.IdRef="If_92">
                                  <If.Then>
                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_237">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="x:String">[colAmount]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="x:String">[(Double.Parse(colAmount) - Double.Parse(DictOcrValues("VAT_AMOUNT").Tostring)).ToString]</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                  </If.Then>
                                </If>
                              </If.Then>
                            </If>
                          </Sequence>
                        </If.Then>
                      </If>
                    </Sequence>
                  </If.Then>
                </If>
                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_56" Line="[&quot;VendorID : &quot; + vendorid]" Source="[logFile]" />
                <If Condition="False" sap2010:WorkflowViewState.IdRef="If_52">
                  <If.Then>
                    <If Condition="[out1(&quot;results&quot;)(0)(&quot;records&quot;)(0)(&quot;REPL&quot;).ToString().split(&quot;~&quot;C)(4).ToString().Length = 2]" sap2010:WorkflowViewState.IdRef="If_51">
                      <If.Then>
                        <Assign DisplayName="Assign tepy" sap2010:WorkflowViewState.IdRef="Assign_166">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[tepy]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">["0" + Convert.ToInt32(out1("results")(0)("records")(0)("REPL").ToString().split("~"C)(4).ToString(),10).ToString]</InArgument>
                          </Assign.Value>
                        </Assign>
                      </If.Then>
                      <If.Else>
                        <Assign DisplayName="Assign tepy" sap2010:WorkflowViewState.IdRef="Assign_167">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[tepy]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[out1("results")(0)("records")(0)("REPL").ToString().split("~"C)(4).ToString()]</InArgument>
                          </Assign.Value>
                        </Assign>
                      </If.Else>
                    </If>
                  </If.Then>
                </If>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_168">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[tepy]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[out1("results")(0)("records")(0)("REPL").ToString().split("~"C)(4).ToString()]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_164">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[pyme]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[out1("results")(0)("records")(0)("REPL").ToString().split("~"C)(5).ToString()]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_163">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[cucd]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[out1("results")(0)("records")(0)("REPL").ToString().split("~"C)(3).ToString()]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_9">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[req2]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">["F2PNLI,F2REPN,F2SCOC,F2RPQA,F2TRDT,F2ITNO,F2CONO,F2CUCD,F2DIVI,F2SUNO,F2IVQA,F2PUNO,F2SUDO,F2RCAC,F2RPQT,F2SCOP,F2SERA,F2IVQT,F2ICAC from FGRECL00 where F2DIVI = " + DIVISION+" AND F2PUNO = " + pono + " and F2IMST != 9 "]</InArgument>
                  </Assign.Value>
                </Assign>
                <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for vendor name" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_17" Response="[respObj3]" StatusCode="[StatusCode3]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/CRS620MI/GetBasicData&quot;]">
                  <iai:IONAPIRequestWizard.Headers>
                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                      <scg:List x:TypeArguments="x:String" Capacity="0" />
                      <scg:List x:TypeArguments="x:String" Capacity="0" />
                    </scg:List>
                  </iai:IONAPIRequestWizard.Headers>
                  <iai:IONAPIRequestWizard.QueryParameters>
                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                      <scg:List x:TypeArguments="x:String" Capacity="8">
                        <x:String>extendedresult</x:String>
                        <x:String>format</x:String>
                        <x:String>righttrim</x:String>
                        <x:String>excludeempty</x:String>
                        <x:String>dateformat</x:String>
                        <x:String>SUNO</x:String>
                      </scg:List>
                      <scg:List x:TypeArguments="x:String" Capacity="8">
                        <x:String>false</x:String>
                        <x:String>PRETTY</x:String>
                        <x:String>true</x:String>
                        <x:String>false</x:String>
                        <x:String>YMD8</x:String>
                        <x:String>vendorId</x:String>
                      </scg:List>
                    </scg:List>
                  </iai:IONAPIRequestWizard.QueryParameters>
                </iai:IONAPIRequestWizard>
                <If Condition="[StatusCode3 = 200]" sap2010:WorkflowViewState.IdRef="If_55">
                  <If.Then>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_67">
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_170">
                        <Assign.To>
                          <OutArgument x:TypeArguments="njl:JToken">[out3]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj3.ReadAsText)]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <If Condition="[out3(&quot;results&quot;)(0)(&quot;records&quot;).ToString &lt;&gt; &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_56">
                        <If.Then>
                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_68">
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_171">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[vendorName]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[out3("results")(0)("records")(0)("SUNM").ToString]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <If Condition="[authUser.ToUpper() = &quot;SUPPLIERAUTHUSER&quot;]" sap2010:WorkflowViewState.IdRef="If_57">
                              <If.Then>
                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_69">
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_172">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[authUser]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">[out3("results")(0)("records")(0)("RESP").ToString]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                </Sequence>
                              </If.Then>
                            </If>
                          </Sequence>
                        </If.Then>
                        <If.Else>
                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_99">
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_227">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">["Vendor name is not obtained for the Supplier Number " + vendorId+"."]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_228">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_55" Line="[commentStatus]" Source="[logfile]" />
                          </Sequence>
                        </If.Else>
                      </If>
                    </Sequence>
                  </If.Then>
                  <If.Else>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_98">
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_225">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">["Received an IONAPI error while fetching Vendor name for the Vendor Id " + vendorId+"."]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_226">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_54" Line="[commentStatus]" Source="[logfile]" />
                    </Sequence>
                  </If.Else>
                </If>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_256">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:Boolean">[allLinesReceived]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_283">
                  <Assign.To>
                    <OutArgument x:TypeArguments="scg:List(x:String)">[notReceivedItems]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="scg:List(x:String)">[New List(Of String)()]</InArgument>
                  </Assign.Value>
                </Assign>
                <If Condition="[matchVendorItemCode]" sap2010:WorkflowViewState.IdRef="If_117">
                  <If.Then>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_124">
                      <iai:IONAPIRequestWizard FileAttachments="{x:Null}" Headers="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_31" Response="[poLinesResponseObject]" Url="[TenantID +&quot;M3/m3api-rest/v2/execute/PPS200MI/LstLine?maxrecs=0&amp;dateformat=YMD8&amp;excludeempty=false&amp;righttrim=true&amp;returncols=PUNO%2CITNO%2CSITE%2CORQA%2CRVQA&amp;format=PRETTY&amp;extendedresult=false&quot;]">
                        <iai:IONAPIRequestWizard.QueryParameters>
                          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                            <scg:List x:TypeArguments="x:String" Capacity="4">
                              <x:String>PUNO</x:String>
                            </scg:List>
                            <scg:List x:TypeArguments="x:String" Capacity="4">
                              <x:String>pono</x:String>
                            </scg:List>
                          </scg:List>
                        </iai:IONAPIRequestWizard.QueryParameters>
                      </iai:IONAPIRequestWizard>
                      <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_7">
                        <TryCatch.Try>
                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_180">
                            <If Condition="[tenantID.contains(&quot;WHEELER&quot;) and (ocrText.tostring.contains(&quot;MANN+HUMMEL Filtration Technology US&quot;) OR ocrText.tostring.contains(&quot;CHAMPION LABORATORIES&quot;) OR OCRText.ToString.Contains(&quot;B.J. Maurer Ford&quot;) OR OCRText.ToString.Contains(&quot;bjmaurerford&quot;) OR OCRText.ToString.Contains(&quot;FREIGHTLINER WESTERN STAR OF NEW STANTON&quot;))]" sap2010:WorkflowViewState.IdRef="If_170">
                              <If.Then>
                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_188">
                                  <Sequence.Variables>
                                    <Variable x:TypeArguments="x:Int32" Name="extractTableResponseCode" />
                                    <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="extractTablesDictionary" />
                                  </Sequence.Variables>
                                  <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;ocrText&quot;,ocrText},{&quot;documentPath&quot;,documentPath}}]" ContinueOnError="True" DisplayName="Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_5" OutputArguments="[extractTablesDictionary]" ResponseCode="[extractTableResponseCode]" WorkflowFile="[projectPath+&quot;\WheelerLines.xaml&quot;]" />
                                  <If Condition="[extractTableResponseCode = 200]" sap2010:WorkflowViewState.IdRef="If_261">
                                    <If.Then>
                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_282">
                                        <Sequence.Variables>
                                          <Variable x:TypeArguments="scg:List(x:String)" Name="ocrTablesItemCodes" />
                                        </Sequence.Variables>
                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_517">
                                          <Assign.To>
                                            <OutArgument x:TypeArguments="scg:List(x:String)">[ocrTablesItemCodes]</OutArgument>
                                          </Assign.To>
                                          <Assign.Value>
                                            <InArgument x:TypeArguments="scg:List(x:String)">[CType(extractTablesDictionary("ocrTablesItemCodes"), List(Of String))]</InArgument>
                                          </Assign.Value>
                                        </Assign>
                                        <ForEach x:TypeArguments="njl:JToken" DisplayName="ForEach&lt;JToken&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_33" Values="[poLinesResponseObject.readasjson(&quot;results&quot;)(0)(&quot;records&quot;)]">
                                          <ActivityAction x:TypeArguments="njl:JToken">
                                            <ActivityAction.Argument>
                                              <DelegateInArgument x:TypeArguments="njl:JToken" Name="item" />
                                            </ActivityAction.Argument>
                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_186">
                                              <If Condition="[ocrTablesItemCodes.Any(Function(s) Regex.Replace(s, &quot;[^a-zA-Z0-9]&quot;, &quot;&quot;).Contains(Regex.Replace(item(&quot;ITNO&quot;).tostring.tolower, &quot;[^a-zA-Z0-9]&quot;, &quot;&quot;))) OR ocrTablesItemCodes.Any(Function(s) Regex.Replace(s, &quot;[^a-zA-Z0-9]&quot;, &quot;&quot;).Contains(Regex.Replace(item(&quot;SITE&quot;).tostring.tolower, &quot;[^a-zA-Z0-9]&quot;, &quot;&quot;)))]" sap2010:WorkflowViewState.IdRef="If_174">
                                                <If.Then>
                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_182">
                                                    <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_13" MethodName="Add">
                                                      <InvokeMethod.TargetObject>
                                                        <InArgument x:TypeArguments="scg:List(x:String)">[invoicesItemNumbers]</InArgument>
                                                      </InvokeMethod.TargetObject>
                                                      <InArgument x:TypeArguments="x:String">[item("ITNO").tostring]</InArgument>
                                                    </InvokeMethod>
                                                    <If Condition="[Integer.Parse(item(&quot;ORQA&quot;).tostring) &gt; Integer.Parse(item(&quot;RVQA&quot;).tostring)]" sap2010:WorkflowViewState.IdRef="If_171">
                                                      <If.Then>
                                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_181">
                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_370">
                                                            <Assign.To>
                                                              <OutArgument x:TypeArguments="x:Boolean">[allLinesReceived]</OutArgument>
                                                            </Assign.To>
                                                            <Assign.Value>
                                                              <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                                                            </Assign.Value>
                                                          </Assign>
                                                          <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_14" MethodName="Add">
                                                            <InvokeMethod.TargetObject>
                                                              <InArgument x:TypeArguments="scg:List(x:String)">[notReceivedItems]</InArgument>
                                                            </InvokeMethod.TargetObject>
                                                            <InArgument x:TypeArguments="x:String">[item("ITNO").tostring]</InArgument>
                                                          </InvokeMethod>
                                                        </Sequence>
                                                      </If.Then>
                                                    </If>
                                                  </Sequence>
                                                </If.Then>
                                              </If>
                                            </Sequence>
                                          </ActivityAction>
                                        </ForEach>
                                      </Sequence>
                                    </If.Then>
                                  </If>
                                </Sequence>
                              </If.Then>
                              <If.Else>
                                <ForEach x:TypeArguments="njl:JToken" DisplayName="ForEach&lt;JToken&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_9" Values="[poLinesResponseObject.readasjson(&quot;results&quot;)(0)(&quot;records&quot;)]">
                                  <ActivityAction x:TypeArguments="njl:JToken">
                                    <ActivityAction.Argument>
                                      <DelegateInArgument x:TypeArguments="njl:JToken" Name="item" />
                                    </ActivityAction.Argument>
                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_103">
                                      <If Condition="[ocrText(&quot;data&quot;).tostring.tolower.contains(&quot; &quot;+item(&quot;ITNO&quot;).tostring.tolower+&quot; &quot;) OR ocrText(&quot;data&quot;).tostring.tolower.contains(&quot; &quot;+item(&quot;SITE&quot;).tostring.tolower+&quot; &quot;)]" sap2010:WorkflowViewState.IdRef="If_96">
                                        <If.Then>
                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_117">
                                            <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_2" MethodName="Add">
                                              <InvokeMethod.TargetObject>
                                                <InArgument x:TypeArguments="scg:List(x:String)">[invoicesItemNumbers]</InArgument>
                                              </InvokeMethod.TargetObject>
                                              <InArgument x:TypeArguments="x:String">[item("ITNO").tostring]</InArgument>
                                            </InvokeMethod>
                                            <If Condition="[Integer.Parse(item(&quot;ORQA&quot;).tostring) &gt; Integer.Parse(item(&quot;RVQA&quot;).tostring)]" sap2010:WorkflowViewState.IdRef="If_110">
                                              <If.Then>
                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_134">
                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_257">
                                                    <Assign.To>
                                                      <OutArgument x:TypeArguments="x:Boolean">[allLinesReceived]</OutArgument>
                                                    </Assign.To>
                                                    <Assign.Value>
                                                      <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                                                    </Assign.Value>
                                                  </Assign>
                                                  <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_8" MethodName="Add">
                                                    <InvokeMethod.TargetObject>
                                                      <InArgument x:TypeArguments="scg:List(x:String)">[notReceivedItems]</InArgument>
                                                    </InvokeMethod.TargetObject>
                                                    <InArgument x:TypeArguments="x:String">[item("ITNO").tostring]</InArgument>
                                                  </InvokeMethod>
                                                </Sequence>
                                              </If.Then>
                                            </If>
                                          </Sequence>
                                        </If.Then>
                                        <If.Else>
                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_108">
                                            <Sequence.Variables>
                                              <Variable x:TypeArguments="x:String" Name="itemNumberWithPrefix" />
                                              <Variable x:TypeArguments="x:String" Name="itemSupNumberWithPrefix" />
                                            </Sequence.Variables>
                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_243">
                                              <Assign.To>
                                                <OutArgument x:TypeArguments="x:String">[itemSupNumberWithPrefix]</OutArgument>
                                              </Assign.To>
                                              <Assign.Value>
                                                <InArgument x:TypeArguments="x:String">[System.Text.RegularExpressions.Regex.Replace(item("SITE").tostring, "(\d+)", " $1 ")]</InArgument>
                                              </Assign.Value>
                                            </Assign>
                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_369">
                                              <Assign.To>
                                                <OutArgument x:TypeArguments="x:String">[itemNumberWithPrefix]</OutArgument>
                                              </Assign.To>
                                              <Assign.Value>
                                                <InArgument x:TypeArguments="x:String">[System.Text.RegularExpressions.Regex.Replace(item("ITNO").tostring, "(\d+)", " $1 ")]</InArgument>
                                              </Assign.Value>
                                            </Assign>
                                            <If Condition="[ocrText(&quot;data&quot;).tostring.tolower.contains(&quot; &quot;+itemNumberWithPrefix.trim.tolower+&quot; &quot;) OR ocrText(&quot;data&quot;).tostring.tolower.contains(&quot; &quot;+itemSupNumberWithPrefix.trim.tolower+&quot; &quot;)]" sap2010:WorkflowViewState.IdRef="If_102">
                                              <If.Then>
                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_120">
                                                  <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_3" MethodName="Add">
                                                    <InvokeMethod.TargetObject>
                                                      <InArgument x:TypeArguments="scg:List(x:String)">[invoicesItemNumbers]</InArgument>
                                                    </InvokeMethod.TargetObject>
                                                    <InArgument x:TypeArguments="x:String">[item("ITNO").tostring]</InArgument>
                                                  </InvokeMethod>
                                                  <If Condition="[Integer.Parse(item(&quot;ORQA&quot;).tostring) &gt; Integer.Parse(item(&quot;RVQA&quot;).tostring)]" sap2010:WorkflowViewState.IdRef="If_111">
                                                    <If.Then>
                                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_135">
                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_258">
                                                          <Assign.To>
                                                            <OutArgument x:TypeArguments="x:Boolean">[allLinesReceived]</OutArgument>
                                                          </Assign.To>
                                                          <Assign.Value>
                                                            <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                                                          </Assign.Value>
                                                        </Assign>
                                                        <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_9" MethodName="Add">
                                                          <InvokeMethod.TargetObject>
                                                            <InArgument x:TypeArguments="scg:List(x:String)">[notReceivedItems]</InArgument>
                                                          </InvokeMethod.TargetObject>
                                                          <InArgument x:TypeArguments="x:String">[item("ITNO").tostring]</InArgument>
                                                        </InvokeMethod>
                                                      </Sequence>
                                                    </If.Then>
                                                  </If>
                                                </Sequence>
                                              </If.Then>
                                            </If>
                                          </Sequence>
                                        </If.Else>
                                      </If>
                                    </Sequence>
                                  </ActivityAction>
                                </ForEach>
                              </If.Else>
                            </If>
                          </Sequence>
                        </TryCatch.Try>
                        <TryCatch.Catches>
                          <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_7">
                            <ActivityAction x:TypeArguments="s:Exception">
                              <ActivityAction.Argument>
                                <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                              </ActivityAction.Argument>
                              <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_65" Line="Error reading item codes" Source="[logfile]" />
                            </ActivityAction>
                          </Catch>
                        </TryCatch.Catches>
                      </TryCatch>
                      <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_63" Line="[&quot;Invoice lines count : &quot;+invoicesItemNumbers.Count.tostring]" Source="[logfile]" />
                    </Sequence>
                  </If.Then>
                </If>
                <iai:IONAPIRequestWizard FileAttachments="{x:Null}" Headers="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="Export MI lines IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_2" Response="[respObj2]" StatusCode="[StatusCode2]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/EXPORTMI/Select?HDRS=0&amp;dateformat=YMD8&amp;excludeempty=false&amp;righttrim=true&amp;format=PRETTY&amp;extendedresult=false&amp;maxrecs=0&quot;]">
                  <iai:IONAPIRequestWizard.QueryParameters>
                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>QERY</x:String>
                        <x:String>SEPC</x:String>
                      </scg:List>
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>req2</x:String>
                        <x:String>~</x:String>
                      </scg:List>
                    </scg:List>
                  </iai:IONAPIRequestWizard.QueryParameters>
                </iai:IONAPIRequestWizard>
                <If Condition="[StatusCode2 = 200]" sap2010:WorkflowViewState.IdRef="If_15">
                  <If.Then>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_23">
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_10">
                        <Assign.To>
                          <OutArgument x:TypeArguments="njl:JToken">[out2]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj2.ReadAsText)]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <If Condition="[out2(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_6">
                        <If.Then>
                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_2">
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_13">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">No items listed in the PO have been received.</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_14">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">PONOTRECEIVED</InArgument>
                              </Assign.Value>
                            </Assign>
                            <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_2" Line="[commentStatus]" Source="[logfile]" />
                          </Sequence>
                        </If.Then>
                        <If.Else>
                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_8">
                            <Sequence.Variables>
                              <Variable x:TypeArguments="x:Int32" Name="x" />
                            </Sequence.Variables>
                            <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_20" Line="[&quot;PO lines for the Purchase order number &quot; + pono + &quot; is extracted.&quot;]" Source="[logfile]" />
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_133">
                              <Assign.To>
                                <OutArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="scg:List(s:String[])">[new list(OF string()) ()]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_16">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:Int32">[x]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:Int32">1</InArgument>
                              </Assign.Value>
                            </Assign>
                            <ForEach x:TypeArguments="njl:JToken" DisplayName="ForEach&lt;JToken&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_3" Values="[out2(&quot;results&quot;)(0)(&quot;records&quot;)]">
                              <ActivityAction x:TypeArguments="njl:JToken">
                                <ActivityAction.Argument>
                                  <DelegateInArgument x:TypeArguments="njl:JToken" Name="item" />
                                </ActivityAction.Argument>
                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_7">
                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_6">
                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_18">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="s:String[]">[M3Values]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="s:String[]">[New String(6) {}]</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_19">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="x:String">[m3Values(3)]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="x:String">[(Convert.toDecimal(item("REPL").ToString().split("~"C)(3).ToString()) - Convert.toDecimal(item("REPL").ToString().split("~"C)(10).ToString())).ToString()]</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                    <If Condition="[CInt(m3Values(3)).ToString &gt; &quot;0&quot;]" sap2010:WorkflowViewState.IdRef="If_4">
                                      <If.Then>
                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_5">
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_20">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[m3Values(0)]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">[item("REPL").ToString().split("~"C)(0).ToString()]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_21">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[m3Values(1)]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">[item("REPL").ToString().split("~"C)(1).ToString()]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_22">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[m3Values(2)]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">[item("REPL").ToString().split("~"C)(2).ToString()]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_23">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[m3Values(4)]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">[item("REPL").ToString().split("~"C)(4).ToString()]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_24">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[m3Values(5)]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">[item("REPL").ToString().split("~"C)(5).ToString()]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_25">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[SupplierNo]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">[item("REPL").ToString().split("~"C)(9).ToString()]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_27">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[cono]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">[item("REPL").ToString().split("~"C)(6).ToString()]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <If Condition="[matchVendorItemCode and invoicesItemNumbers.Count &gt; 0]" sap2010:WorkflowViewState.IdRef="If_107">
                                            <If.Then>
                                              <If Condition="[invoicesItemNumbers.contains(m3Values(5))]" sap2010:WorkflowViewState.IdRef="If_99">
                                                <If.Then>
                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_107">
                                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_106">
                                                      <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_1" MethodName="Add">
                                                        <InvokeMethod.TargetObject>
                                                          <InArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows]</InArgument>
                                                        </InvokeMethod.TargetObject>
                                                        <InArgument x:TypeArguments="s:String[]">[m3Values]</InArgument>
                                                      </InvokeMethod>
                                                    </Sequence>
                                                  </Sequence>
                                                </If.Then>
                                              </If>
                                            </If.Then>
                                            <If.Else>
                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_111">
                                                <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_4" MethodName="Add">
                                                  <InvokeMethod.TargetObject>
                                                    <InArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows]</InArgument>
                                                  </InvokeMethod.TargetObject>
                                                  <InArgument x:TypeArguments="s:String[]">[m3Values]</InArgument>
                                                </InvokeMethod>
                                              </Sequence>
                                            </If.Else>
                                          </If>
                                        </Sequence>
                                      </If.Then>
                                    </If>
                                  </Sequence>
                                </Sequence>
                              </ActivityAction>
                            </ForEach>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_269">
                              <Assign.To>
                                <OutArgument x:TypeArguments="scg:IEnumerable(sl:IGrouping(x:String, s:String[]))">[transDateGroups]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="scg:IEnumerable(sl:IGrouping(x:String, s:String[]))">[M3TotalTableRows.GroupBy(Function(item) item(4))]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <If Condition="[miscValues(&quot;groupByTransDate&quot;).ToString.ToLower = &quot;true&quot;]" sap2010:WorkflowViewState.IdRef="If_269" />
                            <ForEach x:TypeArguments="sl:IGrouping(x:String, s:String[])" DisplayName="ForEach&lt;IGrouping&lt;String,String[]&gt;&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_15" Values="[transDateGroups]">
                              <ActivityAction x:TypeArguments="sl:IGrouping(x:String, s:String[])">
                                <ActivityAction.Argument>
                                  <DelegateInArgument x:TypeArguments="sl:IGrouping(x:String, s:String[])" Name="group" />
                                </ActivityAction.Argument>
                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_125">
                                  <If Condition="[matchVendorItemCode and invoicesItemNumbers.Count &gt; 0]" sap2010:WorkflowViewState.IdRef="If_119">
                                    <If.Then>
                                      <If Condition="[group.ToList().Count &gt;=  invoicesItemNumbers.Count]" sap2010:WorkflowViewState.IdRef="If_118">
                                        <If.Then>
                                          <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_6" MethodName="Add">
                                            <InvokeMethod.TargetObject>
                                              <InArgument x:TypeArguments="scg:Dictionary(x:String, scg:List(s:String[]))">[transDateDictionary]</InArgument>
                                            </InvokeMethod.TargetObject>
                                            <InArgument x:TypeArguments="x:String">[group.Key]</InArgument>
                                            <InArgument x:TypeArguments="scg:List(s:String[])">[group.ToList]</InArgument>
                                          </InvokeMethod>
                                        </If.Then>
                                      </If>
                                    </If.Then>
                                    <If.Else>
                                      <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_7" MethodName="Add">
                                        <InvokeMethod.TargetObject>
                                          <InArgument x:TypeArguments="scg:Dictionary(x:String, scg:List(s:String[]))">[transDateDictionary]</InArgument>
                                        </InvokeMethod.TargetObject>
                                        <InArgument x:TypeArguments="x:String">[group.Key]</InArgument>
                                        <InArgument x:TypeArguments="scg:List(s:String[])">[group.ToList]</InArgument>
                                      </InvokeMethod>
                                    </If.Else>
                                  </If>
                                </Sequence>
                              </ActivityAction>
                            </ForEach>
                            <If Condition="False" sap2010:WorkflowViewState.IdRef="If_262">
                              <If.Then>
                                <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_13">
                                  <TryCatch.Try>
                                    <If Condition="[transDateDictionary.Count =0]" DisplayName="Not Items matching Invoice Items Count" sap2010:WorkflowViewState.IdRef="If_165">
                                      <If.Then>
                                        <ForEach x:TypeArguments="sl:IGrouping(x:String, s:String[])" DisplayName="ForEach&lt;IGrouping&lt;String,String[]&gt;&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_32" Values="[transDateGroups]">
                                          <ActivityAction x:TypeArguments="sl:IGrouping(x:String, s:String[])">
                                            <ActivityAction.Argument>
                                              <DelegateInArgument x:TypeArguments="sl:IGrouping(x:String, s:String[])" Name="group" />
                                            </ActivityAction.Argument>
                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_179">
                                              <If Condition="[matchVendorItemCode and invoicesItemNumbers.Count &gt; 0]" sap2010:WorkflowViewState.IdRef="If_169">
                                                <If.Then>
                                                  <If Condition="[group.ToList().Count &gt;=  invoicesItemNumbers.Count]" sap2010:WorkflowViewState.IdRef="If_168">
                                                    <If.Else>
                                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_178">
                                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_177">
                                                          <Sequence.Variables>
                                                            <Variable x:TypeArguments="x:Decimal" Name="transDateAmount" />
                                                            <Variable x:TypeArguments="x:Decimal" Name="onePercent" />
                                                            <Variable x:TypeArguments="x:Decimal" Name="invoiceAmount" />
                                                          </Sequence.Variables>
                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_363">
                                                            <Assign.To>
                                                              <OutArgument x:TypeArguments="x:Decimal">[transDateAmount]</OutArgument>
                                                            </Assign.To>
                                                            <Assign.Value>
                                                              <InArgument x:TypeArguments="x:Decimal">[0]</InArgument>
                                                            </Assign.Value>
                                                          </Assign>
                                                          <ForEach x:TypeArguments="s:String[]" DisplayName="ForEach&lt;String[]&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_31" Values="[group.ToList]">
                                                            <ActivityAction x:TypeArguments="s:String[]">
                                                              <ActivityAction.Argument>
                                                                <DelegateInArgument x:TypeArguments="s:String[]" Name="item" />
                                                              </ActivityAction.Argument>
                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_364">
                                                                <Assign.To>
                                                                  <OutArgument x:TypeArguments="x:Decimal">[transDateAmount]</OutArgument>
                                                                </Assign.To>
                                                                <Assign.Value>
                                                                  <InArgument x:TypeArguments="x:Decimal" xml:space="preserve">[transDateAmount+( Decimal.Parse(item(2)) *  Decimal.Parse(item(3)))]</InArgument>
                                                                </Assign.Value>
                                                              </Assign>
                                                            </ActivityAction>
                                                          </ForEach>
                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_365">
                                                            <Assign.To>
                                                              <OutArgument x:TypeArguments="x:Decimal">[onePercent]</OutArgument>
                                                            </Assign.To>
                                                            <Assign.Value>
                                                              <InArgument x:TypeArguments="x:Decimal">[transDateAmount * 0.01D]</InArgument>
                                                            </Assign.Value>
                                                          </Assign>
                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_366">
                                                            <Assign.To>
                                                              <OutArgument x:TypeArguments="x:Decimal">[invoiceAmount]</OutArgument>
                                                            </Assign.To>
                                                            <Assign.Value>
                                                              <InArgument x:TypeArguments="x:Decimal">[Decimal.parse(colAmount)]</InArgument>
                                                            </Assign.Value>
                                                          </Assign>
                                                          <If Condition="[onePercent &gt; 10]" sap2010:WorkflowViewState.IdRef="If_166">
                                                            <If.Then>
                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_367">
                                                                <Assign.To>
                                                                  <OutArgument x:TypeArguments="x:Decimal">[onePercent]</OutArgument>
                                                                </Assign.To>
                                                                <Assign.Value>
                                                                  <InArgument x:TypeArguments="x:Decimal">[10]</InArgument>
                                                                </Assign.Value>
                                                              </Assign>
                                                            </If.Then>
                                                          </If>
                                                          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_100" Line="[&quot;Trans Date :&quot;+group.Key+&quot; Trans Date amount &quot;+transDateAmount.tostring+&quot; Sub Total&quot;+colAmount]" Source="[logFile]" />
                                                          <If Condition="[invoiceAmount &gt; transDateAmount - onePercent and  invoiceAmount &lt; transDateAmount + onePercent]" sap2010:WorkflowViewState.IdRef="If_167">
                                                            <If.Then>
                                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_176">
                                                                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_101" Line="Partial Invoices Line amount matching with PO received line amount" Source="[logFile]" />
                                                                <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_12" MethodName="Add">
                                                                  <InvokeMethod.TargetObject>
                                                                    <InArgument x:TypeArguments="scg:Dictionary(x:String, scg:List(s:String[]))">[transDateDictionary]</InArgument>
                                                                  </InvokeMethod.TargetObject>
                                                                  <InArgument x:TypeArguments="x:String">[group.Key]</InArgument>
                                                                  <InArgument x:TypeArguments="scg:List(s:String[])">[group.ToList]</InArgument>
                                                                </InvokeMethod>
                                                              </Sequence>
                                                            </If.Then>
                                                          </If>
                                                        </Sequence>
                                                      </Sequence>
                                                    </If.Else>
                                                  </If>
                                                </If.Then>
                                              </If>
                                            </Sequence>
                                          </ActivityAction>
                                        </ForEach>
                                      </If.Then>
                                    </If>
                                  </TryCatch.Try>
                                  <TryCatch.Catches>
                                    <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_13">
                                      <ActivityAction x:TypeArguments="s:Exception">
                                        <ActivityAction.Argument>
                                          <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                                        </ActivityAction.Argument>
                                      </ActivityAction>
                                    </Catch>
                                  </TryCatch.Catches>
                                </TryCatch>
                              </If.Then>
                            </If>
                            <If Condition="[transDateDictionary.Count =0]" sap2010:WorkflowViewState.IdRef="If_128">
                              <If.Then>
                                <If Condition="[matchVendorItemCode and invoicesItemNumbers.Count &gt; 0]" sap2010:WorkflowViewState.IdRef="If_106">
                                  <If.Then>
                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_130">
                                      <Sequence.Variables>
                                        <Variable x:TypeArguments="x:Int32" Name="count" />
                                      </Sequence.Variables>
                                      <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_64" Line="[&quot;Invoice lines count matches with PO Receipt not invoiced  : &quot;+M3TotalTableRows .Count.tostring]" Source="[logfile]" />
                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_284">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:Int32">[count]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:Int32">1</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                      <ForEach x:TypeArguments="x:String" DisplayName="ForEach&lt;String&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_10" Values="[invoicesItemNumbers]">
                                        <ActivityAction x:TypeArguments="x:String">
                                          <ActivityAction.Argument>
                                            <DelegateInArgument x:TypeArguments="x:String" Name="itemCode" />
                                          </ActivityAction.Argument>
                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_105">
                                            <Sequence.Variables>
                                              <Variable x:TypeArguments="x:Boolean" Name="itemCodeAvailable" />
                                              <Variable x:TypeArguments="x:String" Name="receivedDate" />
                                            </Sequence.Variables>
                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_241">
                                              <Assign.To>
                                                <OutArgument x:TypeArguments="x:Boolean">[itemCodeAvailable]</OutArgument>
                                              </Assign.To>
                                              <Assign.Value>
                                                <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                                              </Assign.Value>
                                            </Assign>
                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_280">
                                              <Assign.To>
                                                <OutArgument x:TypeArguments="x:String">[receivedDate]</OutArgument>
                                              </Assign.To>
                                              <Assign.Value>
                                                <InArgument x:TypeArguments="x:String">
                                                  <Literal x:TypeArguments="x:String" Value="" />
                                                </InArgument>
                                              </Assign.Value>
                                            </Assign>
                                            <ForEach x:TypeArguments="s:String[]" DisplayName="ForEach&lt;String[]&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_26" Values="[M3TotalTableRows]">
                                              <ActivityAction x:TypeArguments="s:String[]">
                                                <ActivityAction.Argument>
                                                  <DelegateInArgument x:TypeArguments="s:String[]" Name="recItem" />
                                                </ActivityAction.Argument>
                                                <If Condition="[recItem(5) = itemCode]" sap2010:WorkflowViewState.IdRef="If_98">
                                                  <If.Then>
                                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_131">
                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_242">
                                                        <Assign.To>
                                                          <OutArgument x:TypeArguments="x:Boolean">[itemCodeAvailable]</OutArgument>
                                                        </Assign.To>
                                                        <Assign.Value>
                                                          <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                                                        </Assign.Value>
                                                      </Assign>
                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_281">
                                                        <Assign.To>
                                                          <OutArgument x:TypeArguments="x:String">[receivedDate]</OutArgument>
                                                        </Assign.To>
                                                        <Assign.Value>
                                                          <InArgument x:TypeArguments="x:String">[recItem(4)]</InArgument>
                                                        </Assign.Value>
                                                      </Assign>
                                                      <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_72" Line="[Count.tostring+&quot;)&quot;+itemCode+&quot; is available in PO and Invoice. Received Date : &quot;+receivedDate]" Source="[logFile]" />
                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_285">
                                                        <Assign.To>
                                                          <OutArgument x:TypeArguments="x:Int32">[count]</OutArgument>
                                                        </Assign.To>
                                                        <Assign.Value>
                                                          <InArgument x:TypeArguments="x:Int32">[count+1]</InArgument>
                                                        </Assign.Value>
                                                      </Assign>
                                                    </Sequence>
                                                  </If.Then>
                                                </If>
                                              </ActivityAction>
                                            </ForEach>
                                            <If Condition="[not itemCodeAvailable]" sap2010:WorkflowViewState.IdRef="If_101">
                                              <If.Then>
                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_136">
                                                  <Sequence.Variables>
                                                    <Variable x:TypeArguments="x:String" Name="variable2" />
                                                  </Sequence.Variables>
                                                  <If Condition="[notReceivedItems.Contains(itemCode)]" sap2010:WorkflowViewState.IdRef="If_125">
                                                    <If.Then>
                                                      <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_73" Line="[Count.tostring+&quot;)&quot;+itemCode+&quot; is not received&quot;]" Source="[logFile]" />
                                                    </If.Then>
                                                    <If.Else>
                                                      <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_74" Line="[Count.tostring+&quot;)&quot;+itemCode+&quot; is already invoiced&quot;]" Source="[logFile]" />
                                                    </If.Else>
                                                  </If>
                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_286">
                                                    <Assign.To>
                                                      <OutArgument x:TypeArguments="x:Int32">[count]</OutArgument>
                                                    </Assign.To>
                                                    <Assign.Value>
                                                      <InArgument x:TypeArguments="x:Int32">[count+1]</InArgument>
                                                    </Assign.Value>
                                                  </Assign>
                                                </Sequence>
                                              </If.Then>
                                            </If>
                                          </Sequence>
                                        </ActivityAction>
                                      </ForEach>
                                    </Sequence>
                                  </If.Then>
                                </If>
                              </If.Then>
                            </If>
                            <If Condition="[M3TotalTableRows.Count &gt; 0]" sap2010:WorkflowViewState.IdRef="If_108">
                              <If.Then>
                                <If Condition="[transDateDictionary.Count&gt;0]" sap2010:WorkflowViewState.IdRef="If_10">
                                  <If.Then>
                                    <Sequence DisplayName="Trans Date For Loop" sap2010:WorkflowViewState.IdRef="Sequence_126">
                                      <Sequence.Variables>
                                        <Variable x:TypeArguments="x:Boolean" Name="optimizerSuccess" />
                                      </Sequence.Variables>
                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_272">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:Boolean">[optimizerSuccess]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                      <ForEach x:TypeArguments="scg:KeyValuePair(x:String, scg:List(s:String[]))" DisplayName="ForEach&lt;KeyValuePair&lt;String,List&lt;String[]&gt;&gt;&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_17" Values="[transDateDictionary]">
                                        <ActivityAction x:TypeArguments="scg:KeyValuePair(x:String, scg:List(s:String[]))">
                                          <ActivityAction.Argument>
                                            <DelegateInArgument x:TypeArguments="scg:KeyValuePair(x:String, scg:List(s:String[]))" Name="transDateItem" />
                                          </ActivityAction.Argument>
                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_127">
                                            <If Condition="[not optimizerSuccess]" sap2010:WorkflowViewState.IdRef="If_120">
                                              <If.Then>
                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_18">
                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_278">
                                                    <Assign.To>
                                                      <OutArgument x:TypeArguments="x:String">[APIString]</OutArgument>
                                                    </Assign.To>
                                                    <Assign.Value>
                                                      <InArgument x:TypeArguments="x:String">
                                                        <Literal x:TypeArguments="x:String" Value="" />
                                                      </InArgument>
                                                    </Assign.Value>
                                                  </Assign>
                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_273">
                                                    <Assign.To>
                                                      <OutArgument x:TypeArguments="x:Int32">[x]</OutArgument>
                                                    </Assign.To>
                                                    <Assign.Value>
                                                      <InArgument x:TypeArguments="x:Int32">1</InArgument>
                                                    </Assign.Value>
                                                  </Assign>
                                                  <ForEach x:TypeArguments="s:String[]" DisplayName="ForEach&lt;String[]&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_19" Values="[transDateItem.Value]">
                                                    <ActivityAction x:TypeArguments="s:String[]">
                                                      <ActivityAction.Argument>
                                                        <DelegateInArgument x:TypeArguments="s:String[]" Name="m3Values" />
                                                      </ActivityAction.Argument>
                                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_133">
                                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_132">
                                                          <If Condition="[m3Values(3).Contains(&quot;.&quot;)]" sap2010:WorkflowViewState.IdRef="If_121">
                                                            <If.Then>
                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_274">
                                                                <Assign.To>
                                                                  <OutArgument x:TypeArguments="x:String">[m3Values(3)]</OutArgument>
                                                                </Assign.To>
                                                                <Assign.Value>
                                                                  <InArgument x:TypeArguments="x:String">[m3Values(3).SubString(0,m3Values(3).IndexOf("."))]</InArgument>
                                                                </Assign.Value>
                                                              </Assign>
                                                            </If.Then>
                                                          </If>
                                                          <If Condition="[x=1]" sap2010:WorkflowViewState.IdRef="If_123">
                                                            <If.Then>
                                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_128">
                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_275">
                                                                  <Assign.To>
                                                                    <OutArgument x:TypeArguments="x:String">[APIString]</OutArgument>
                                                                  </Assign.To>
                                                                  <Assign.Value>
                                                                    <InArgument x:TypeArguments="x:String">["[["+m3Values(0)+","+m3Values(1)+","+m3Values(2)+","+m3Values(3)+","+colAmount+",1]"]</InArgument>
                                                                  </Assign.Value>
                                                                </Assign>
                                                              </Sequence>
                                                            </If.Then>
                                                            <If.Else>
                                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_129">
                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_277">
                                                                  <Assign.To>
                                                                    <OutArgument x:TypeArguments="x:String">[APIString]</OutArgument>
                                                                  </Assign.To>
                                                                  <Assign.Value>
                                                                    <InArgument x:TypeArguments="x:String">[APIString+",["+m3Values(0)+","+m3Values(1)+","+m3Values(2)+","+m3Values(3)+","+colAmount+",1]"]</InArgument>
                                                                  </Assign.Value>
                                                                </Assign>
                                                              </Sequence>
                                                            </If.Else>
                                                          </If>
                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_282">
                                                            <Assign.To>
                                                              <OutArgument x:TypeArguments="x:Int32">[x]</OutArgument>
                                                            </Assign.To>
                                                            <Assign.Value>
                                                              <InArgument x:TypeArguments="x:Int32">[x+1]</InArgument>
                                                            </Assign.Value>
                                                          </Assign>
                                                        </Sequence>
                                                      </Sequence>
                                                    </ActivityAction>
                                                  </ForEach>
                                                  <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;DictOcrValues&quot;,DictOcrValues},{&quot;colemanAPI&quot;,colemanAPI},{&quot;APIString&quot;,APIString},{&quot;logfile&quot;,logfile}}]" ContinueOnError="True" DisplayName="Coleman Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_1" OutputArguments="[ColemanOutPut]" ResponseCode="[ColemanRespCode]" WorkflowFile="[projectPath+&quot;\CallColeman.xaml&quot;]" />
                                                  <If Condition="[ColemanRespCode = 200]" sap2010:WorkflowViewState.IdRef="If_12">
                                                    <If.Then>
                                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_19">
                                                        <Sequence.Variables>
                                                          <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="sendApprovalResponseDictionary" />
                                                          <Variable x:TypeArguments="x:String" Name="vat" />
                                                        </Sequence.Variables>
                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_53">
                                                          <Assign.To>
                                                            <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                          </Assign.To>
                                                          <Assign.Value>
                                                            <InArgument x:TypeArguments="x:String">[ColemanOutPut("comments").ToString()]</InArgument>
                                                          </Assign.Value>
                                                        </Assign>
                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_54">
                                                          <Assign.To>
                                                            <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                          </Assign.To>
                                                          <Assign.Value>
                                                            <InArgument x:TypeArguments="x:String">[ColemanOutPut("Status").ToString()]</InArgument>
                                                          </Assign.Value>
                                                        </Assign>
                                                        <If Condition="[Status = &quot;Success&quot;]" sap2010:WorkflowViewState.IdRef="If_130">
                                                          <If.Then>
                                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_141">
                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_52">
                                                                <Assign.To>
                                                                  <OutArgument x:TypeArguments="njl:JToken">[httpOut]</OutArgument>
                                                                </Assign.To>
                                                                <Assign.Value>
                                                                  <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(ColemanOutPut("httpOut").ToString)]</InArgument>
                                                                </Assign.Value>
                                                              </Assign>
                                                              <If Condition="[NOT httpOut.ToString.Contains(&quot;Not Allocated&quot;)]" sap2010:WorkflowViewState.IdRef="If_162">
                                                                <If.Then>
                                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_171">
                                                                    <Sequence.Variables>
                                                                      <Variable x:TypeArguments="njl:JToken" Name="out5" />
                                                                    </Sequence.Variables>
                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_296">
                                                                      <Assign.To>
                                                                        <OutArgument x:TypeArguments="x:Boolean">[optimizerSuccess]</OutArgument>
                                                                      </Assign.To>
                                                                      <Assign.Value>
                                                                        <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                                                                      </Assign.Value>
                                                                    </Assign>
                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_297">
                                                                      <Assign.To>
                                                                        <OutArgument x:TypeArguments="x:String">[inbnValue]</OutArgument>
                                                                      </Assign.To>
                                                                      <Assign.Value>
                                                                        <InArgument x:TypeArguments="x:String">
                                                                          <Literal x:TypeArguments="x:String" Value="" />
                                                                        </InArgument>
                                                                      </Assign.Value>
                                                                    </Assign>
                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_298">
                                                                      <Assign.To>
                                                                        <OutArgument x:TypeArguments="x:String">[vat]</OutArgument>
                                                                      </Assign.To>
                                                                      <Assign.Value>
                                                                        <InArgument x:TypeArguments="x:String">
                                                                          <Literal x:TypeArguments="x:String" Value="" />
                                                                        </InArgument>
                                                                      </Assign.Value>
                                                                    </Assign>
                                                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_170">
                                                                      <Sequence.Variables>
                                                                        <Variable x:TypeArguments="x:String" Name="SupAcho" />
                                                                        <Variable x:TypeArguments="x:Int32" Name="StatusCode4" />
                                                                        <Variable x:TypeArguments="iru:ResponseObject" Name="respObj4" />
                                                                        <Variable x:TypeArguments="njl:JToken" Name="out4" />
                                                                        <Variable x:TypeArguments="x:String" Name="ivdate" />
                                                                        <Variable x:TypeArguments="x:String" Name="sino" />
                                                                        <Variable x:TypeArguments="x:String" Name="cuam" />
                                                                        <Variable x:TypeArguments="x:Int32" Name="StatusCode5" />
                                                                      </Sequence.Variables>
                                                                      <Assign DisplayName="Assign SupAcho" sap2010:WorkflowViewState.IdRef="Assign_299">
                                                                        <Assign.To>
                                                                          <OutArgument x:TypeArguments="x:String">[SupAcho]</OutArgument>
                                                                        </Assign.To>
                                                                        <Assign.Value>
                                                                          <InArgument x:TypeArguments="x:String">["ACHO:"+supplierNo]</InArgument>
                                                                        </Assign.Value>
                                                                      </Assign>
                                                                      <iai:IONAPIRequestWizard FileAttachments="{x:Null}" Headers="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for bkid" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_32" Response="[respObj4]" StatusCode="[StatusCode4]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/CRS692MI/SearchBnkAcc&quot;]">
                                                                        <iai:IONAPIRequestWizard.QueryParameters>
                                                                          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                            <scg:List x:TypeArguments="x:String" Capacity="8">
                                                                              <x:String>SQRY</x:String>
                                                                              <x:String>dateformat</x:String>
                                                                              <x:String>excludeempty</x:String>
                                                                              <x:String>righttrim</x:String>
                                                                              <x:String>format</x:String>
                                                                              <x:String>extendedresult</x:String>
                                                                            </scg:List>
                                                                            <scg:List x:TypeArguments="x:String" Capacity="8">
                                                                              <x:String>SupAcho</x:String>
                                                                              <x:String>YMD8</x:String>
                                                                              <x:String>false</x:String>
                                                                              <x:String>true</x:String>
                                                                              <x:String>PRETTY</x:String>
                                                                              <x:String>false</x:String>
                                                                            </scg:List>
                                                                          </scg:List>
                                                                        </iai:IONAPIRequestWizard.QueryParameters>
                                                                      </iai:IONAPIRequestWizard>
                                                                      <If Condition="[StatusCode4 = 200]" sap2010:WorkflowViewState.IdRef="If_161">
                                                                        <If.Then>
                                                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_168">
                                                                            <Sequence.Variables>
                                                                              <Variable x:TypeArguments="x:String" Name="bkid" />
                                                                              <Variable x:TypeArguments="iru:ResponseObject" Name="respObj5" />
                                                                            </Sequence.Variables>
                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_300">
                                                                              <Assign.To>
                                                                                <OutArgument x:TypeArguments="njl:JToken">[out4]</OutArgument>
                                                                              </Assign.To>
                                                                              <Assign.Value>
                                                                                <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj4.ReadAsText)]</InArgument>
                                                                              </Assign.Value>
                                                                            </Assign>
                                                                            <If Condition="[out4(&quot;results&quot;)(0)(&quot;records&quot;).ToString &lt;&gt; &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_131">
                                                                              <If.Then>
                                                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_142">
                                                                                  <Assign DisplayName="Assign bkid" sap2010:WorkflowViewState.IdRef="Assign_301">
                                                                                    <Assign.To>
                                                                                      <OutArgument x:TypeArguments="x:String">[bkid]</OutArgument>
                                                                                    </Assign.To>
                                                                                    <Assign.Value>
                                                                                      <InArgument x:TypeArguments="x:String">[(out4("results")(0)("records")(0)("BKID")).ToString]</InArgument>
                                                                                    </Assign.Value>
                                                                                  </Assign>
                                                                                </Sequence>
                                                                              </If.Then>
                                                                              <If.Else>
                                                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_143">
                                                                                  <Assign DisplayName="Assign bkid" sap2010:WorkflowViewState.IdRef="Assign_302">
                                                                                    <Assign.To>
                                                                                      <OutArgument x:TypeArguments="x:String">[bkid]</OutArgument>
                                                                                    </Assign.To>
                                                                                    <Assign.Value>
                                                                                      <InArgument x:TypeArguments="x:String">
                                                                                        <Literal x:TypeArguments="x:String" Value="" />
                                                                                      </InArgument>
                                                                                    </Assign.Value>
                                                                                  </Assign>
                                                                                </Sequence>
                                                                              </If.Else>
                                                                            </If>
                                                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_167">
                                                                              <Sequence.Variables>
                                                                                <Variable x:TypeArguments="x:Boolean" Name="InvoiceAlreadyExists" />
                                                                              </Sequence.Variables>
                                                                              <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_14">
                                                                                <TryCatch.Try>
                                                                                  <Assign DisplayName="Assign ivdate" sap2010:WorkflowViewState.IdRef="Assign_303">
                                                                                    <Assign.To>
                                                                                      <OutArgument x:TypeArguments="x:String">[ivdate]</OutArgument>
                                                                                    </Assign.To>
                                                                                    <Assign.Value>
                                                                                      <InArgument x:TypeArguments="x:String">[DateTime.ParseExact(DictOcrValues("INVOICE_RECEIPT_DATE").Tostring, "dd/MM/yyyy", Nothing).ToString("yyyyMMdd")]</InArgument>
                                                                                    </Assign.Value>
                                                                                  </Assign>
                                                                                </TryCatch.Try>
                                                                                <TryCatch.Catches>
                                                                                  <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_14">
                                                                                    <ActivityAction x:TypeArguments="s:Exception">
                                                                                      <ActivityAction.Argument>
                                                                                        <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                                                                                      </ActivityAction.Argument>
                                                                                    </ActivityAction>
                                                                                  </Catch>
                                                                                </TryCatch.Catches>
                                                                              </TryCatch>
                                                                              <Assign DisplayName="Assign sino" sap2010:WorkflowViewState.IdRef="Assign_304">
                                                                                <Assign.To>
                                                                                  <OutArgument x:TypeArguments="x:String">[sino]</OutArgument>
                                                                                </Assign.To>
                                                                                <Assign.Value>
                                                                                  <InArgument x:TypeArguments="x:String">[DictOcrValues("INVOICE_RECEIPT_ID").Tostring]</InArgument>
                                                                                </Assign.Value>
                                                                              </Assign>
                                                                              <Assign DisplayName="Assign cuam" sap2010:WorkflowViewState.IdRef="Assign_305">
                                                                                <Assign.To>
                                                                                  <OutArgument x:TypeArguments="x:String">[cuam]</OutArgument>
                                                                                </Assign.To>
                                                                                <Assign.Value>
                                                                                  <InArgument x:TypeArguments="x:String">[DictOcrValues("TOTAL").Tostring]</InArgument>
                                                                                </Assign.Value>
                                                                              </Assign>
                                                                              <Assign DisplayName="Assign cuam" sap2010:WorkflowViewState.IdRef="Assign_306">
                                                                                <Assign.To>
                                                                                  <OutArgument x:TypeArguments="x:String">[cuam]</OutArgument>
                                                                                </Assign.To>
                                                                                <Assign.Value>
                                                                                  <InArgument x:TypeArguments="x:String">[cuam.Replace(",","")]</InArgument>
                                                                                </Assign.Value>
                                                                              </Assign>
                                                                              <iai:IONAPIRequestWizard FileAttachments="{x:Null}" Headers="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Invoice header" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_33" Response="[respObj5]" StatusCode="[StatusCode5]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/AddHead&quot;]">
                                                                                <iai:IONAPIRequestWizard.QueryParameters>
                                                                                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                    <scg:List x:TypeArguments="x:String" Capacity="16">
                                                                                      <x:String>SUNO</x:String>
                                                                                      <x:String>IVDT</x:String>
                                                                                      <x:String>DIVI</x:String>
                                                                                      <x:String>SINO</x:String>
                                                                                      <x:String>CUCD</x:String>
                                                                                      <x:String>TEPY</x:String>
                                                                                      <x:String>PYME</x:String>
                                                                                      <x:String>CUAM</x:String>
                                                                                      <x:String>IMCD</x:String>
                                                                                      <x:String>CRTP</x:String>
                                                                                      <x:String>dateformat</x:String>
                                                                                      <x:String>excludeempty</x:String>
                                                                                      <x:String>righttrim</x:String>
                                                                                      <x:String>format</x:String>
                                                                                      <x:String>extendedresult</x:String>
                                                                                      <x:String>APCD</x:String>
                                                                                    </scg:List>
                                                                                    <scg:List x:TypeArguments="x:String" Capacity="16">
                                                                                      <x:String>SupplierNo</x:String>
                                                                                      <x:String>ivdate</x:String>
                                                                                      <x:String>division</x:String>
                                                                                      <x:String>sino</x:String>
                                                                                      <x:String>cucd</x:String>
                                                                                      <x:String>tepy</x:String>
                                                                                      <x:String>pyme</x:String>
                                                                                      <x:String>cuam</x:String>
                                                                                      <x:String>1</x:String>
                                                                                      <x:String>1</x:String>
                                                                                      <x:String>YMD8</x:String>
                                                                                      <x:String>false</x:String>
                                                                                      <x:String>true</x:String>
                                                                                      <x:String>PRETTY</x:String>
                                                                                      <x:String>false</x:String>
                                                                                      <x:String>authUser</x:String>
                                                                                    </scg:List>
                                                                                  </scg:List>
                                                                                </iai:IONAPIRequestWizard.QueryParameters>
                                                                              </iai:IONAPIRequestWizard>
                                                                              <If Condition="[StatusCode5 = 200  and respObj5.ReadAsText.Contains(&quot;Bank account identity must be entered&quot;)]" sap2010:WorkflowViewState.IdRef="If_132">
                                                                                <If.Then>
                                                                                  <iai:IONAPIRequestWizard FileAttachments="{x:Null}" Headers="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Invoice header" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_34" Response="[respObj5]" StatusCode="[StatusCode5]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/AddHead&quot;]">
                                                                                    <iai:IONAPIRequestWizard.QueryParameters>
                                                                                      <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                        <scg:List x:TypeArguments="x:String" Capacity="32">
                                                                                          <x:String>SUNO</x:String>
                                                                                          <x:String>IVDT</x:String>
                                                                                          <x:String>DIVI</x:String>
                                                                                          <x:String>SINO</x:String>
                                                                                          <x:String>CUCD</x:String>
                                                                                          <x:String>TEPY</x:String>
                                                                                          <x:String>PYME</x:String>
                                                                                          <x:String>CUAM</x:String>
                                                                                          <x:String>IMCD</x:String>
                                                                                          <x:String>CRTP</x:String>
                                                                                          <x:String>dateformat</x:String>
                                                                                          <x:String>excludeempty</x:String>
                                                                                          <x:String>righttrim</x:String>
                                                                                          <x:String>format</x:String>
                                                                                          <x:String>extendedresult</x:String>
                                                                                          <x:String>APCD</x:String>
                                                                                          <x:String>BKID</x:String>
                                                                                        </scg:List>
                                                                                        <scg:List x:TypeArguments="x:String" Capacity="32">
                                                                                          <x:String>SupplierNo</x:String>
                                                                                          <x:String>ivdate</x:String>
                                                                                          <x:String>division</x:String>
                                                                                          <x:String>sino</x:String>
                                                                                          <x:String>cucd</x:String>
                                                                                          <x:String>tepy</x:String>
                                                                                          <x:String>pyme</x:String>
                                                                                          <x:String>cuam</x:String>
                                                                                          <x:String>1</x:String>
                                                                                          <x:String>1</x:String>
                                                                                          <x:String>YMD8</x:String>
                                                                                          <x:String>false</x:String>
                                                                                          <x:String>true</x:String>
                                                                                          <x:String>PRETTY</x:String>
                                                                                          <x:String>false</x:String>
                                                                                          <x:String>authUser</x:String>
                                                                                          <x:String>bkid</x:String>
                                                                                        </scg:List>
                                                                                      </scg:List>
                                                                                    </iai:IONAPIRequestWizard.QueryParameters>
                                                                                  </iai:IONAPIRequestWizard>
                                                                                </If.Then>
                                                                              </If>
                                                                              <If Condition="[StatusCode5 = 200]" sap2010:WorkflowViewState.IdRef="If_160">
                                                                                <If.Then>
                                                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_165">
                                                                                    <Sequence.Variables>
                                                                                      <Variable x:TypeArguments="x:Int32" Name="respout1" />
                                                                                      <Variable x:TypeArguments="x:String" Name="additionalChargeExcp" />
                                                                                      <Variable x:TypeArguments="x:Int32" Name="m" />
                                                                                    </Sequence.Variables>
                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_307">
                                                                                      <Assign.To>
                                                                                        <OutArgument x:TypeArguments="njl:JToken">[out5]</OutArgument>
                                                                                      </Assign.To>
                                                                                      <Assign.Value>
                                                                                        <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj5.ReadAsText)]</InArgument>
                                                                                      </Assign.Value>
                                                                                    </Assign>
                                                                                    <If Condition="[out5(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_133">
                                                                                      <If.Then>
                                                                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_144">
                                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_308">
                                                                                            <Assign.To>
                                                                                              <OutArgument x:TypeArguments="x:Boolean">[InvoiceAlreadyExists]</OutArgument>
                                                                                            </Assign.To>
                                                                                            <Assign.Value>
                                                                                              <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                                                                                            </Assign.Value>
                                                                                          </Assign>
                                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_309">
                                                                                            <Assign.To>
                                                                                              <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                            </Assign.To>
                                                                                            <Assign.Value>
                                                                                              <InArgument x:TypeArguments="x:String">[(out5("results")(0)("errorMessage")).ToString]</InArgument>
                                                                                            </Assign.Value>
                                                                                          </Assign>
                                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_310">
                                                                                            <Assign.To>
                                                                                              <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                                            </Assign.To>
                                                                                            <Assign.Value>
                                                                                              <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                                                                                            </Assign.Value>
                                                                                          </Assign>
                                                                                        </Sequence>
                                                                                      </If.Then>
                                                                                      <If.Else>
                                                                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_145">
                                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_311">
                                                                                            <Assign.To>
                                                                                              <OutArgument x:TypeArguments="x:Boolean">[InvoiceAlreadyExists]</OutArgument>
                                                                                            </Assign.To>
                                                                                            <Assign.Value>
                                                                                              <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                                                                                            </Assign.Value>
                                                                                          </Assign>
                                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_312">
                                                                                            <Assign.To>
                                                                                              <OutArgument x:TypeArguments="x:String">[inbnValue]</OutArgument>
                                                                                            </Assign.To>
                                                                                            <Assign.Value>
                                                                                              <InArgument x:TypeArguments="x:String">[(out5("results")(0)("records")(0)("INBN")).ToString]</InArgument>
                                                                                            </Assign.Value>
                                                                                          </Assign>
                                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_313">
                                                                                            <Assign.To>
                                                                                              <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                            </Assign.To>
                                                                                            <Assign.Value>
                                                                                              <InArgument x:TypeArguments="x:String">["Invoice Header Created"]</InArgument>
                                                                                            </Assign.Value>
                                                                                          </Assign>
                                                                                        </Sequence>
                                                                                      </If.Else>
                                                                                    </If>
                                                                                    <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_79" Line="[commentStatus]" Source="[logfile]" />
                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_314">
                                                                                      <Assign.To>
                                                                                        <OutArgument x:TypeArguments="x:Int32">[m]</OutArgument>
                                                                                      </Assign.To>
                                                                                      <Assign.Value>
                                                                                        <InArgument x:TypeArguments="x:Int32">0</InArgument>
                                                                                      </Assign.Value>
                                                                                    </Assign>
                                                                                    <If Condition="[NOT InvoiceAlreadyExists]" sap2010:WorkflowViewState.IdRef="If_155">
                                                                                      <If.Then>
                                                                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_162">
                                                                                          <Sequence.Variables>
                                                                                            <Variable x:TypeArguments="njl:JToken" Name="out5" />
                                                                                            <Variable x:TypeArguments="x:Int32" Name="respout" />
                                                                                            <Variable x:TypeArguments="scg:List(x:String)" Name="vatList" />
                                                                                            <Variable x:TypeArguments="x:String" Name="TaxCode" />
                                                                                            <Variable x:TypeArguments="x:String" Name="charge" />
                                                                                            <Variable x:TypeArguments="x:String" Name="discount" />
                                                                                            <Variable x:TypeArguments="x:String" Name="vatCode" />
                                                                                          </Sequence.Variables>
                                                                                          <ForEach x:TypeArguments="njl:JToken" DisplayName="ForEach&lt;JToken&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_28" Values="[httpOut]">
                                                                                            <ActivityAction x:TypeArguments="njl:JToken">
                                                                                              <ActivityAction.Argument>
                                                                                                <DelegateInArgument x:TypeArguments="njl:JToken" Name="out" />
                                                                                              </ActivityAction.Argument>
                                                                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_152">
                                                                                                <ForEach x:TypeArguments="s:String[]" DisplayName="ForEach&lt;String[]&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_27" Values="[transDateItem.Value]">
                                                                                                  <ActivityAction x:TypeArguments="s:String[]">
                                                                                                    <ActivityAction.Argument>
                                                                                                      <DelegateInArgument x:TypeArguments="s:String[]" Name="rows" />
                                                                                                    </ActivityAction.Argument>
                                                                                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_151">
                                                                                                      <If Condition="[out(&quot;line_number&quot;).ToString=rows(0) And out(&quot;rcd_number&quot;).ToString=rows(1)]" sap2010:WorkflowViewState.IdRef="If_139">
                                                                                                        <If.Then>
                                                                                                          <Sequence DisplayName="Add lines Sequence" sap2010:WorkflowViewState.IdRef="Sequence_150">
                                                                                                            <Sequence.Variables>
                                                                                                              <Variable x:TypeArguments="x:String" Name="puno" />
                                                                                                              <Variable x:TypeArguments="x:String" Name="inbn" />
                                                                                                              <Variable x:TypeArguments="x:String" Name="itno" />
                                                                                                              <Variable x:TypeArguments="x:String" Name="ppun" />
                                                                                                              <Variable x:TypeArguments="x:String" Name="puun" />
                                                                                                              <Variable x:TypeArguments="njl:JToken" Name="out4" />
                                                                                                              <Variable x:TypeArguments="njl:JToken" Name="out3" />
                                                                                                              <Variable x:TypeArguments="x:String" Name="repn" />
                                                                                                              <Variable x:TypeArguments="x:String" Name="pnli" />
                                                                                                              <Variable x:TypeArguments="x:String" Name="grpr" />
                                                                                                              <Variable x:TypeArguments="njl:JToken" Name="out7" />
                                                                                                              <Variable x:TypeArguments="iru:ResponseObject" Name="respObj7" />
                                                                                                              <Variable x:TypeArguments="iru:ResponseObject" Name="respObj4" />
                                                                                                              <Variable x:TypeArguments="x:String" Name="ivqa" />
                                                                                                              <Variable x:TypeArguments="x:Int32" Name="StatusCode6" />
                                                                                                              <Variable x:TypeArguments="iru:ResponseObject" Name="RespObj6" />
                                                                                                              <Variable x:TypeArguments="njl:JToken" Name="out6" />
                                                                                                              <Variable x:TypeArguments="x:Int32" Name="StatusCode7" />
                                                                                                            </Sequence.Variables>
                                                                                                            <Assign DisplayName="Assign inbn" sap2010:WorkflowViewState.IdRef="Assign_315">
                                                                                                              <Assign.To>
                                                                                                                <OutArgument x:TypeArguments="x:String">[inbn]</OutArgument>
                                                                                                              </Assign.To>
                                                                                                              <Assign.Value>
                                                                                                                <InArgument x:TypeArguments="x:String">[inbnValue]</InArgument>
                                                                                                              </Assign.Value>
                                                                                                            </Assign>
                                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_316">
                                                                                                              <Assign.To>
                                                                                                                <OutArgument x:TypeArguments="scg:List(x:String)">[vatList]</OutArgument>
                                                                                                              </Assign.To>
                                                                                                              <Assign.Value>
                                                                                                                <InArgument x:TypeArguments="scg:List(x:String)">[New List(Of String)()]</InArgument>
                                                                                                              </Assign.Value>
                                                                                                            </Assign>
                                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_317">
                                                                                                              <Assign.To>
                                                                                                                <OutArgument x:TypeArguments="x:String">[puno]</OutArgument>
                                                                                                              </Assign.To>
                                                                                                              <Assign.Value>
                                                                                                                <InArgument x:TypeArguments="x:String">[DictOcrValues("PO_NUMBER").Tostring]</InArgument>
                                                                                                              </Assign.Value>
                                                                                                            </Assign>
                                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_318">
                                                                                                              <Assign.To>
                                                                                                                <OutArgument x:TypeArguments="x:String">[pnli]</OutArgument>
                                                                                                              </Assign.To>
                                                                                                              <Assign.Value>
                                                                                                                <InArgument x:TypeArguments="x:String">[rows(0)]</InArgument>
                                                                                                              </Assign.Value>
                                                                                                            </Assign>
                                                                                                            <Assign DisplayName="Assign itno" sap2010:WorkflowViewState.IdRef="Assign_319">
                                                                                                              <Assign.To>
                                                                                                                <OutArgument x:TypeArguments="x:String">[itno]</OutArgument>
                                                                                                              </Assign.To>
                                                                                                              <Assign.Value>
                                                                                                                <InArgument x:TypeArguments="x:String">[rows(5)]</InArgument>
                                                                                                              </Assign.Value>
                                                                                                            </Assign>
                                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_320">
                                                                                                              <Assign.To>
                                                                                                                <OutArgument x:TypeArguments="x:String">[repn]</OutArgument>
                                                                                                              </Assign.To>
                                                                                                              <Assign.Value>
                                                                                                                <InArgument x:TypeArguments="x:String">[rows(1)]</InArgument>
                                                                                                              </Assign.Value>
                                                                                                            </Assign>
                                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_321">
                                                                                                              <Assign.To>
                                                                                                                <OutArgument x:TypeArguments="x:String">[ivqa]</OutArgument>
                                                                                                              </Assign.To>
                                                                                                              <Assign.Value>
                                                                                                                <InArgument x:TypeArguments="x:String">[rows(3)]</InArgument>
                                                                                                              </Assign.Value>
                                                                                                            </Assign>
                                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_322">
                                                                                                              <Assign.To>
                                                                                                                <OutArgument x:TypeArguments="x:String">[grpr]</OutArgument>
                                                                                                              </Assign.To>
                                                                                                              <Assign.Value>
                                                                                                                <InArgument x:TypeArguments="x:String">[rows(2)]</InArgument>
                                                                                                              </Assign.Value>
                                                                                                            </Assign>
                                                                                                            <iai:IONAPIRequestWizard FileAttachments="{x:Null}" Headers="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for ppun, puun" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_35" Response="[respObj7]" StatusCode="[StatusCode7]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/MMS200MI/GetItmBasic&quot;]">
                                                                                                              <iai:IONAPIRequestWizard.QueryParameters>
                                                                                                                <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                                                  <scg:List x:TypeArguments="x:String" Capacity="8">
                                                                                                                    <x:String>ITNO</x:String>
                                                                                                                    <x:String>dateformat</x:String>
                                                                                                                    <x:String>excludeempty</x:String>
                                                                                                                    <x:String>righttrim</x:String>
                                                                                                                    <x:String>format</x:String>
                                                                                                                    <x:String>extendedresult</x:String>
                                                                                                                  </scg:List>
                                                                                                                  <scg:List x:TypeArguments="x:String" Capacity="8">
                                                                                                                    <x:String>itno</x:String>
                                                                                                                    <x:String>YMD8</x:String>
                                                                                                                    <x:String>false</x:String>
                                                                                                                    <x:String>true</x:String>
                                                                                                                    <x:String>PRETTY</x:String>
                                                                                                                    <x:String>false</x:String>
                                                                                                                  </scg:List>
                                                                                                                </scg:List>
                                                                                                              </iai:IONAPIRequestWizard.QueryParameters>
                                                                                                            </iai:IONAPIRequestWizard>
                                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_323">
                                                                                                              <Assign.To>
                                                                                                                <OutArgument x:TypeArguments="njl:JToken">[out7]</OutArgument>
                                                                                                              </Assign.To>
                                                                                                              <Assign.Value>
                                                                                                                <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj7.ReadAsText)]</InArgument>
                                                                                                              </Assign.Value>
                                                                                                            </Assign>
                                                                                                            <If Condition="[out7(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_138">
                                                                                                              <If.Then>
                                                                                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_146">
                                                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_324">
                                                                                                                    <Assign.To>
                                                                                                                      <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                                                    </Assign.To>
                                                                                                                    <Assign.Value>
                                                                                                                      <InArgument x:TypeArguments="x:String">["Purchase price UOM and purchase order UOM are not available."]</InArgument>
                                                                                                                    </Assign.Value>
                                                                                                                  </Assign>
                                                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_325">
                                                                                                                    <Assign.To>
                                                                                                                      <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                                                                    </Assign.To>
                                                                                                                    <Assign.Value>
                                                                                                                      <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                                                                                                                    </Assign.Value>
                                                                                                                  </Assign>
                                                                                                                  <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_80" Line="[commentStatus]" Source="[logfile]" />
                                                                                                                </Sequence>
                                                                                                              </If.Then>
                                                                                                              <If.Else>
                                                                                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_149">
                                                                                                                  <Sequence.Variables>
                                                                                                                    <Variable x:TypeArguments="njl:JToken" Name="out8" />
                                                                                                                    <Variable x:TypeArguments="x:Int32" Name="StatusCode8" />
                                                                                                                    <Variable x:TypeArguments="iru:ResponseObject" Name="respObj8" />
                                                                                                                  </Sequence.Variables>
                                                                                                                  <Assign DisplayName="Assign ppun" sap2010:WorkflowViewState.IdRef="Assign_326">
                                                                                                                    <Assign.To>
                                                                                                                      <OutArgument x:TypeArguments="x:String">[ppun]</OutArgument>
                                                                                                                    </Assign.To>
                                                                                                                    <Assign.Value>
                                                                                                                      <InArgument x:TypeArguments="x:String">[out7("results")(0)("records")(0)("PPUN").ToString]</InArgument>
                                                                                                                    </Assign.Value>
                                                                                                                  </Assign>
                                                                                                                  <Assign DisplayName="Assign puun" sap2010:WorkflowViewState.IdRef="Assign_327">
                                                                                                                    <Assign.To>
                                                                                                                      <OutArgument x:TypeArguments="x:String">[puun]</OutArgument>
                                                                                                                    </Assign.To>
                                                                                                                    <Assign.Value>
                                                                                                                      <InArgument x:TypeArguments="x:String">[out7("results")(0)("records")(0)("PUUN").ToString]</InArgument>
                                                                                                                    </Assign.Value>
                                                                                                                  </Assign>
                                                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_328">
                                                                                                                    <Assign.To>
                                                                                                                      <OutArgument x:TypeArguments="x:String">[puno]</OutArgument>
                                                                                                                    </Assign.To>
                                                                                                                    <Assign.Value>
                                                                                                                      <InArgument x:TypeArguments="x:String">[DictOcrValues("PO_NUMBER").Tostring]</InArgument>
                                                                                                                    </Assign.Value>
                                                                                                                  </Assign>
                                                                                                                  <If Condition="[m=0 AND vatCodeConfig = &quot;LINE&quot;]" sap2010:WorkflowViewState.IdRef="If_136">
                                                                                                                    <If.Then>
                                                                                                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_147">
                                                                                                                        <Sequence.Variables>
                                                                                                                          <Variable x:TypeArguments="iru:ResponseObject" Name="vatResp" />
                                                                                                                          <Variable x:TypeArguments="njl:JToken" Name="vatOut" />
                                                                                                                        </Sequence.Variables>
                                                                                                                        <iai:IONAPIRequestWizard FileAttachments="{x:Null}" Headers="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_36" Response="[vatResp]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/PPS200MI/GetLine?dateformat=YMD8&amp;excludeempty=false&amp;righttrim=true&amp;format=PRETTY&amp;extendedresult=false&quot;]">
                                                                                                                          <iai:IONAPIRequestWizard.QueryParameters>
                                                                                                                            <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                                                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                                                <x:String>PNLI</x:String>
                                                                                                                                <x:String>PUNO</x:String>
                                                                                                                              </scg:List>
                                                                                                                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                                                <x:String>pnli</x:String>
                                                                                                                                <x:String>puno</x:String>
                                                                                                                              </scg:List>
                                                                                                                            </scg:List>
                                                                                                                          </iai:IONAPIRequestWizard.QueryParameters>
                                                                                                                        </iai:IONAPIRequestWizard>
                                                                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_329">
                                                                                                                          <Assign.To>
                                                                                                                            <OutArgument x:TypeArguments="njl:JToken">[vatOut]</OutArgument>
                                                                                                                          </Assign.To>
                                                                                                                          <Assign.Value>
                                                                                                                            <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(vatResp.ReadAsText)]</InArgument>
                                                                                                                          </Assign.Value>
                                                                                                                        </Assign>
                                                                                                                        <If Condition="[vatOut(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_135">
                                                                                                                          <If.Else>
                                                                                                                            <If Condition="[vatOut(&quot;results&quot;)(0)(&quot;records&quot;)(0)(&quot;VTCD&quot;).ToString &lt;&gt; &quot;&quot; AND vatOut(&quot;results&quot;)(0)(&quot;records&quot;)(0)(&quot;VTCD&quot;).ToString &lt;&gt; &quot;0&quot;]" sap2010:WorkflowViewState.IdRef="If_134">
                                                                                                                              <If.Then>
                                                                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_330">
                                                                                                                                  <Assign.To>
                                                                                                                                    <OutArgument x:TypeArguments="x:String">[vatCode]</OutArgument>
                                                                                                                                  </Assign.To>
                                                                                                                                  <Assign.Value>
                                                                                                                                    <InArgument x:TypeArguments="x:String">[vatOut("results")(0)("records")(0)("VTCD").ToString]</InArgument>
                                                                                                                                  </Assign.Value>
                                                                                                                                </Assign>
                                                                                                                              </If.Then>
                                                                                                                            </If>
                                                                                                                          </If.Else>
                                                                                                                        </If>
                                                                                                                      </Sequence>
                                                                                                                    </If.Then>
                                                                                                                  </If>
                                                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_331">
                                                                                                                    <Assign.To>
                                                                                                                      <OutArgument x:TypeArguments="x:Int32">[m]</OutArgument>
                                                                                                                    </Assign.To>
                                                                                                                    <Assign.Value>
                                                                                                                      <InArgument x:TypeArguments="x:Int32">[m+1]</InArgument>
                                                                                                                    </Assign.Value>
                                                                                                                  </Assign>
                                                                                                                  <iai:IONAPIRequestWizard FileAttachments="{x:Null}" Headers="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding lines" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_37" Response="[respObj8]" StatusCode="[StatusCode8]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                                                                                                                    <iai:IONAPIRequestWizard.QueryParameters>
                                                                                                                      <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                                                        <scg:List x:TypeArguments="x:String" Capacity="16">
                                                                                                                          <x:String>INBN</x:String>
                                                                                                                          <x:String>RDTP</x:String>
                                                                                                                          <x:String>DIVI</x:String>
                                                                                                                          <x:String>PPUN</x:String>
                                                                                                                          <x:String>PUUN</x:String>
                                                                                                                          <x:String>GRPR</x:String>
                                                                                                                          <x:String>ITNO</x:String>
                                                                                                                          <x:String>IVQA</x:String>
                                                                                                                          <x:String>PNLI</x:String>
                                                                                                                          <x:String>PUNO</x:String>
                                                                                                                          <x:String>RELP</x:String>
                                                                                                                          <x:String>REPN</x:String>
                                                                                                                        </scg:List>
                                                                                                                        <scg:List x:TypeArguments="x:String" Capacity="16">
                                                                                                                          <x:String>inbn</x:String>
                                                                                                                          <x:String>1</x:String>
                                                                                                                          <x:String>division</x:String>
                                                                                                                          <x:String>ppun</x:String>
                                                                                                                          <x:String>puun</x:String>
                                                                                                                          <x:String>grpr</x:String>
                                                                                                                          <x:String>itno</x:String>
                                                                                                                          <x:String>ivqa</x:String>
                                                                                                                          <x:String>pnli</x:String>
                                                                                                                          <x:String>puno</x:String>
                                                                                                                          <x:String>1</x:String>
                                                                                                                          <x:String>repn</x:String>
                                                                                                                        </scg:List>
                                                                                                                      </scg:List>
                                                                                                                    </iai:IONAPIRequestWizard.QueryParameters>
                                                                                                                  </iai:IONAPIRequestWizard>
                                                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_332">
                                                                                                                    <Assign.To>
                                                                                                                      <OutArgument x:TypeArguments="njl:JToken">[out8]</OutArgument>
                                                                                                                    </Assign.To>
                                                                                                                    <Assign.Value>
                                                                                                                      <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj8.ReadAsText)]</InArgument>
                                                                                                                    </Assign.Value>
                                                                                                                  </Assign>
                                                                                                                  <If Condition="[out8(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_137">
                                                                                                                    <If.Else>
                                                                                                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_148">
                                                                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_333">
                                                                                                                          <Assign.To>
                                                                                                                            <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                                                          </Assign.To>
                                                                                                                          <Assign.Value>
                                                                                                                            <InArgument x:TypeArguments="x:String">Invoice Line has been created</InArgument>
                                                                                                                          </Assign.Value>
                                                                                                                        </Assign>
                                                                                                                        <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_81" Line="[commentStatus]" Source="[logfile]" />
                                                                                                                      </Sequence>
                                                                                                                    </If.Else>
                                                                                                                  </If>
                                                                                                                </Sequence>
                                                                                                              </If.Else>
                                                                                                            </If>
                                                                                                          </Sequence>
                                                                                                        </If.Then>
                                                                                                      </If>
                                                                                                    </Sequence>
                                                                                                  </ActivityAction>
                                                                                                </ForEach>
                                                                                              </Sequence>
                                                                                            </ActivityAction>
                                                                                          </ForEach>
                                                                                          <If Condition="[DictOcrValues(&quot;VAT_AMOUNT&quot;).Tostring &lt;&gt; &quot;&quot;]" DisplayName="If TAX" sap2010:WorkflowViewState.IdRef="If_146">
                                                                                            <If.Then>
                                                                                              <If Condition="[CInt(DictOcrValues(&quot;VAT_AMOUNT&quot;).Tostring).ToString &lt;&gt; &quot;0&quot;]" sap2010:WorkflowViewState.IdRef="If_145">
                                                                                                <If.Then>
                                                                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_155">
                                                                                                    <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_82" Line="Tax line available" Source="[logfile]" />
                                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_334">
                                                                                                      <Assign.To>
                                                                                                        <OutArgument x:TypeArguments="x:String">[vat]</OutArgument>
                                                                                                      </Assign.To>
                                                                                                      <Assign.Value>
                                                                                                        <InArgument x:TypeArguments="x:String">[DictOcrValues("VAT_AMOUNT").Tostring]</InArgument>
                                                                                                      </Assign.Value>
                                                                                                    </Assign>
                                                                                                    <If Condition="[vatCodeConfig = &quot;NA&quot;]" sap2010:WorkflowViewState.IdRef="If_142">
                                                                                                      <If.Then>
                                                                                                        <iai:IONAPIRequestWizard FileAttachments="{x:Null}" Headers="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding VAT" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_38" Response="[respObj5]" StatusCode="[respout]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                                                                                                          <iai:IONAPIRequestWizard.QueryParameters>
                                                                                                            <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                                <x:String>INBN</x:String>
                                                                                                                <x:String>RDTP</x:String>
                                                                                                                <x:String>DIVI</x:String>
                                                                                                                <x:String>GLAM</x:String>
                                                                                                              </scg:List>
                                                                                                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                                <x:String>inbnValue</x:String>
                                                                                                                <x:String>3</x:String>
                                                                                                                <x:String>division</x:String>
                                                                                                                <x:String>vat</x:String>
                                                                                                              </scg:List>
                                                                                                            </scg:List>
                                                                                                          </iai:IONAPIRequestWizard.QueryParameters>
                                                                                                        </iai:IONAPIRequestWizard>
                                                                                                      </If.Then>
                                                                                                      <If.Else>
                                                                                                        <If Condition="[vatCodeConfig &lt;&gt; &quot;NA&quot; AND vatCodeConfig &lt;&gt; &quot;LINE&quot;]" sap2010:WorkflowViewState.IdRef="If_141">
                                                                                                          <If.Then>
                                                                                                            <iai:IONAPIRequestWizard FileAttachments="{x:Null}" Headers="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding VAT" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_39" Response="[respObj5]" StatusCode="[respout]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                                                                                                              <iai:IONAPIRequestWizard.QueryParameters>
                                                                                                                <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                                                  <scg:List x:TypeArguments="x:String" Capacity="8">
                                                                                                                    <x:String>INBN</x:String>
                                                                                                                    <x:String>RDTP</x:String>
                                                                                                                    <x:String>DIVI</x:String>
                                                                                                                    <x:String>VTA1</x:String>
                                                                                                                    <x:String>VTCD</x:String>
                                                                                                                  </scg:List>
                                                                                                                  <scg:List x:TypeArguments="x:String" Capacity="8">
                                                                                                                    <x:String>inbnValue</x:String>
                                                                                                                    <x:String>3</x:String>
                                                                                                                    <x:String>division</x:String>
                                                                                                                    <x:String>vat</x:String>
                                                                                                                    <x:String>vatCodeConfig</x:String>
                                                                                                                  </scg:List>
                                                                                                                </scg:List>
                                                                                                              </iai:IONAPIRequestWizard.QueryParameters>
                                                                                                            </iai:IONAPIRequestWizard>
                                                                                                          </If.Then>
                                                                                                          <If.Else>
                                                                                                            <If Condition="[vatCodeConfig = &quot;LINE&quot;]" sap2010:WorkflowViewState.IdRef="If_140">
                                                                                                              <If.Then>
                                                                                                                <iai:IONAPIRequestWizard FileAttachments="{x:Null}" Headers="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding VAT" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_40" Response="[respObj5]" StatusCode="[respout]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                                                                                                                  <iai:IONAPIRequestWizard.QueryParameters>
                                                                                                                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                                                      <scg:List x:TypeArguments="x:String" Capacity="8">
                                                                                                                        <x:String>INBN</x:String>
                                                                                                                        <x:String>RDTP</x:String>
                                                                                                                        <x:String>DIVI</x:String>
                                                                                                                        <x:String>VTA1</x:String>
                                                                                                                        <x:String>VTCD</x:String>
                                                                                                                      </scg:List>
                                                                                                                      <scg:List x:TypeArguments="x:String" Capacity="8">
                                                                                                                        <x:String>inbnValue</x:String>
                                                                                                                        <x:String>3</x:String>
                                                                                                                        <x:String>division</x:String>
                                                                                                                        <x:String>vat</x:String>
                                                                                                                        <x:String>vatCode</x:String>
                                                                                                                      </scg:List>
                                                                                                                    </scg:List>
                                                                                                                  </iai:IONAPIRequestWizard.QueryParameters>
                                                                                                                </iai:IONAPIRequestWizard>
                                                                                                              </If.Then>
                                                                                                            </If>
                                                                                                          </If.Else>
                                                                                                        </If>
                                                                                                      </If.Else>
                                                                                                    </If>
                                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_335">
                                                                                                      <Assign.To>
                                                                                                        <OutArgument x:TypeArguments="njl:JToken">[out5]</OutArgument>
                                                                                                      </Assign.To>
                                                                                                      <Assign.Value>
                                                                                                        <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj5.ReadAsText)]</InArgument>
                                                                                                      </Assign.Value>
                                                                                                    </Assign>
                                                                                                    <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_8">
                                                                                                      <TryCatch.Try>
                                                                                                        <If Condition="[respout = 200]" sap2010:WorkflowViewState.IdRef="If_144">
                                                                                                          <If.Then>
                                                                                                            <If Condition="[out5(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_143">
                                                                                                              <If.Then>
                                                                                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_153">
                                                                                                                  <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_83" Line="[(out5(&quot;results&quot;)(0)(&quot;errorMessage&quot;)).ToString]" Source="[logfile]" />
                                                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_336">
                                                                                                                    <Assign.To>
                                                                                                                      <OutArgument x:TypeArguments="x:String">[additionalChargeExcp]</OutArgument>
                                                                                                                    </Assign.To>
                                                                                                                    <Assign.Value>
                                                                                                                      <InArgument x:TypeArguments="x:String">[additionalChargeExcp + "Tax line failed."]</InArgument>
                                                                                                                    </Assign.Value>
                                                                                                                  </Assign>
                                                                                                                </Sequence>
                                                                                                              </If.Then>
                                                                                                              <If.Else>
                                                                                                                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_84" Line="Vat line added" Source="[logfile]" />
                                                                                                              </If.Else>
                                                                                                            </If>
                                                                                                          </If.Then>
                                                                                                          <If.Else>
                                                                                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_154">
                                                                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_337">
                                                                                                                <Assign.To>
                                                                                                                  <OutArgument x:TypeArguments="x:String">[additionalChargeExcp]</OutArgument>
                                                                                                                </Assign.To>
                                                                                                                <Assign.Value>
                                                                                                                  <InArgument x:TypeArguments="x:String">[additionalChargeExcp]</InArgument>
                                                                                                                </Assign.Value>
                                                                                                              </Assign>
                                                                                                              <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_85" Line="Error while adding the Vat line" Source="[logfile]" />
                                                                                                            </Sequence>
                                                                                                          </If.Else>
                                                                                                        </If>
                                                                                                      </TryCatch.Try>
                                                                                                      <TryCatch.Catches>
                                                                                                        <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_8">
                                                                                                          <ActivityAction x:TypeArguments="s:Exception">
                                                                                                            <ActivityAction.Argument>
                                                                                                              <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                                                                                                            </ActivityAction.Argument>
                                                                                                            <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_86" Line="Exception while adding Vat line" Source="[logfile]" />
                                                                                                          </ActivityAction>
                                                                                                        </Catch>
                                                                                                      </TryCatch.Catches>
                                                                                                    </TryCatch>
                                                                                                  </Sequence>
                                                                                                </If.Then>
                                                                                              </If>
                                                                                            </If.Then>
                                                                                          </If>
                                                                                          <If Condition="[DictOcrValues(&quot;SHIPPING_AND_HANDLING_AMOUNT&quot;).Tostring &lt;&gt; &quot;&quot;]" DisplayName="If Charges" sap2010:WorkflowViewState.IdRef="If_150">
                                                                                            <If.Then>
                                                                                              <If Condition="[CInt(DictOcrValues(&quot;SHIPPING_AND_HANDLING_AMOUNT&quot;).Tostring).ToString &lt;&gt; &quot;0&quot;]" sap2010:WorkflowViewState.IdRef="If_149">
                                                                                                <If.Then>
                                                                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_158">
                                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_338">
                                                                                                      <Assign.To>
                                                                                                        <OutArgument x:TypeArguments="x:String">[charge]</OutArgument>
                                                                                                      </Assign.To>
                                                                                                      <Assign.Value>
                                                                                                        <InArgument x:TypeArguments="x:String">[DictOcrValues("SHIPPING_AND_HANDLING_AMOUNT").Tostring]</InArgument>
                                                                                                      </Assign.Value>
                                                                                                    </Assign>
                                                                                                    <iai:IONAPIRequestWizard FileAttachments="{x:Null}" Headers="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding charges" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_41" Response="[respObj5]" StatusCode="[respout]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                                                                                                      <iai:IONAPIRequestWizard.QueryParameters>
                                                                                                        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                                          <scg:List x:TypeArguments="x:String" Capacity="8">
                                                                                                            <x:String>INBN</x:String>
                                                                                                            <x:String>RDTP</x:String>
                                                                                                            <x:String>DIVI</x:String>
                                                                                                            <x:String>NLAM</x:String>
                                                                                                            <x:String>CEID</x:String>
                                                                                                            <x:String>PUNO</x:String>
                                                                                                          </scg:List>
                                                                                                          <scg:List x:TypeArguments="x:String" Capacity="8">
                                                                                                            <x:String>inbnValue</x:String>
                                                                                                            <x:String>2</x:String>
                                                                                                            <x:String>division</x:String>
                                                                                                            <x:String>charge</x:String>
                                                                                                            <x:String>chargeCode</x:String>
                                                                                                            <x:String>pono</x:String>
                                                                                                          </scg:List>
                                                                                                        </scg:List>
                                                                                                      </iai:IONAPIRequestWizard.QueryParameters>
                                                                                                    </iai:IONAPIRequestWizard>
                                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_339">
                                                                                                      <Assign.To>
                                                                                                        <OutArgument x:TypeArguments="njl:JToken">[out5]</OutArgument>
                                                                                                      </Assign.To>
                                                                                                      <Assign.Value>
                                                                                                        <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj5.ReadAsText)]</InArgument>
                                                                                                      </Assign.Value>
                                                                                                    </Assign>
                                                                                                    <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_9">
                                                                                                      <TryCatch.Try>
                                                                                                        <If Condition="[respout = 200]" sap2010:WorkflowViewState.IdRef="If_148">
                                                                                                          <If.Then>
                                                                                                            <If Condition="[out5(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_147">
                                                                                                              <If.Then>
                                                                                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_156">
                                                                                                                  <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_87" Line="[(out5(&quot;results&quot;)(0)(&quot;errorMessage&quot;)).ToString]" Source="[logfile]" />
                                                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_340">
                                                                                                                    <Assign.To>
                                                                                                                      <OutArgument x:TypeArguments="x:String">[additionalChargeExcp]</OutArgument>
                                                                                                                    </Assign.To>
                                                                                                                    <Assign.Value>
                                                                                                                      <InArgument x:TypeArguments="x:String">[additionalChargeExcp + "Charge line failed."]</InArgument>
                                                                                                                    </Assign.Value>
                                                                                                                  </Assign>
                                                                                                                </Sequence>
                                                                                                              </If.Then>
                                                                                                              <If.Else>
                                                                                                                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_88" Line="Charge line added" Source="[logfile]" />
                                                                                                              </If.Else>
                                                                                                            </If>
                                                                                                          </If.Then>
                                                                                                          <If.Else>
                                                                                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_157">
                                                                                                              <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_89" Line="Error while adding the charge line" Source="[logfile]" />
                                                                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_341">
                                                                                                                <Assign.To>
                                                                                                                  <OutArgument x:TypeArguments="x:String">[additionalChargeExcp]</OutArgument>
                                                                                                                </Assign.To>
                                                                                                                <Assign.Value>
                                                                                                                  <InArgument x:TypeArguments="x:String">[additionalChargeExcp + "Charge line failed."]</InArgument>
                                                                                                                </Assign.Value>
                                                                                                              </Assign>
                                                                                                            </Sequence>
                                                                                                          </If.Else>
                                                                                                        </If>
                                                                                                      </TryCatch.Try>
                                                                                                      <TryCatch.Catches>
                                                                                                        <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_9">
                                                                                                          <ActivityAction x:TypeArguments="s:Exception">
                                                                                                            <ActivityAction.Argument>
                                                                                                              <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                                                                                                            </ActivityAction.Argument>
                                                                                                            <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_90" Line="Exception while adding charge line" Source="[logfile]" />
                                                                                                          </ActivityAction>
                                                                                                        </Catch>
                                                                                                      </TryCatch.Catches>
                                                                                                    </TryCatch>
                                                                                                  </Sequence>
                                                                                                </If.Then>
                                                                                              </If>
                                                                                            </If.Then>
                                                                                          </If>
                                                                                          <If Condition="[DictOcrValues(&quot;Invoice_Level_Discount_AMOUNT&quot;).Tostring &lt;&gt; &quot;&quot; AND NOT (discountHandling = &quot;True&quot;)]" DisplayName="If discount" sap2010:WorkflowViewState.IdRef="If_154">
                                                                                            <If.Then>
                                                                                              <If Condition="[CInt(DictOcrValues(&quot;Invoice_Level_Discount_AMOUNT&quot;).Tostring).ToString &lt;&gt; &quot;0&quot;]" sap2010:WorkflowViewState.IdRef="If_153">
                                                                                                <If.Then>
                                                                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_161">
                                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_342">
                                                                                                      <Assign.To>
                                                                                                        <OutArgument x:TypeArguments="x:String">[discount]</OutArgument>
                                                                                                      </Assign.To>
                                                                                                      <Assign.Value>
                                                                                                        <InArgument x:TypeArguments="x:String">["-"+DictOcrValues("Invoice_Level_Discount_AMOUNT").Tostring]</InArgument>
                                                                                                      </Assign.Value>
                                                                                                    </Assign>
                                                                                                    <iai:IONAPIRequestWizard FileAttachments="{x:Null}" Headers="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding Discount" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_42" Response="[respObj5]" StatusCode="[respout]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                                                                                                      <iai:IONAPIRequestWizard.QueryParameters>
                                                                                                        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                                          <scg:List x:TypeArguments="x:String" Capacity="8">
                                                                                                            <x:String>INBN</x:String>
                                                                                                            <x:String>RDTP</x:String>
                                                                                                            <x:String>DIVI</x:String>
                                                                                                            <x:String>NLAM</x:String>
                                                                                                            <x:String>CEID</x:String>
                                                                                                            <x:String>PUNO</x:String>
                                                                                                          </scg:List>
                                                                                                          <scg:List x:TypeArguments="x:String" Capacity="8">
                                                                                                            <x:String>inbnValue</x:String>
                                                                                                            <x:String>2</x:String>
                                                                                                            <x:String>division</x:String>
                                                                                                            <x:String>discount</x:String>
                                                                                                            <x:String>discountCode</x:String>
                                                                                                            <x:String>pono</x:String>
                                                                                                          </scg:List>
                                                                                                        </scg:List>
                                                                                                      </iai:IONAPIRequestWizard.QueryParameters>
                                                                                                    </iai:IONAPIRequestWizard>
                                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_343">
                                                                                                      <Assign.To>
                                                                                                        <OutArgument x:TypeArguments="njl:JToken">[out5]</OutArgument>
                                                                                                      </Assign.To>
                                                                                                      <Assign.Value>
                                                                                                        <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj5.ReadAsText)]</InArgument>
                                                                                                      </Assign.Value>
                                                                                                    </Assign>
                                                                                                    <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_10">
                                                                                                      <TryCatch.Try>
                                                                                                        <If Condition="[respout = 200]" sap2010:WorkflowViewState.IdRef="If_152">
                                                                                                          <If.Then>
                                                                                                            <If Condition="[out5(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_151">
                                                                                                              <If.Then>
                                                                                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_159">
                                                                                                                  <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_91" Line="[(out5(&quot;results&quot;)(0)(&quot;errorMessage&quot;)).ToString]" Source="[logfile]" />
                                                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_344">
                                                                                                                    <Assign.To>
                                                                                                                      <OutArgument x:TypeArguments="x:String">[additionalChargeExcp]</OutArgument>
                                                                                                                    </Assign.To>
                                                                                                                    <Assign.Value>
                                                                                                                      <InArgument x:TypeArguments="x:String">[additionalChargeExcp + "Discount line failed."]</InArgument>
                                                                                                                    </Assign.Value>
                                                                                                                  </Assign>
                                                                                                                </Sequence>
                                                                                                              </If.Then>
                                                                                                              <If.Else>
                                                                                                                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_92" Line="Discount line added" Source="[logfile]" />
                                                                                                              </If.Else>
                                                                                                            </If>
                                                                                                          </If.Then>
                                                                                                          <If.Else>
                                                                                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_160">
                                                                                                              <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_93" Line="Error while adding the discount line" Source="[logfile]" />
                                                                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_345">
                                                                                                                <Assign.To>
                                                                                                                  <OutArgument x:TypeArguments="x:String">[additionalChargeExcp]</OutArgument>
                                                                                                                </Assign.To>
                                                                                                                <Assign.Value>
                                                                                                                  <InArgument x:TypeArguments="x:String">[additionalChargeExcp + "Discount line failed."]</InArgument>
                                                                                                                </Assign.Value>
                                                                                                              </Assign>
                                                                                                            </Sequence>
                                                                                                          </If.Else>
                                                                                                        </If>
                                                                                                      </TryCatch.Try>
                                                                                                      <TryCatch.Catches>
                                                                                                        <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_10">
                                                                                                          <ActivityAction x:TypeArguments="s:Exception">
                                                                                                            <ActivityAction.Argument>
                                                                                                              <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                                                                                                            </ActivityAction.Argument>
                                                                                                            <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_94" Line="Exception while adding discount line" Source="[logfile]" />
                                                                                                          </ActivityAction>
                                                                                                        </Catch>
                                                                                                      </TryCatch.Catches>
                                                                                                    </TryCatch>
                                                                                                  </Sequence>
                                                                                                </If.Then>
                                                                                              </If>
                                                                                            </If.Then>
                                                                                          </If>
                                                                                        </Sequence>
                                                                                      </If.Then>
                                                                                    </If>
                                                                                    <If Condition="[inbnValue = &quot;&quot;]" DisplayName="If validation" sap2010:WorkflowViewState.IdRef="If_159">
                                                                                      <If.Then>
                                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_346">
                                                                                          <Assign.To>
                                                                                            <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                                          </Assign.To>
                                                                                          <Assign.Value>
                                                                                            <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
                                                                                          </Assign.Value>
                                                                                        </Assign>
                                                                                      </If.Then>
                                                                                      <If.Else>
                                                                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_164">
                                                                                          <Sequence.Variables>
                                                                                            <Variable x:TypeArguments="x:String" Name="SuccessMoved" />
                                                                                          </Sequence.Variables>
                                                                                          <If Condition="[additionalChargeExcp = &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_158">
                                                                                            <If.Then>
                                                                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_163">
                                                                                                <Sequence.Variables>
                                                                                                  <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="checkApprovalResponseDictionary" />
                                                                                                  <Variable x:TypeArguments="x:Int32" Name="checkApprovalResponseCode" />
                                                                                                </Sequence.Variables>
                                                                                                <If Condition="[approvalRequired]" sap2010:WorkflowViewState.IdRef="If_263">
                                                                                                  <If.Then>
                                                                                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_283">
                                                                                                      <Sequence.Variables>
                                                                                                        <Variable x:TypeArguments="x:Boolean" Name="approvalRequest" />
                                                                                                        <Variable x:TypeArguments="x:Int32" Name="sendApprovalResponseCode" />
                                                                                                      </Sequence.Variables>
                                                                                                      <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;amount&quot;,DictOcrValues(&quot;TOTAL&quot;).Tostring},{&quot;tenantID&quot;,tenantID},{&quot;checkAmountBussinessRule&quot;,checkAmountBussinessRule}}]" ContinueOnError="True" DisplayName="Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_6" OutputArguments="[checkApprovalResponseDictionary]" ResponseCode="[checkApprovalResponseCode]" WorkflowFile="[projectPath+&quot;\CheckApproval.xaml&quot;]" />
                                                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_518">
                                                                                                        <Assign.To>
                                                                                                          <OutArgument x:TypeArguments="x:Boolean">[approvalRequest]</OutArgument>
                                                                                                        </Assign.To>
                                                                                                        <Assign.Value>
                                                                                                          <InArgument x:TypeArguments="x:Boolean">[CType(checkApprovalResponseDictionary("approvalRequest"), Boolean)]</InArgument>
                                                                                                        </Assign.Value>
                                                                                                      </Assign>
                                                                                                      <If Condition="[approvalRequest]" sap2010:WorkflowViewState.IdRef="If_264">
                                                                                                        <If.Then>
                                                                                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_284">
                                                                                                            <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;amount&quot;,DictOcrValues(&quot;TOTAL&quot;).Tostring},{&quot;tenantID&quot;,tenantID},{&quot;division&quot;,DIVISION},{&quot;inbn&quot;,inbnValue},{&quot;invoiceNumber&quot;,DictOcrValues(&quot;INVOICE_RECEIPT_ID&quot;).Tostring},{&quot;poNumber&quot;,DictOcrValues(&quot;PO_NUMBER&quot;).Tostring},{&quot;vendorName&quot;,vendorId},{&quot;approvalWorkflow&quot;,approvalWorkflow}}]" ContinueOnError="True" DisplayName="Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_7" OutputArguments="[sendApprovalResponseDictionary]" ResponseCode="[sendApprovalResponseCode]" WorkflowFile="[projectPath+&quot;\SendtoApprovalWorkflow.xaml&quot;]" />
                                                                                                            <If Condition="[sendApprovalResponseCode = 200]" sap2010:WorkflowViewState.IdRef="If_265">
                                                                                                              <If.Then>
                                                                                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_285">
                                                                                                                  <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_102" Line="Supllier invoice is sent for approval since the invoice amount is large" Source="[logfile]" />
                                                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_519">
                                                                                                                    <Assign.To>
                                                                                                                      <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                                                    </Assign.To>
                                                                                                                    <Assign.Value>
                                                                                                                      <InArgument x:TypeArguments="x:String">["Invoice created and Sent for approval"]</InArgument>
                                                                                                                    </Assign.Value>
                                                                                                                  </Assign>
                                                                                                                </Sequence>
                                                                                                              </If.Then>
                                                                                                            </If>
                                                                                                          </Sequence>
                                                                                                        </If.Then>
                                                                                                        <If.Else>
                                                                                                          <iai:IONAPIRequestWizard FileAttachments="{x:Null}" Headers="{x:Null}" PostData="{x:Null}" Response="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_44" StatusCode="[respout1]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS455MI/ValidByBatchNo&quot;]">
                                                                                                            <iai:IONAPIRequestWizard.QueryParameters>
                                                                                                              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                                                <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                                  <x:String>INBN</x:String>
                                                                                                                  <x:String>DIVI</x:String>
                                                                                                                </scg:List>
                                                                                                                <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                                  <x:String>inbnValue</x:String>
                                                                                                                  <x:String>division</x:String>
                                                                                                                </scg:List>
                                                                                                              </scg:List>
                                                                                                            </iai:IONAPIRequestWizard.QueryParameters>
                                                                                                          </iai:IONAPIRequestWizard>
                                                                                                        </If.Else>
                                                                                                      </If>
                                                                                                    </Sequence>
                                                                                                  </If.Then>
                                                                                                  <If.Else>
                                                                                                    <iai:IONAPIRequestWizard FileAttachments="{x:Null}" Headers="{x:Null}" PostData="{x:Null}" Response="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_43" StatusCode="[respout1]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS455MI/ValidByBatchNo&quot;]">
                                                                                                      <iai:IONAPIRequestWizard.QueryParameters>
                                                                                                        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                                          <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                            <x:String>INBN</x:String>
                                                                                                            <x:String>DIVI</x:String>
                                                                                                          </scg:List>
                                                                                                          <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                            <x:String>inbnValue</x:String>
                                                                                                            <x:String>division</x:String>
                                                                                                          </scg:List>
                                                                                                        </scg:List>
                                                                                                      </iai:IONAPIRequestWizard.QueryParameters>
                                                                                                    </iai:IONAPIRequestWizard>
                                                                                                  </If.Else>
                                                                                                </If>
                                                                                                <If Condition="[matchVendorItemCode]" sap2010:WorkflowViewState.IdRef="If_266">
                                                                                                  <If.Then>
                                                                                                    <If Condition="[transDateItem.Value.count = invoicesItemNumbers.count]" sap2010:WorkflowViewState.IdRef="If_156">
                                                                                                      <If.Then>
                                                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_347">
                                                                                                          <Assign.To>
                                                                                                            <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                                          </Assign.To>
                                                                                                          <Assign.Value>
                                                                                                            <InArgument x:TypeArguments="x:String">["Invoice created and validated"]</InArgument>
                                                                                                          </Assign.Value>
                                                                                                        </Assign>
                                                                                                      </If.Then>
                                                                                                      <If.Else>
                                                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_348">
                                                                                                          <Assign.To>
                                                                                                            <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                                          </Assign.To>
                                                                                                          <Assign.Value>
                                                                                                            <InArgument x:TypeArguments="x:String">["Invoice created and validated, Please verify"]</InArgument>
                                                                                                          </Assign.Value>
                                                                                                        </Assign>
                                                                                                      </If.Else>
                                                                                                    </If>
                                                                                                  </If.Then>
                                                                                                  <If.Else>
                                                                                                    <If Condition="[respOut1 = 200]" sap2010:WorkflowViewState.IdRef="If_268">
                                                                                                      <If.Then>
                                                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_520">
                                                                                                          <Assign.To>
                                                                                                            <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                                          </Assign.To>
                                                                                                          <Assign.Value>
                                                                                                            <InArgument x:TypeArguments="x:String">["Invoice created and validated"]</InArgument>
                                                                                                          </Assign.Value>
                                                                                                        </Assign>
                                                                                                      </If.Then>
                                                                                                    </If>
                                                                                                  </If.Else>
                                                                                                </If>
                                                                                                <If Condition="[vat = &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_157">
                                                                                                  <If.Else>
                                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_349">
                                                                                                      <Assign.To>
                                                                                                        <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                                      </Assign.To>
                                                                                                      <Assign.Value>
                                                                                                        <InArgument x:TypeArguments="x:String">["Invoice created and validated. Vat line is available."]</InArgument>
                                                                                                      </Assign.Value>
                                                                                                    </Assign>
                                                                                                  </If.Else>
                                                                                                </If>
                                                                                              </Sequence>
                                                                                            </If.Then>
                                                                                            <If.Else>
                                                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_350">
                                                                                                <Assign.To>
                                                                                                  <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                                </Assign.To>
                                                                                                <Assign.Value>
                                                                                                  <InArgument x:TypeArguments="x:String">["Invoice created. " + additionalChargeExcp]</InArgument>
                                                                                                </Assign.Value>
                                                                                              </Assign>
                                                                                            </If.Else>
                                                                                          </If>
                                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_351">
                                                                                            <Assign.To>
                                                                                              <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                                            </Assign.To>
                                                                                            <Assign.Value>
                                                                                              <InArgument x:TypeArguments="x:String">["SUCCESS"]</InArgument>
                                                                                            </Assign.Value>
                                                                                          </Assign>
                                                                                        </Sequence>
                                                                                      </If.Else>
                                                                                    </If>
                                                                                  </Sequence>
                                                                                </If.Then>
                                                                                <If.Else>
                                                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_166">
                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_352">
                                                                                      <Assign.To>
                                                                                        <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                      </Assign.To>
                                                                                      <Assign.Value>
                                                                                        <InArgument x:TypeArguments="x:String">["Received an IONAPI error while adding Invoice header."]</InArgument>
                                                                                      </Assign.Value>
                                                                                    </Assign>
                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_353">
                                                                                      <Assign.To>
                                                                                        <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                                      </Assign.To>
                                                                                      <Assign.Value>
                                                                                        <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
                                                                                      </Assign.Value>
                                                                                    </Assign>
                                                                                    <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_95" Line="[commentStatus]" Source="[logfile]" />
                                                                                  </Sequence>
                                                                                </If.Else>
                                                                              </If>
                                                                            </Sequence>
                                                                          </Sequence>
                                                                        </If.Then>
                                                                        <If.Else>
                                                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_169">
                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_354">
                                                                              <Assign.To>
                                                                                <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                              </Assign.To>
                                                                              <Assign.Value>
                                                                                <InArgument x:TypeArguments="x:String">["Received an IONAPI error while fetching Banking details for the Supplier Number " + SupplierNo +"."]</InArgument>
                                                                              </Assign.Value>
                                                                            </Assign>
                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_355">
                                                                              <Assign.To>
                                                                                <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                              </Assign.To>
                                                                              <Assign.Value>
                                                                                <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
                                                                              </Assign.Value>
                                                                            </Assign>
                                                                            <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_96" Line="[commentStatus]" Source="[logfile]" />
                                                                          </Sequence>
                                                                        </If.Else>
                                                                      </If>
                                                                    </Sequence>
                                                                  </Sequence>
                                                                </If.Then>
                                                                <If.Else>
                                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_172">
                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_356">
                                                                      <Assign.To>
                                                                        <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                      </Assign.To>
                                                                      <Assign.Value>
                                                                        <InArgument x:TypeArguments="x:String">The quantity and unit price of the items on the invoice do not match with receipts.</InArgument>
                                                                      </Assign.Value>
                                                                    </Assign>
                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_357">
                                                                      <Assign.To>
                                                                        <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                      </Assign.To>
                                                                      <Assign.Value>
                                                                        <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
                                                                      </Assign.Value>
                                                                    </Assign>
                                                                    <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_97" Line="[commentStatus]" Source="[logfile]" />
                                                                  </Sequence>
                                                                </If.Else>
                                                              </If>
                                                            </Sequence>
                                                          </If.Then>
                                                        </If>
                                                      </Sequence>
                                                    </If.Then>
                                                    <If.Else>
                                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_45">
                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_118">
                                                          <Assign.To>
                                                            <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                          </Assign.To>
                                                          <Assign.Value>
                                                            <InArgument x:TypeArguments="x:String">An error occurred with the AI Optimizer.</InArgument>
                                                          </Assign.Value>
                                                        </Assign>
                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_119">
                                                          <Assign.To>
                                                            <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                          </Assign.To>
                                                          <Assign.Value>
                                                            <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
                                                          </Assign.Value>
                                                        </Assign>
                                                        <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_12" Line="[commentStatus]" Source="[logfile]" />
                                                      </Sequence>
                                                    </If.Else>
                                                  </If>
                                                </Sequence>
                                              </If.Then>
                                            </If>
                                          </Sequence>
                                        </ActivityAction>
                                      </ForEach>
                                    </Sequence>
                                  </If.Then>
                                  <If.Else>
                                    <If Condition="[matchVendorItemCode  and invoicesItemNumbers.Count &gt; 0]" sap2010:WorkflowViewState.IdRef="If_127">
                                      <If.Then>
                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_140">
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_293">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">One or more items listed on the invoice have not been received.</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_294">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">PONOTRECEIVED</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_78" Line="[commentStatus]" Source="[logfile]" />
                                        </Sequence>
                                      </If.Then>
                                      <If.Else>
                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_139">
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_291">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">The item(s) listed on the invoice has not been received.</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_292">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">PONOTRECEIVED</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_77" Line="[commentStatus]" Source="[logfile]" />
                                        </Sequence>
                                      </If.Else>
                                    </If>
                                  </If.Else>
                                </If>
                              </If.Then>
                              <If.Else>
                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_114">
                                  <If Condition="[matchVendorItemCode  and invoicesItemNumbers.Count &gt; 0]" sap2010:WorkflowViewState.IdRef="If_109">
                                    <If.Then>
                                      <If Condition="[allLinesReceived]" sap2010:WorkflowViewState.IdRef="If_112">
                                        <If.Then>
                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_118">
                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_259">
                                              <Assign.To>
                                                <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                              </Assign.To>
                                              <Assign.Value>
                                                <InArgument x:TypeArguments="x:String">All items listed on the invoice have been invoiced.</InArgument>
                                              </Assign.Value>
                                            </Assign>
                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_260">
                                              <Assign.To>
                                                <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                              </Assign.To>
                                              <Assign.Value>
                                                <InArgument x:TypeArguments="x:String">ALLPOLINESINVOICED</InArgument>
                                              </Assign.Value>
                                            </Assign>
                                            <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_69" Line="[commentStatus]" Source="[logFile]" />
                                          </Sequence>
                                        </If.Then>
                                        <If.Else>
                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_119">
                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_261">
                                              <Assign.To>
                                                <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                              </Assign.To>
                                              <Assign.Value>
                                                <InArgument x:TypeArguments="x:String">["The item(s) listed on the invoice has not been received."]</InArgument>
                                              </Assign.Value>
                                            </Assign>
                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_262">
                                              <Assign.To>
                                                <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                              </Assign.To>
                                              <Assign.Value>
                                                <InArgument x:TypeArguments="x:String">PONOTRECEIVED</InArgument>
                                              </Assign.Value>
                                            </Assign>
                                            <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_70" Line="[commentStatus]" Source="[logfile]" />
                                          </Sequence>
                                        </If.Else>
                                      </If>
                                    </If.Then>
                                    <If.Else>
                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_121">
                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_263">
                                          <Assign.To>
                                            <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                          </Assign.To>
                                          <Assign.Value>
                                            <InArgument x:TypeArguments="x:String">Items from the purchase order have not been received or have already been invoiced.</InArgument>
                                          </Assign.Value>
                                        </Assign>
                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_264">
                                          <Assign.To>
                                            <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                          </Assign.To>
                                          <Assign.Value>
                                            <InArgument x:TypeArguments="x:String">PONOTRECEIVED</InArgument>
                                          </Assign.Value>
                                        </Assign>
                                        <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_71" Line="[commentStatus]" Source="[logfile]" />
                                      </Sequence>
                                    </If.Else>
                                  </If>
                                </Sequence>
                              </If.Else>
                            </If>
                          </Sequence>
                        </If.Else>
                      </If>
                    </Sequence>
                  </If.Then>
                  <If.Else>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_44">
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_116">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">["Received an IONAPI error while fetching lines for the PO " + pono +"."]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_117">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_11" Line="[commentStatus]" Source="[logfile]" />
                    </Sequence>
                  </If.Else>
                </If>
              </Sequence>
            </If.Else>
          </If>
        </Sequence>
      </If.Then>
      <If.Else>
        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_43">
          <Assign sap2010:WorkflowViewState.IdRef="Assign_114">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">["Received an IONAPI error while fetching company number and division for the PO " + pono +"."]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_115">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
            </Assign.Value>
          </Assign>
          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_10" Line="[commentStatus]" Source="[logfile]" />
        </Sequence>
      </If.Else>
    </If>
    <sads:DebugSymbol.Symbol>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</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="Assign_1" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_136" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_137" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_158" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_157" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_2" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_1" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_240" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_271" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_3" sap:VirtualizedContainerService.HintSize="1336,62" />
      <sap2010:ViewStateData Id="Assign_7" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_111" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_1" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_1" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_19" sap:VirtualizedContainerService.HintSize="1088,22" />
      <sap2010:ViewStateData Id="Assign_8" sap:VirtualizedContainerService.HintSize="1088,62" />
      <sap2010:ViewStateData Id="Assign_141" sap:VirtualizedContainerService.HintSize="1088,62" />
      <sap2010:ViewStateData Id="Assign_142" sap:VirtualizedContainerService.HintSize="1088,62" />
      <sap2010:ViewStateData Id="Assign_235" sap:VirtualizedContainerService.HintSize="1088,62" />
      <sap2010:ViewStateData Id="Assign_239" sap:VirtualizedContainerService.HintSize="1088,62" />
      <sap2010:ViewStateData Id="InvokeWorkflow_2" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Assign_238" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Assign_234" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_236" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_91" sap:VirtualizedContainerService.HintSize="464,216" />
      <sap2010:ViewStateData Id="If_90" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_237" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_92" sap:VirtualizedContainerService.HintSize="464,216" />
      <sap2010:ViewStateData Id="If_93" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_102" sap:VirtualizedContainerService.HintSize="264,371.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_94" sap:VirtualizedContainerService.HintSize="464,525.333333333333" />
      <sap2010:ViewStateData Id="Sequence_101" sap:VirtualizedContainerService.HintSize="486,813.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_88" sap:VirtualizedContainerService.HintSize="1088,967.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_56" sap:VirtualizedContainerService.HintSize="1088,22" />
      <sap2010:ViewStateData Id="Assign_166" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_167" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_51" sap:VirtualizedContainerService.HintSize="510,216" />
      <sap2010:ViewStateData Id="If_52" sap:VirtualizedContainerService.HintSize="1088,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_168" sap:VirtualizedContainerService.HintSize="1088,62" />
      <sap2010:ViewStateData Id="Assign_164" sap:VirtualizedContainerService.HintSize="1088,62" />
      <sap2010:ViewStateData Id="Assign_163" sap:VirtualizedContainerService.HintSize="1088,62" />
      <sap2010:ViewStateData Id="Assign_9" sap:VirtualizedContainerService.HintSize="1088,62" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_17" sap:VirtualizedContainerService.HintSize="1088,22" />
      <sap2010:ViewStateData Id="Assign_170" sap:VirtualizedContainerService.HintSize="776,62" />
      <sap2010:ViewStateData Id="Assign_171" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Assign_172" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_69" sap:VirtualizedContainerService.HintSize="264,186">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_57" sap:VirtualizedContainerService.HintSize="464,340" />
      <sap2010:ViewStateData Id="Sequence_68" sap:VirtualizedContainerService.HintSize="486,566">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_227" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_228" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_55" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_99" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_56" sap:VirtualizedContainerService.HintSize="776,720" />
      <sap2010:ViewStateData Id="Sequence_67" sap:VirtualizedContainerService.HintSize="798,946">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_225" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_226" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_54" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_98" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_55" sap:VirtualizedContainerService.HintSize="1088,1100" />
      <sap2010:ViewStateData Id="Assign_256" sap:VirtualizedContainerService.HintSize="1088,62" />
      <sap2010:ViewStateData Id="Assign_283" sap:VirtualizedContainerService.HintSize="1088,62" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_31" sap:VirtualizedContainerService.HintSize="2100,22" />
      <sap2010:ViewStateData Id="InvokeWorkflow_5" sap:VirtualizedContainerService.HintSize="812.666666666667,22" />
      <sap2010:ViewStateData Id="Assign_517" sap:VirtualizedContainerService.HintSize="664.666666666667,62" />
      <sap2010:ViewStateData Id="InvokeMethod_13" sap:VirtualizedContainerService.HintSize="464,134" />
      <sap2010:ViewStateData Id="Assign_370" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="InvokeMethod_14" sap:VirtualizedContainerService.HintSize="242,132" />
      <sap2010:ViewStateData Id="Sequence_181" sap:VirtualizedContainerService.HintSize="264,357.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_171" sap:VirtualizedContainerService.HintSize="464,511.333333333333" />
      <sap2010:ViewStateData Id="Sequence_182" sap:VirtualizedContainerService.HintSize="486,809.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_174" sap:VirtualizedContainerService.HintSize="612,963.333333333333" />
      <sap2010:ViewStateData Id="Sequence_186" sap:VirtualizedContainerService.HintSize="634,1087.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_33" sap:VirtualizedContainerService.HintSize="664.666666666667,1240" />
      <sap2010:ViewStateData Id="Sequence_282" sap:VirtualizedContainerService.HintSize="686.666666666667,1466">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_261" sap:VirtualizedContainerService.HintSize="812.666666666667,1620" />
      <sap2010:ViewStateData Id="Sequence_188" sap:VirtualizedContainerService.HintSize="834.666666666667,1806">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeMethod_2" sap:VirtualizedContainerService.HintSize="464,134" />
      <sap2010:ViewStateData Id="Assign_257" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="InvokeMethod_8" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="Sequence_134" sap:VirtualizedContainerService.HintSize="264,360">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_110" sap:VirtualizedContainerService.HintSize="464,514" />
      <sap2010:ViewStateData Id="Sequence_117" sap:VirtualizedContainerService.HintSize="486,812">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_243" sap:VirtualizedContainerService.HintSize="612,62" />
      <sap2010:ViewStateData Id="Assign_369" sap:VirtualizedContainerService.HintSize="612,62" />
      <sap2010:ViewStateData Id="InvokeMethod_3" sap:VirtualizedContainerService.HintSize="464,134" />
      <sap2010:ViewStateData Id="Assign_258" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="InvokeMethod_9" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="Sequence_135" sap:VirtualizedContainerService.HintSize="264,360">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_111" sap:VirtualizedContainerService.HintSize="464,514" />
      <sap2010:ViewStateData Id="Sequence_120" sap:VirtualizedContainerService.HintSize="486,812">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_102" sap:VirtualizedContainerService.HintSize="612,966" />
      <sap2010:ViewStateData Id="Sequence_108" sap:VirtualizedContainerService.HintSize="634,1294">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_96" sap:VirtualizedContainerService.HintSize="1146,1448" />
      <sap2010:ViewStateData Id="Sequence_103" sap:VirtualizedContainerService.HintSize="1168,1572">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_9" sap:VirtualizedContainerService.HintSize="1198.66666666667,1724.66666666667" />
      <sap2010:ViewStateData Id="If_170" sap:VirtualizedContainerService.HintSize="2059.33333333333,1960" />
      <sap2010:ViewStateData Id="Sequence_180" sap:VirtualizedContainerService.HintSize="2081.33333333333,2084">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_65" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Catch`1_7" sap:VirtualizedContainerService.HintSize="2086,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_7" sap:VirtualizedContainerService.HintSize="2100,2322" />
      <sap2010:ViewStateData Id="Append_Line_63" sap:VirtualizedContainerService.HintSize="2100,22" />
      <sap2010:ViewStateData Id="Sequence_124" sap:VirtualizedContainerService.HintSize="2122,2570">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_117" sap:VirtualizedContainerService.HintSize="1088,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="IONAPIRequestWizard_2" sap:VirtualizedContainerService.HintSize="1088,22" />
      <sap2010:ViewStateData Id="Assign_10" sap:VirtualizedContainerService.HintSize="1969.33333333333,62" />
      <sap2010:ViewStateData Id="Assign_13" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_14" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Append_Line_2" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_2" sap:VirtualizedContainerService.HintSize="264,348.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_20" sap:VirtualizedContainerService.HintSize="1657.33333333333,22" />
      <sap2010:ViewStateData Id="Assign_133" sap:VirtualizedContainerService.HintSize="1657.33333333333,62" />
      <sap2010:ViewStateData Id="Assign_16" sap:VirtualizedContainerService.HintSize="1657.33333333333,62" />
      <sap2010:ViewStateData Id="Assign_18" sap:VirtualizedContainerService.HintSize="877.333333333333,62" />
      <sap2010:ViewStateData Id="Assign_19" sap:VirtualizedContainerService.HintSize="877.333333333333,62" />
      <sap2010:ViewStateData Id="Assign_20" sap:VirtualizedContainerService.HintSize="729.333333333333,62" />
      <sap2010:ViewStateData Id="Assign_21" sap:VirtualizedContainerService.HintSize="729.333333333333,62" />
      <sap2010:ViewStateData Id="Assign_22" sap:VirtualizedContainerService.HintSize="729.333333333333,62" />
      <sap2010:ViewStateData Id="Assign_23" sap:VirtualizedContainerService.HintSize="729.333333333333,62" />
      <sap2010:ViewStateData Id="Assign_24" sap:VirtualizedContainerService.HintSize="729.333333333333,62" />
      <sap2010:ViewStateData Id="Assign_25" sap:VirtualizedContainerService.HintSize="729.333333333333,62" />
      <sap2010:ViewStateData Id="Assign_27" sap:VirtualizedContainerService.HintSize="729.333333333333,62" />
      <sap2010:ViewStateData Id="InvokeMethod_1" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="Sequence_106" sap:VirtualizedContainerService.HintSize="239.333333333333,258">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_107" sap:VirtualizedContainerService.HintSize="261.333333333333,382">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_99" sap:VirtualizedContainerService.HintSize="464,536" />
      <sap2010:ViewStateData Id="InvokeMethod_4" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="Sequence_111" sap:VirtualizedContainerService.HintSize="239.333333333333,258">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_107" sap:VirtualizedContainerService.HintSize="729.333333333333,690" />
      <sap2010:ViewStateData Id="Sequence_5" sap:VirtualizedContainerService.HintSize="751.333333333333,1528">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_4" sap:VirtualizedContainerService.HintSize="877.333333333333,1682" />
      <sap2010:ViewStateData Id="Sequence_6" sap:VirtualizedContainerService.HintSize="899.333333333333,2010">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_7" sap:VirtualizedContainerService.HintSize="921.333333333333,2134">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_3" sap:VirtualizedContainerService.HintSize="1657.33333333333,2286.66666666667" />
      <sap2010:ViewStateData Id="Assign_269" sap:VirtualizedContainerService.HintSize="1657.33333333333,62" />
      <sap2010:ViewStateData Id="If_269" sap:VirtualizedContainerService.HintSize="1657.33333333333,214" />
      <sap2010:ViewStateData Id="InvokeMethod_6" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="If_118" sap:VirtualizedContainerService.HintSize="464,288" />
      <sap2010:ViewStateData Id="InvokeMethod_7" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="If_119" sap:VirtualizedContainerService.HintSize="707.333333333333,442" />
      <sap2010:ViewStateData Id="Sequence_125" sap:VirtualizedContainerService.HintSize="729.333333333333,566">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_15" sap:VirtualizedContainerService.HintSize="1657.33333333333,718.666666666667" />
      <sap2010:ViewStateData Id="Assign_363" sap:VirtualizedContainerService.HintSize="464,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_364" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="ForEach`1_31" sap:VirtualizedContainerService.HintSize="464,212.666666666667" />
      <sap2010:ViewStateData Id="Assign_365" sap:VirtualizedContainerService.HintSize="464,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_366" sap:VirtualizedContainerService.HintSize="464,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_367" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="If_166" sap:VirtualizedContainerService.HintSize="464,213.333333333333" />
      <sap2010:ViewStateData Id="Append_Line_100" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Append_Line_101" sap:VirtualizedContainerService.HintSize="218,22" />
      <sap2010:ViewStateData Id="InvokeMethod_12" sap:VirtualizedContainerService.HintSize="218,132" />
      <sap2010:ViewStateData Id="Sequence_176" sap:VirtualizedContainerService.HintSize="240,318">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_167" sap:VirtualizedContainerService.HintSize="464,470" />
      <sap2010:ViewStateData Id="Sequence_177" sap:VirtualizedContainerService.HintSize="486,1466">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_178" sap:VirtualizedContainerService.HintSize="508,1590">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_168" sap:VirtualizedContainerService.HintSize="634,1742" />
      <sap2010:ViewStateData Id="If_169" sap:VirtualizedContainerService.HintSize="760,1894" />
      <sap2010:ViewStateData Id="Sequence_179" sap:VirtualizedContainerService.HintSize="782,2018">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_32" sap:VirtualizedContainerService.HintSize="812.666666666667,2170.66666666667" />
      <sap2010:ViewStateData Id="If_165" sap:VirtualizedContainerService.HintSize="938.666666666667,2324.66666666667" />
      <sap2010:ViewStateData Id="Catch`1_13" sap:VirtualizedContainerService.HintSize="943.333333333333,21.3333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_13" sap:VirtualizedContainerService.HintSize="957.333333333333,2562" />
      <sap2010:ViewStateData Id="If_262" sap:VirtualizedContainerService.HintSize="1657.33333333333,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_64" sap:VirtualizedContainerService.HintSize="664.666666666667,22" />
      <sap2010:ViewStateData Id="Assign_284" sap:VirtualizedContainerService.HintSize="664.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_241" sap:VirtualizedContainerService.HintSize="612,62" />
      <sap2010:ViewStateData Id="Assign_280" sap:VirtualizedContainerService.HintSize="612,62" />
      <sap2010:ViewStateData Id="Assign_242" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_281" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_72" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_285" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_131" sap:VirtualizedContainerService.HintSize="264,452">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_98" sap:VirtualizedContainerService.HintSize="464,606" />
      <sap2010:ViewStateData Id="ForEach`1_26" sap:VirtualizedContainerService.HintSize="612,758.666666666667" />
      <sap2010:ViewStateData Id="Append_Line_73" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Append_Line_74" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_125" sap:VirtualizedContainerService.HintSize="464,214" />
      <sap2010:ViewStateData Id="Assign_286" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Sequence_136" sap:VirtualizedContainerService.HintSize="486,440">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_101" sap:VirtualizedContainerService.HintSize="612,594" />
      <sap2010:ViewStateData Id="Sequence_105" sap:VirtualizedContainerService.HintSize="634,1720.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_10" sap:VirtualizedContainerService.HintSize="664.666666666667,1873.33333333333" />
      <sap2010:ViewStateData Id="Sequence_130" sap:VirtualizedContainerService.HintSize="686.666666666667,2161.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_106" sap:VirtualizedContainerService.HintSize="812.666666666667,2315.33333333333" />
      <sap2010:ViewStateData Id="If_128" sap:VirtualizedContainerService.HintSize="1657.33333333333,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_272" sap:VirtualizedContainerService.HintSize="829.333333333333,62" />
      <sap2010:ViewStateData Id="Assign_278" sap:VirtualizedContainerService.HintSize="628.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_273" sap:VirtualizedContainerService.HintSize="628.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_274" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_121" sap:VirtualizedContainerService.HintSize="554,216" />
      <sap2010:ViewStateData Id="Assign_275" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_128" sap:VirtualizedContainerService.HintSize="264,186">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_277" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_129" sap:VirtualizedContainerService.HintSize="264,186">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_123" sap:VirtualizedContainerService.HintSize="554,340">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_282" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Sequence_132" sap:VirtualizedContainerService.HintSize="576,822">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_133" sap:VirtualizedContainerService.HintSize="598,946">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_19" sap:VirtualizedContainerService.HintSize="628.666666666667,1098.66666666667" />
      <sap2010:ViewStateData Id="InvokeWorkflow_1" sap:VirtualizedContainerService.HintSize="628.666666666667,22" />
      <sap2010:ViewStateData Id="Assign_53" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_54" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_52" sap:VirtualizedContainerService.HintSize="2200.66666666667,62" />
      <sap2010:ViewStateData Id="Assign_296" sap:VirtualizedContainerService.HintSize="1888.66666666667,62" />
      <sap2010:ViewStateData Id="Assign_297" sap:VirtualizedContainerService.HintSize="1888.66666666667,62" />
      <sap2010:ViewStateData Id="Assign_298" sap:VirtualizedContainerService.HintSize="1888.66666666667,62" />
      <sap2010:ViewStateData Id="Assign_299" sap:VirtualizedContainerService.HintSize="1866.66666666667,62" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_32" sap:VirtualizedContainerService.HintSize="1866.66666666667,22" />
      <sap2010:ViewStateData Id="Assign_300" sap:VirtualizedContainerService.HintSize="1554.66666666667,62" />
      <sap2010:ViewStateData Id="Assign_301" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_142" sap:VirtualizedContainerService.HintSize="264,186">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_302" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_143" sap:VirtualizedContainerService.HintSize="264,186">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_131" sap:VirtualizedContainerService.HintSize="1554.66666666667,340" />
      <sap2010:ViewStateData Id="Assign_303" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Catch`1_14" sap:VirtualizedContainerService.HintSize="404.666666666667,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_14" sap:VirtualizedContainerService.HintSize="1532.66666666667,300" />
      <sap2010:ViewStateData Id="Assign_304" sap:VirtualizedContainerService.HintSize="1532.66666666667,62" />
      <sap2010:ViewStateData Id="Assign_305" sap:VirtualizedContainerService.HintSize="1532.66666666667,62" />
      <sap2010:ViewStateData Id="Assign_306" sap:VirtualizedContainerService.HintSize="1532.66666666667,62" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_33" sap:VirtualizedContainerService.HintSize="1532.66666666667,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_34" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_132" sap:VirtualizedContainerService.HintSize="1532.66666666667,214" />
      <sap2010:ViewStateData Id="Assign_307" sap:VirtualizedContainerService.HintSize="1220.66666666667,62" />
      <sap2010:ViewStateData Id="Assign_308" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_309" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_310" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_144" sap:VirtualizedContainerService.HintSize="264,390">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_311" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_312" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_313" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_145" sap:VirtualizedContainerService.HintSize="264,390">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_133" sap:VirtualizedContainerService.HintSize="1220.66666666667,544" />
      <sap2010:ViewStateData Id="Append_Line_79" sap:VirtualizedContainerService.HintSize="1220.66666666667,22" />
      <sap2010:ViewStateData Id="Assign_314" sap:VirtualizedContainerService.HintSize="1220.66666666667,62" />
      <sap2010:ViewStateData Id="Assign_315" sap:VirtualizedContainerService.HintSize="776,62" />
      <sap2010:ViewStateData Id="Assign_316" sap:VirtualizedContainerService.HintSize="776,62" />
      <sap2010:ViewStateData Id="Assign_317" sap:VirtualizedContainerService.HintSize="776,62" />
      <sap2010:ViewStateData Id="Assign_318" sap:VirtualizedContainerService.HintSize="776,62" />
      <sap2010:ViewStateData Id="Assign_319" sap:VirtualizedContainerService.HintSize="776,62" />
      <sap2010:ViewStateData Id="Assign_320" sap:VirtualizedContainerService.HintSize="776,62" />
      <sap2010:ViewStateData Id="Assign_321" sap:VirtualizedContainerService.HintSize="776,62" />
      <sap2010:ViewStateData Id="Assign_322" sap:VirtualizedContainerService.HintSize="776,62" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_35" sap:VirtualizedContainerService.HintSize="776,22" />
      <sap2010:ViewStateData Id="Assign_323" sap:VirtualizedContainerService.HintSize="776,62" />
      <sap2010:ViewStateData Id="Assign_324" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_325" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_80" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_146" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_326" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Assign_327" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Assign_328" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_36" sap:VirtualizedContainerService.HintSize="590,22" />
      <sap2010:ViewStateData Id="Assign_329" sap:VirtualizedContainerService.HintSize="590,62" />
      <sap2010:ViewStateData Id="Assign_330" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="If_134" sap:VirtualizedContainerService.HintSize="464,213.333333333333" />
      <sap2010:ViewStateData Id="If_135" sap:VirtualizedContainerService.HintSize="590,367.333333333333" />
      <sap2010:ViewStateData Id="Sequence_147" sap:VirtualizedContainerService.HintSize="612,655.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_136" sap:VirtualizedContainerService.HintSize="464,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_331" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_37" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Assign_332" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Assign_333" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_81" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_148" sap:VirtualizedContainerService.HintSize="264,248">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_137" sap:VirtualizedContainerService.HintSize="464,402" />
      <sap2010:ViewStateData Id="Sequence_149" sap:VirtualizedContainerService.HintSize="486,1190.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_138" sap:VirtualizedContainerService.HintSize="776,1344.66666666667" />
      <sap2010:ViewStateData Id="Sequence_150" sap:VirtualizedContainerService.HintSize="798,2448.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_139" sap:VirtualizedContainerService.HintSize="924,2602.66666666667" />
      <sap2010:ViewStateData Id="Sequence_151" sap:VirtualizedContainerService.HintSize="946,2726.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_27" sap:VirtualizedContainerService.HintSize="976.666666666667,2879.33333333333" />
      <sap2010:ViewStateData Id="Sequence_152" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_28" sap:VirtualizedContainerService.HintSize="1072.66666666667,212.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_82" sap:VirtualizedContainerService.HintSize="916,22" />
      <sap2010:ViewStateData Id="Assign_334" sap:VirtualizedContainerService.HintSize="916,62" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_38" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_39" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_40" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_140" sap:VirtualizedContainerService.HintSize="464,214" />
      <sap2010:ViewStateData Id="If_141" sap:VirtualizedContainerService.HintSize="690,368" />
      <sap2010:ViewStateData Id="If_142" sap:VirtualizedContainerService.HintSize="916,522" />
      <sap2010:ViewStateData Id="Assign_335" sap:VirtualizedContainerService.HintSize="916,62" />
      <sap2010:ViewStateData Id="Append_Line_83" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_336" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_153" sap:VirtualizedContainerService.HintSize="264,248">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_84" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_143" sap:VirtualizedContainerService.HintSize="490,402" />
      <sap2010:ViewStateData Id="Assign_337" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_85" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_154" sap:VirtualizedContainerService.HintSize="264,248">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_144" sap:VirtualizedContainerService.HintSize="780,556" />
      <sap2010:ViewStateData Id="Append_Line_86" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Catch`1_8" sap:VirtualizedContainerService.HintSize="784.666666666667,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_8" sap:VirtualizedContainerService.HintSize="916,794">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_155" sap:VirtualizedContainerService.HintSize="938,1746">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_145" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_146" sap:VirtualizedContainerService.HintSize="1072.66666666667,214">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_338" sap:VirtualizedContainerService.HintSize="798.666666666667,62" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_41" sap:VirtualizedContainerService.HintSize="798.666666666667,22" />
      <sap2010:ViewStateData Id="Assign_339" sap:VirtualizedContainerService.HintSize="798.666666666667,62" />
      <sap2010:ViewStateData Id="Append_Line_87" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_340" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_156" sap:VirtualizedContainerService.HintSize="264,248">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_88" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_147" sap:VirtualizedContainerService.HintSize="490,402" />
      <sap2010:ViewStateData Id="Append_Line_89" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_341" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_157" sap:VirtualizedContainerService.HintSize="264,248">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_148" sap:VirtualizedContainerService.HintSize="780,556" />
      <sap2010:ViewStateData Id="Append_Line_90" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Catch`1_9" sap:VirtualizedContainerService.HintSize="784.666666666667,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_9" sap:VirtualizedContainerService.HintSize="798.666666666667,794">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_158" sap:VirtualizedContainerService.HintSize="820.666666666667,1184">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_149" sap:VirtualizedContainerService.HintSize="946.666666666667,1338" />
      <sap2010:ViewStateData Id="If_150" sap:VirtualizedContainerService.HintSize="1072.66666666667,1492">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_342" sap:VirtualizedContainerService.HintSize="798.666666666667,62" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_42" sap:VirtualizedContainerService.HintSize="798.666666666667,22" />
      <sap2010:ViewStateData Id="Assign_343" sap:VirtualizedContainerService.HintSize="798.666666666667,62" />
      <sap2010:ViewStateData Id="Append_Line_91" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_344" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_159" sap:VirtualizedContainerService.HintSize="264,248">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_92" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_151" sap:VirtualizedContainerService.HintSize="490,402" />
      <sap2010:ViewStateData Id="Append_Line_93" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_345" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_160" sap:VirtualizedContainerService.HintSize="264,248">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_152" sap:VirtualizedContainerService.HintSize="780,556" />
      <sap2010:ViewStateData Id="Append_Line_94" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Catch`1_10" sap:VirtualizedContainerService.HintSize="784.666666666667,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_10" sap:VirtualizedContainerService.HintSize="798.666666666667,794">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_161" sap:VirtualizedContainerService.HintSize="820.666666666667,1184">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_153" sap:VirtualizedContainerService.HintSize="946.666666666667,1338" />
      <sap2010:ViewStateData Id="If_154" sap:VirtualizedContainerService.HintSize="1072.66666666667,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_162" sap:VirtualizedContainerService.HintSize="1094.66666666667,2215.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_155" sap:VirtualizedContainerService.HintSize="1220.66666666667,2369.33333333333" />
      <sap2010:ViewStateData Id="Assign_346" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="InvokeWorkflow_6" sap:VirtualizedContainerService.HintSize="712,22" />
      <sap2010:ViewStateData Id="Assign_518" sap:VirtualizedContainerService.HintSize="712,62" />
      <sap2010:ViewStateData Id="InvokeWorkflow_7" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Append_Line_102" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_519" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_285" sap:VirtualizedContainerService.HintSize="264,248">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_265" sap:VirtualizedContainerService.HintSize="464,402" />
      <sap2010:ViewStateData Id="Sequence_284" sap:VirtualizedContainerService.HintSize="486,588">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="IONAPIRequestWizard_44" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_264" sap:VirtualizedContainerService.HintSize="712,742" />
      <sap2010:ViewStateData Id="Sequence_283" sap:VirtualizedContainerService.HintSize="734,1030">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="IONAPIRequestWizard_43" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_263" sap:VirtualizedContainerService.HintSize="1000,1184" />
      <sap2010:ViewStateData Id="Assign_347" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_348" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_156" sap:VirtualizedContainerService.HintSize="510,216" />
      <sap2010:ViewStateData Id="Assign_520" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_268" sap:VirtualizedContainerService.HintSize="464,216" />
      <sap2010:ViewStateData Id="If_266" sap:VirtualizedContainerService.HintSize="1000,370" />
      <sap2010:ViewStateData Id="Assign_349" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_157" sap:VirtualizedContainerService.HintSize="1000,216" />
      <sap2010:ViewStateData Id="Sequence_163" sap:VirtualizedContainerService.HintSize="1022,1974">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_350" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_158" sap:VirtualizedContainerService.HintSize="1290,2128">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_351" sap:VirtualizedContainerService.HintSize="1290,62" />
      <sap2010:ViewStateData Id="Sequence_164" sap:VirtualizedContainerService.HintSize="1312,2354">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_159" sap:VirtualizedContainerService.HintSize="1220.66666666667,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_165" sap:VirtualizedContainerService.HintSize="1242.66666666667,3436">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_352" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_353" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_95" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_166" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_160" sap:VirtualizedContainerService.HintSize="1532.66666666667,3590" />
      <sap2010:ViewStateData Id="Sequence_167" sap:VirtualizedContainerService.HintSize="1554.66666666667,4676">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_168" sap:VirtualizedContainerService.HintSize="1576.66666666667,5282">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_354" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_355" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Append_Line_96" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_169" sap:VirtualizedContainerService.HintSize="264,348.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_161" sap:VirtualizedContainerService.HintSize="1866.66666666667,5436" />
      <sap2010:ViewStateData Id="Sequence_170" sap:VirtualizedContainerService.HintSize="1888.66666666667,5724">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_171" sap:VirtualizedContainerService.HintSize="1910.66666666667,6154">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_356" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_357" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_97" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_172" sap:VirtualizedContainerService.HintSize="264,346">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_162" sap:VirtualizedContainerService.HintSize="2200.66666666667,6308" />
      <sap2010:ViewStateData Id="Sequence_141" sap:VirtualizedContainerService.HintSize="2222.66666666667,6534">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_130" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_19" sap:VirtualizedContainerService.HintSize="264,380.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_118" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_119" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_12" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_45" sap:VirtualizedContainerService.HintSize="264,346">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_12" sap:VirtualizedContainerService.HintSize="628.666666666667,534.666666666667" />
      <sap2010:ViewStateData Id="Sequence_18" sap:VirtualizedContainerService.HintSize="650.666666666667,2063.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_120" sap:VirtualizedContainerService.HintSize="776.666666666667,2217.33333333333" />
      <sap2010:ViewStateData Id="Sequence_127" sap:VirtualizedContainerService.HintSize="798.666666666667,2341.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_17" sap:VirtualizedContainerService.HintSize="829.333333333333,2494" />
      <sap2010:ViewStateData Id="Sequence_126" sap:VirtualizedContainerService.HintSize="851.333333333333,2720">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_293" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_294" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_78" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_140" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_291" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_292" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_77" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_139" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_127" sap:VirtualizedContainerService.HintSize="554,504" />
      <sap2010:ViewStateData Id="If_10" sap:VirtualizedContainerService.HintSize="1431.33333333333,2874">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_259" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_260" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_69" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_118" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_261" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_262" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_70" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_119" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_112" sap:VirtualizedContainerService.HintSize="554,504" />
      <sap2010:ViewStateData Id="Assign_263" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_264" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_71" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_121" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_109" sap:VirtualizedContainerService.HintSize="844,658" />
      <sap2010:ViewStateData Id="Sequence_114" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_108" sap:VirtualizedContainerService.HintSize="1657.33333333333,3028" />
      <sap2010:ViewStateData Id="Sequence_8" sap:VirtualizedContainerService.HintSize="1679.33333333333,7044.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_6" sap:VirtualizedContainerService.HintSize="1969.33333333333,7198.66666666667" />
      <sap2010:ViewStateData Id="Sequence_23" sap:VirtualizedContainerService.HintSize="1991.33333333333,7424.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_116" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_117" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_11" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_44" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_15" sap:VirtualizedContainerService.HintSize="1088,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_16" sap:VirtualizedContainerService.HintSize="1110,3879.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_11" sap:VirtualizedContainerService.HintSize="1336,4033.33333333333" />
      <sap2010:ViewStateData Id="Sequence_22" sap:VirtualizedContainerService.HintSize="1358,4259.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_114" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_115" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_10" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_43" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_14" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_17" sap:VirtualizedContainerService.HintSize="264,1054.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="304,1134.66666666667" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>