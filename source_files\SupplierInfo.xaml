﻿<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow" this:Workflow.division="670" this:Workflow.company="999" this:Workflow.vendorID="113810" this:Workflow.tenantID="https://mingle-ionapi.inforcloudsuite.com/OLDCASTLE_DEV/"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI"
 xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys"
 xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities"
 xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json"
 xmlns:s="clr-namespace:System;assembly=mscorlib"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib"
 xmlns:this="clr-namespace:RehostedWorkflowDesigner"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="division" Type="InArgument(x:String)" />
    <x:Property Name="company" Type="InArgument(x:String)" />
    <x:Property Name="vendorID" Type="InArgument(x:String)" />
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="invoiceType" Type="OutArgument(x:String)" />
    <x:Property Name="GLCode" Type="OutArgument(x:String)" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Infor.Activities.IONAPI</x:String>
      <x:String>Infor.Activities.Sys</x:String>
      <x:String>Infor.RPA.Utilities</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>System</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>Microsoft.VisualBasic.CompilerServices</x:String>
      <x:String>Newtonsoft.Json</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>Infor.Activities.IONAPI</AssemblyReference>
      <AssemblyReference>Infor.Activities.Sys</AssemblyReference>
      <AssemblyReference>Infor.RPA.Utilities</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence DisplayName="Supplier info Sequence" sap2010:WorkflowViewState.IdRef="Sequence_1">
    <Sequence.Variables>
      <Variable x:TypeArguments="x:String" Name="vendorTextInput" />
      <Variable x:TypeArguments="njl:JToken" Name="vendorjsoninput" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="vendorresponseobj" />
      <Variable x:TypeArguments="s:String[]" Name="vendorResp1" />
      <Variable x:TypeArguments="x:String" Name="variable1" />
      <Variable x:TypeArguments="x:Int32" Name="respcode" />
    </Sequence.Variables>
    <Assign DisplayName="Initialise GLCode Assign" sap2010:WorkflowViewState.IdRef="Assign_5">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[GLCode]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">
          <Literal x:TypeArguments="x:String" Value="" />
        </InArgument>
      </Assign.Value>
    </Assign>
    <ias:Template_Apply ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="VendorID_divisionCompany Templating Activity" sap2010:WorkflowViewState.IdRef="Template_Apply_1" Template="{}{'data':[['{{%vendorID%}}_{{%division%}}{{%company%}}']]}" Text="[vendorTextInput]">
      <ias:Template_Apply.Values>
        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>vendorID</x:String>
            <x:String>company</x:String>
            <x:String>division</x:String>
          </scg:List>
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>vendorID</x:String>
            <x:String>company</x:String>
            <x:String>division</x:String>
          </scg:List>
        </scg:List>
      </ias:Template_Apply.Values>
    </ias:Template_Apply>
    <ias:DeserializeJSON ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="SupplierInfo Deserialize JSON" sap2010:WorkflowViewState.IdRef="DeserializeJSON_1" JTokenObject="[vendorjsoninput]" JTokenString="[vendorTextInput]" />
    <iai:IONAPIRequestWizard FileAttachments="{x:Null}" QueryParameters="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Supplier_Info" HttpMethod="POST" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_1" PostData="[vendorjsoninput.tostring]" Response="[vendorresponseobj]" StatusCode="[respcode]" Url="[tenantID + &quot;COLEMANAI/aiplatform/v1/endpoints/Supplier_Information/prediction&quot;]">
      <iai:IONAPIRequestWizard.Headers>
        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>Accept</x:String>
          </scg:List>
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>application/json</x:String>
          </scg:List>
        </scg:List>
      </iai:IONAPIRequestWizard.Headers>
    </iai:IONAPIRequestWizard>
    <If Condition="[respcode = 200]" DisplayName="Supplier infor resp is 200 If" sap2010:WorkflowViewState.IdRef="If_3">
      <If.Then>
        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_3">
          <Assign DisplayName="Assign invoice Type" sap2010:WorkflowViewState.IdRef="Assign_3">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[invoiceType]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[vendorresponseobj.ReadAsJson(0)(0).ToString]</InArgument>
            </Assign.Value>
          </Assign>
          <If Condition="[invoiceType = &quot;no values found&quot;]" DisplayName="Historical data exists If" sap2010:WorkflowViewState.IdRef="If_1">
            <If.Then>
              <Assign sap2010:WorkflowViewState.IdRef="Assign_2">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:String">[invoiceType]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:String">
                    <Literal x:TypeArguments="x:String" Value="" />
                  </InArgument>
                </Assign.Value>
              </Assign>
            </If.Then>
            <If.Else>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_2">
                <If Condition="[invoiceType = &quot;Expense Invoice&quot;]" DisplayName="Expense If" sap2010:WorkflowViewState.IdRef="If_2">
                  <If.Then>
                    <Assign DisplayName="Account dimensions Assign" sap2010:WorkflowViewState.IdRef="Assign_4">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[GLCode]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">[vendorresponseobj.ReadAsJson(0)(1).ToString]</InArgument>
                      </Assign.Value>
                    </Assign>
                  </If.Then>
                </If>
              </Sequence>
            </If.Else>
          </If>
        </Sequence>
      </If.Then>
      <If.Else>
        <Assign sap2010:WorkflowViewState.IdRef="Assign_6">
          <Assign.To>
            <OutArgument x:TypeArguments="x:String">[invoiceType]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:String">
              <Literal x:TypeArguments="x:String" Value="" />
            </InArgument>
          </Assign.Value>
        </Assign>
      </If.Else>
    </If>
    <sads:DebugSymbol.Symbol>d1hDOlxVc2Vyc1xkbWl0dGFwYWxsaVxBcHBEYXRhXExvY2FsXEluZm9yUlBBXE0zSW52b2ljZVByb2Nlc3NpbmdHZW5BSVYyXFN1cHBsaWVySW5mby54YW1sJwFuAXIBBQGKAQGOAQEEAacBAa4BAQMBxwEBgAIBAkgDsQEOAgEBUQVaDgIBLlsFahoCASprBWvqAQIBJWwFdx8CAR14Ba8BCgIBAlcLVzoCATFTMVM5AgEvW/8BW5ICAgEsW7wBW/kBAgEra9QBa+cBAgEoa7MBa8YBAgEmbJgCbLQCAgEkbL4CbNMCAgEibPACbMwDAgEgbN8CbOsCAgEeeBN4JQIBA3oJoQEUAgEJpAEJrQESAgEFewuCARQCARmDAQugARACAQqqAQ+qAT4CAQimATWmAUICAQaAATaAAWMCARx9N31EAgEagwEZgwFGAgELhQEPjgEYAgEVkQEPngEaAgENiwEViwFEAgEYhwE7hwFIAgEWkgERnQEWAgEOkgEfkgFMAgEPlAEVmwEeAgERmQFAmQFtAgEUlgFBlgFJAgES</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="Assign_5" sap:VirtualizedContainerService.HintSize="1044,62" />
      <sap2010:ViewStateData Id="Template_Apply_1" sap:VirtualizedContainerService.HintSize="1044,22" />
      <sap2010:ViewStateData Id="DeserializeJSON_1" sap:VirtualizedContainerService.HintSize="1044,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_1" sap:VirtualizedContainerService.HintSize="1044,22" />
      <sap2010:ViewStateData Id="Assign_3" sap:VirtualizedContainerService.HintSize="754,62" />
      <sap2010:ViewStateData Id="Assign_2" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_4" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_2" sap:VirtualizedContainerService.HintSize="464,216" />
      <sap2010:ViewStateData Id="Sequence_2" sap:VirtualizedContainerService.HintSize="486,340">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_1" sap:VirtualizedContainerService.HintSize="754,494" />
      <sap2010:ViewStateData Id="Sequence_3" sap:VirtualizedContainerService.HintSize="776,720">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_6" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_3" sap:VirtualizedContainerService.HintSize="1044,874" />
      <sap2010:ViewStateData Id="Sequence_1" sap:VirtualizedContainerService.HintSize="1066,1286">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="1106,1526" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>