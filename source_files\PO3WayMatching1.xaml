﻿<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:iad="clr-namespace:Infor.Activities.Debug;assembly=Infor.Activities.Debug"
 xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI"
 xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys"
 xmlns:iaw="clr-namespace:Infor.Activities.Workflow;assembly=Infor.Activities.Workflow"
 xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities"
 xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json"
 xmlns:s="clr-namespace:System;assembly=mscorlib"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="includeDatalake" Type="InArgument(x:Boolean)" />
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="commentStatus" Type="OutArgument(x:String)" />
    <x:Property Name="Status" Type="OutArgument(x:String)" />
    <x:Property Name="DictOcrValues" Type="InArgument(scg:Dictionary(x:String, x:Object))" />
    <x:Property Name="vendorId" Type="InArgument(x:String)" />
    <x:Property Name="projectPath" Type="InArgument(x:String)" />
    <x:Property Name="company" Type="InArgument(x:String)" />
    <x:Property Name="division" Type="InArgument(x:String)" />
    <x:Property Name="APResp" Type="InArgument(x:String)" />
    <x:Property Name="correlationID" Type="OutArgument(x:String)" />
    <x:Property Name="authUser" Type="InArgument(x:String)" />
    <x:Property Name="miscValues" Type="InArgument(scg:Dictionary(x:String, x:Object))" />
    <x:Property Name="vatCodeConfig" Type="InArgument(x:String)" />
    <x:Property Name="chargeCode" Type="InArgument(x:String)" />
    <x:Property Name="ListocrLineValues" Type="InArgument(scg:List(scg:Dictionary(x:String, x:Object)))" />
    <x:Property Name="logFile" Type="InArgument(x:String)" />
    <x:Property Name="approvalList" Type="OutArgument(scg:List(x:String))" />
    <x:Property Name="vendorName" Type="OutArgument(x:String)" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Debug</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Infor.Activities.IONAPI</x:String>
      <x:String>Infor.Activities.Sys</x:String>
      <x:String>Infor.Activities.Workflow</x:String>
      <x:String>Infor.RPA.Utilities</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>System</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Linq</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>Microsoft.VisualBasic.CompilerServices</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Debug</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>Infor.Activities.IONAPI</AssemblyReference>
      <AssemblyReference>Infor.Activities.Sys</AssemblyReference>
      <AssemblyReference>Infor.Activities.Workflow</AssemblyReference>
      <AssemblyReference>Infor.RPA.Utilities</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>System.Collections.Immutable</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_46">
    <Sequence.Variables>
      <Variable x:TypeArguments="x:String" Name="inbnValue" />
      <Variable x:TypeArguments="scg:List(x:String)" Name="PONumbers" />
      <Variable x:TypeArguments="x:String" Name="pono" />
      <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="POocrWorkflowOutput" />
      <Variable x:TypeArguments="x:Int32" Name="POocrWorkflowStatus" />
      <Variable x:TypeArguments="x:String" Name="ivdate" />
      <Variable x:TypeArguments="scg:List(x:String)" Name="vendorResult" />
      <Variable x:TypeArguments="x:String" Name="cuam" />
      <Variable x:TypeArguments="x:String" Name="sino" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="respObj0" />
      <Variable x:TypeArguments="x:Int32" Name="StatusCode0" />
      <Variable x:TypeArguments="x:String" Name="tepy" />
      <Variable x:TypeArguments="x:String" Name="req2" />
      <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="linesOcrWorkflowOutput" />
      <Variable x:TypeArguments="x:Int32" Name="linesOcrWorkflowStatus" />
      <Variable x:TypeArguments="x:String" Name="diffamt" />
      <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="AddLineOcrWorkflowOutput" />
      <Variable x:TypeArguments="x:Int32" Name="AddLineOcrWorkflowStatus" />
      <Variable x:TypeArguments="x:String" Name="chargeDiff" />
      <Variable x:TypeArguments="x:String" Name="countryCode" />
      <Variable x:TypeArguments="x:String" Name="bkid" />
      <Variable x:TypeArguments="x:Boolean" Name="InvoiceAlreadyExists" />
      <Variable x:TypeArguments="x:String" Name="authUsertemp" />
      <Variable x:TypeArguments="x:String" Name="cucd" />
      <Variable x:TypeArguments="x:String" Name="pyme" />
      <Variable x:TypeArguments="x:String" Name="geoc" />
      <Variable x:TypeArguments="scg:List(s:String[])" Name="M3TotalTableRows" />
      <Variable x:TypeArguments="scg:List(s:String[])" Name="M3TotalTableRows1" />
      <Variable x:TypeArguments="x:Int32" Default="0" Name="Itr" />
      <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="debitNoteDict" />
      <Variable x:TypeArguments="x:Int32" Name="debitNoteStatusCode" />
    </Sequence.Variables>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_119">
      <Assign.To>
        <OutArgument x:TypeArguments="scg:List(x:String)">[approvalList]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="scg:List(x:String)">[new list(Of string)]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_120">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[vendorName]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">
          <Literal x:TypeArguments="x:String" Value="" />
        </InArgument>
      </Assign.Value>
    </Assign>
    <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" Title="{x:Null}" ContinueOnError="True" DisplayName="Message Box" sap2010:WorkflowViewState.IdRef="MessageBox_1" Selection="OK" Text="po3way match Start" />
    <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" Title="{x:Null}" ContinueOnError="True" DisplayName="Message Box" sap2010:WorkflowViewState.IdRef="MessageBox_5" Selection="OK" Text="[respObj0.ReadAsText]" />
    <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for vendor name" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_1" Response="[respObj0]" StatusCode="[StatusCode0]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/CRS620MI/GetBasicData&quot;]">
      <iai:IONAPIRequestWizard.Headers>
        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>Accept</x:String>
          </scg:List>
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>application/json</x:String>
          </scg:List>
        </scg:List>
      </iai:IONAPIRequestWizard.Headers>
      <iai:IONAPIRequestWizard.QueryParameters>
        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
          <scg:List x:TypeArguments="x:String" Capacity="8">
            <x:String>extendedresult</x:String>
            <x:String>format</x:String>
            <x:String>righttrim</x:String>
            <x:String>excludeempty</x:String>
            <x:String>dateformat</x:String>
            <x:String>SUNO</x:String>
          </scg:List>
          <scg:List x:TypeArguments="x:String" Capacity="8">
            <x:String>false</x:String>
            <x:String>PRETTY</x:String>
            <x:String>true</x:String>
            <x:String>false</x:String>
            <x:String>YMD8</x:String>
            <x:String>vendorId</x:String>
          </scg:List>
        </scg:List>
      </iai:IONAPIRequestWizard.QueryParameters>
    </iai:IONAPIRequestWizard>
    <If Condition="[StatusCode0 = 200]" DisplayName="If fetching vendor details" sap2010:WorkflowViewState.IdRef="If_4">
      <If.Then>
        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_8">
          <Sequence.Variables>
            <Variable x:TypeArguments="njl:JToken" Name="out0" />
          </Sequence.Variables>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_1">
            <Assign.To>
              <OutArgument x:TypeArguments="njl:JToken">[out0]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj0.ReadAsText)]</InArgument>
            </Assign.Value>
          </Assign>
          <If Condition="[out0(&quot;results&quot;)(0)(&quot;records&quot;).ToString &lt;&gt; &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_3">
            <If.Then>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_6">
                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_1">
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_2">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[tepy]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[out0("results")(0)("records")(0)("TEPY").ToString]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_3">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[pyme]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[out0("results")(0)("records")(0)("PYME").ToString]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_4">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[countryCode]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[out0("results")(0)("records")(0)("CSCD").ToString]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_5">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[cucd]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[out0("results")(0)("records")(0)("CUCD").ToString]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_6">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[vendorName]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[out0("results")(0)("records")(0)("SUNM").ToString]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_7">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[geoc]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[out0("results")(0)("records")(0)("GEOC").ToString]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_83">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[authUsertemp]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[out0("results")(0)("records")(0)("RESP").ToString]</InArgument>
                    </Assign.Value>
                  </Assign>
                </Sequence>
                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_5">
                  <Sequence.Variables>
                    <Variable x:TypeArguments="x:String" Name="SupAcho" />
                    <Variable x:TypeArguments="x:Int32" Name="StatusCode4" />
                    <Variable x:TypeArguments="iru:ResponseObject" Name="respObj4" />
                    <Variable x:TypeArguments="njl:JToken" Name="out4" />
                    <Variable x:TypeArguments="s:DateTime" Name="ivdate1" />
                  </Sequence.Variables>
                  <Assign DisplayName="Assign SupAcho" sap2010:WorkflowViewState.IdRef="Assign_8">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[SupAcho]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">["ACHO:"+vendorId + " AND BKTP: 03"]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for bkid" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_2" Response="[respObj4]" StatusCode="[StatusCode4]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/CRS692MI/SearchBnkAcc&quot;]">
                    <iai:IONAPIRequestWizard.Headers>
                      <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                        <scg:List x:TypeArguments="x:String" Capacity="4">
                          <x:String>Accept</x:String>
                        </scg:List>
                        <scg:List x:TypeArguments="x:String" Capacity="4">
                          <x:String>application/json</x:String>
                        </scg:List>
                      </scg:List>
                    </iai:IONAPIRequestWizard.Headers>
                    <iai:IONAPIRequestWizard.QueryParameters>
                      <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                        <scg:List x:TypeArguments="x:String" Capacity="8">
                          <x:String>SQRY</x:String>
                          <x:String>dateformat</x:String>
                          <x:String>excludeempty</x:String>
                          <x:String>righttrim</x:String>
                          <x:String>format</x:String>
                          <x:String>extendedresult</x:String>
                        </scg:List>
                        <scg:List x:TypeArguments="x:String" Capacity="8">
                          <x:String>SupAcho</x:String>
                          <x:String>YMD8</x:String>
                          <x:String>false</x:String>
                          <x:String>true</x:String>
                          <x:String>PRETTY</x:String>
                          <x:String>false</x:String>
                        </scg:List>
                      </scg:List>
                    </iai:IONAPIRequestWizard.QueryParameters>
                  </iai:IONAPIRequestWizard>
                  <If Condition="[StatusCode4 = 200]" sap2010:WorkflowViewState.IdRef="If_2">
                    <If.Then>
                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_4">
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_9">
                          <Assign.To>
                            <OutArgument x:TypeArguments="njl:JToken">[out4]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj4.ReadAsText)]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <If Condition="[out4(&quot;results&quot;)(0)(&quot;records&quot;).ToString &lt;&gt; &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_1">
                          <If.Then>
                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_2">
                              <Assign DisplayName="Assign bkid" sap2010:WorkflowViewState.IdRef="Assign_10">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[bkid]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[(out4("results")(0)("records")(0)("BKID")).ToString]</InArgument>
                                </Assign.Value>
                              </Assign>
                            </Sequence>
                          </If.Then>
                          <If.Else>
                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_3">
                              <Assign DisplayName="Assign bkid" sap2010:WorkflowViewState.IdRef="Assign_11">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[bkid]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">
                                    <Literal x:TypeArguments="x:String" Value="" />
                                  </InArgument>
                                </Assign.Value>
                              </Assign>
                            </Sequence>
                          </If.Else>
                        </If>
                      </Sequence>
                    </If.Then>
                  </If>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_12">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[cuam]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[DictOcrValues("TOTAL").Tostring]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_13">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[sino]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[DictOcrValues("INVOICE_RECEIPT_ID").Tostring]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign DisplayName="Assign ivdate" sap2010:WorkflowViewState.IdRef="Assign_14">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[ivdate]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[DateTime.ParseExact(DictOcrValues("INVOICE_RECEIPT_DATE").Tostring, "dd/MM/yyyy", Nothing).ToString("yyyyMMdd")]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign DisplayName="Assign coID" sap2010:WorkflowViewState.IdRef="Assign_15">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[correlationID]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[System.DateTime.Now.ToString("yyyyMMddHHmmssfff").tostring + " -"+ DictOcrValues("INVOICE_RECEIPT_ID").Tostring + "-" + division + "-" + vendorID]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_1">
                    <TryCatch.Try>
                      <Assign DisplayName="Assign coID" sap2010:WorkflowViewState.IdRef="Assign_16">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[correlationID]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[correlationID.Substring(0,35)]</InArgument>
                        </Assign.Value>
                      </Assign>
                    </TryCatch.Try>
                    <TryCatch.Catches>
                      <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_1">
                        <ActivityAction x:TypeArguments="s:Exception">
                          <ActivityAction.Argument>
                            <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                          </ActivityAction.Argument>
                          <Assign DisplayName="Assign coID" sap2010:WorkflowViewState.IdRef="Assign_17">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:String">[correlationID]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String">[correlationID]</InArgument>
                            </Assign.Value>
                          </Assign>
                        </ActivityAction>
                      </Catch>
                    </TryCatch.Catches>
                  </TryCatch>
                </Sequence>
              </Sequence>
            </If.Then>
            <If.Else>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_7">
                <Assign sap2010:WorkflowViewState.IdRef="Assign_18">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[(out0("results")(0)("errorMessage")).ToString]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_19">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
                  </Assign.Value>
                </Assign>
                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_1" Line="[commentStatus]" Source="[logfile]" />
              </Sequence>
            </If.Else>
          </If>
        </Sequence>
      </If.Then>
      <If.Else>
        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_9">
          <Assign sap2010:WorkflowViewState.IdRef="Assign_20">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">["Received an IONAPI error while fetching Vendor name for the Vendor Id " + vendorId+"."]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_21">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
            </Assign.Value>
          </Assign>
          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_2" Line="[commentStatus]" Source="[logfile]" />
        </Sequence>
      </If.Else>
    </If>
    <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" Title="{x:Null}" ContinueOnError="True" DisplayName="Message Box" sap2010:WorkflowViewState.IdRef="MessageBox_4" Selection="OK" Text="[&quot;bkid &quot; + bkid +&quot; cuam &quot;+cuam +&quot; cucd &quot;+cucd]" />
    <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;DictOcrValues&quot;,DictOcrValues},{&quot;includeDatalake&quot;,includeDatalake},{&quot;logFile&quot;,logFile},{&quot;projectPath&quot;,projectPath},{&quot;company&quot;,company},{&quot;authUser&quot;,authUser},{&quot;APResp&quot;,APResp},{&quot;division&quot;,division},{&quot;ListocrLineValues&quot;,ListocrLineValues},{&quot;vatCodeConfig&quot;,vatCodeConfig},{&quot;miscValues&quot;,miscValues},{&quot;vendorId&quot;,vendorId}}]" ContinueOnError="True" DisplayName="Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_12" OutputArguments="[debitNoteDict]" ResponseCode="[debitNoteStatusCode]" WorkflowFile="[projectPath+&quot;\DebitNoteCreation.xaml&quot;]" />
    <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Message Box" sap2010:WorkflowViewState.IdRef="MessageBox_27" Selection="OK" Text="[debitNoteStatusCode.ToString]" Title="debitNoteStatusCode" />
    <If Condition="[miscValues(&quot;BusinessRuleAPResp&quot;).ToString.ToUpper() &lt;&gt; &quot;NA&quot;]" sap2010:WorkflowViewState.IdRef="If_7">
      <If.Then>
        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_10">
          <Sequence.Variables>
            <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="APRespRespDict" />
            <Variable x:TypeArguments="x:Int32" Name="APRespRespCode" />
          </Sequence.Variables>
          <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;division&quot;,division},{&quot;company&quot;,company},{&quot;VendorID&quot;,vendorID}}]" ContinueOnError="True" DisplayName="AP resp Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_1" OutputArguments="[APRespRespDict]" ResponseCode="[APRespRespCode]" WorkflowFile="[projectPath+&quot;\APResp.xaml&quot;]" />
          <If Condition="[APRespRespCode = 200]" sap2010:WorkflowViewState.IdRef="If_5">
            <If.Then>
              <Assign sap2010:WorkflowViewState.IdRef="Assign_22">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:String">[APResp]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:String">[CType(APRespRespDict("APResp"), String)]</InArgument>
                </Assign.Value>
              </Assign>
            </If.Then>
          </If>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_23">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[authUser]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[APResp]</InArgument>
            </Assign.Value>
          </Assign>
        </Sequence>
      </If.Then>
      <If.Else>
        <If Condition="[authUser.ToUpper() = &quot;SUPPLIERAUTHUSER&quot;]" sap2010:WorkflowViewState.IdRef="If_6">
          <If.Then>
            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_11">
              <Assign sap2010:WorkflowViewState.IdRef="Assign_24">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:String">[authUser]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:String">[authUsertemp]</InArgument>
                </Assign.Value>
              </Assign>
            </Sequence>
          </If.Then>
        </If>
      </If.Else>
    </If>
    <Sequence DisplayName="Sequence creating header" sap2010:WorkflowViewState.IdRef="Sequence_16">
      <Sequence.Variables>
        <Variable x:TypeArguments="x:Int32" Name="StatusCode5" />
        <Variable x:TypeArguments="iru:ResponseObject" Name="respObj5" />
        <Variable x:TypeArguments="njl:JToken" Name="out5" />
      </Sequence.Variables>
      <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Invoice header" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_3" Response="[respObj5]" StatusCode="[StatusCode5]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/AddHead&quot;]">
        <iai:IONAPIRequestWizard.Headers>
          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
            <scg:List x:TypeArguments="x:String" Capacity="4">
              <x:String>Accept</x:String>
            </scg:List>
            <scg:List x:TypeArguments="x:String" Capacity="4">
              <x:String>application/json</x:String>
            </scg:List>
          </scg:List>
        </iai:IONAPIRequestWizard.Headers>
        <iai:IONAPIRequestWizard.QueryParameters>
          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
            <scg:List x:TypeArguments="x:String" Capacity="32">
              <x:String>SUNO</x:String>
              <x:String>IVDT</x:String>
              <x:String>DIVI</x:String>
              <x:String>SINO</x:String>
              <x:String>CUCD</x:String>
              <x:String>TEPY</x:String>
              <x:String>PYME</x:String>
              <x:String>CUAM</x:String>
              <x:String>IMCD</x:String>
              <x:String>CRTP</x:String>
              <x:String>dateformat</x:String>
              <x:String>excludeempty</x:String>
              <x:String>righttrim</x:String>
              <x:String>format</x:String>
              <x:String>extendedresult</x:String>
              <x:String>APCD</x:String>
              <x:String>CORI</x:String>
            </scg:List>
            <scg:List x:TypeArguments="x:String" Capacity="32">
              <x:String>vendorId</x:String>
              <x:String>ivdate</x:String>
              <x:String>division</x:String>
              <x:String>sino</x:String>
              <x:String>cucd</x:String>
              <x:String>tepy</x:String>
              <x:String>pyme</x:String>
              <x:String>cuam</x:String>
              <x:String>1</x:String>
              <x:String>1</x:String>
              <x:String>YMD8</x:String>
              <x:String>false</x:String>
              <x:String>true</x:String>
              <x:String>PRETTY</x:String>
              <x:String>false</x:String>
              <x:String>authUser</x:String>
              <x:String>correlationID</x:String>
            </scg:List>
          </scg:List>
        </iai:IONAPIRequestWizard.QueryParameters>
      </iai:IONAPIRequestWizard>
      <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" Title="{x:Null}" ContinueOnError="True" DisplayName="Message Box" sap2010:WorkflowViewState.IdRef="MessageBox_2" Selection="OK" Text="[respObj5.ReadAsText]" />
      <If Condition="[StatusCode5 = 200  and respObj5.ReadAsText.Contains(&quot;Bank account identity must be entered&quot;)]" sap2010:WorkflowViewState.IdRef="If_8">
        <If.Then>
          <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Invoice header" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_4" Response="[respObj5]" StatusCode="[StatusCode5]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/AddHead&quot;]">
            <iai:IONAPIRequestWizard.Headers>
              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>Accept</x:String>
                </scg:List>
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>application/json</x:String>
                </scg:List>
              </scg:List>
            </iai:IONAPIRequestWizard.Headers>
            <iai:IONAPIRequestWizard.QueryParameters>
              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                <scg:List x:TypeArguments="x:String" Capacity="32">
                  <x:String>SUNO</x:String>
                  <x:String>IVDT</x:String>
                  <x:String>DIVI</x:String>
                  <x:String>SINO</x:String>
                  <x:String>CUCD</x:String>
                  <x:String>TEPY</x:String>
                  <x:String>PYME</x:String>
                  <x:String>CUAM</x:String>
                  <x:String>IMCD</x:String>
                  <x:String>CRTP</x:String>
                  <x:String>dateformat</x:String>
                  <x:String>excludeempty</x:String>
                  <x:String>righttrim</x:String>
                  <x:String>format</x:String>
                  <x:String>extendedresult</x:String>
                  <x:String>APCD</x:String>
                  <x:String>BKID</x:String>
                  <x:String>CORI</x:String>
                </scg:List>
                <scg:List x:TypeArguments="x:String" Capacity="32">
                  <x:String>vendorId</x:String>
                  <x:String>ivdate</x:String>
                  <x:String>division</x:String>
                  <x:String>sino</x:String>
                  <x:String>cucd</x:String>
                  <x:String>tepy</x:String>
                  <x:String>pyme</x:String>
                  <x:String>cuam</x:String>
                  <x:String>1</x:String>
                  <x:String>1</x:String>
                  <x:String>YMD8</x:String>
                  <x:String>false</x:String>
                  <x:String>true</x:String>
                  <x:String>PRETTY</x:String>
                  <x:String>false</x:String>
                  <x:String>authUser</x:String>
                  <x:String>bkid</x:String>
                  <x:String>correlationID</x:String>
                </scg:List>
              </scg:List>
            </iai:IONAPIRequestWizard.QueryParameters>
          </iai:IONAPIRequestWizard>
        </If.Then>
      </If>
      <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" Title="{x:Null}" ContinueOnError="True" DisplayName="Message Box" sap2010:WorkflowViewState.IdRef="MessageBox_3" Selection="OK" Text="[respObj5.ReadAsText]" />
      <If Condition="[StatusCode5 = 200]" sap2010:WorkflowViewState.IdRef="If_11">
        <If.Then>
          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_14">
            <Sequence.Variables>
              <Variable x:TypeArguments="x:Int32" Name="respout1" />
              <Variable x:TypeArguments="x:String" Name="additionalChargeExcp" />
              <Variable x:TypeArguments="x:Int32" Name="m" />
            </Sequence.Variables>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_25">
              <Assign.To>
                <OutArgument x:TypeArguments="njl:JToken">[out5]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj5.ReadAsText)]</InArgument>
              </Assign.Value>
            </Assign>
            <If Condition="[out5(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_10">
              <If.Then>
                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_12">
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_26">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:Boolean">[InvoiceAlreadyExists]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_27">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[inbnValue]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">
                        <Literal x:TypeArguments="x:String" Value="" />
                      </InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_28">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[(out5("results")(0)("errorMessage")).ToString]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <If Condition="[respObj5.ReadAsText.Contains(&quot;Bank account identity must be entered&quot;)]" sap2010:WorkflowViewState.IdRef="If_9">
                    <If.Then>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_29">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">NEEDSVERIFICATION</InArgument>
                        </Assign.Value>
                      </Assign>
                    </If.Then>
                    <If.Else>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_30">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                        </Assign.Value>
                      </Assign>
                    </If.Else>
                  </If>
                </Sequence>
              </If.Then>
              <If.Else>
                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_13">
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_31">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:Boolean">[InvoiceAlreadyExists]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_32">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[inbnValue]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[(out5("results")(0)("records")(0)("INBN")).ToString]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_33">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">["Invoice Header Created"]</InArgument>
                    </Assign.Value>
                  </Assign>
                </Sequence>
              </If.Else>
            </If>
          </Sequence>
        </If.Then>
        <If.Else>
          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_15">
            <Assign sap2010:WorkflowViewState.IdRef="Assign_34">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">["Received an IONAPI error while adding Invoice header."]</InArgument>
              </Assign.Value>
            </Assign>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_35">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
              </Assign.Value>
            </Assign>
            <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_3" Line="[commentStatus]" Source="[logfile]" />
          </Sequence>
        </If.Else>
      </If>
    </Sequence>
    <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Message Box" sap2010:WorkflowViewState.IdRef="MessageBox_6" Selection="OK" Text="[InvoiceAlreadyExists.ToString]" Title="InvoiceAlreadyExists" />
    <If Condition="[InvoiceAlreadyExists AND includeDatalake]" sap2010:WorkflowViewState.IdRef="If_28">
      <If.Then>
        <If Condition="[includeDatalake]" sap2010:WorkflowViewState.IdRef="If_19">
          <If.Then>
            <Sequence DisplayName="Sequence invoice from Excep UI" sap2010:WorkflowViewState.IdRef="Sequence_22">
              <Sequence.Variables>
                <Variable x:TypeArguments="scg:List(s:String[])" Name="M3TotalTableRows" />
                <Variable x:TypeArguments="x:Decimal" Name="lineAmt" />
                <Variable x:TypeArguments="x:String" Name="variable1" />
                <Variable x:TypeArguments="x:Decimal" Name="qty" />
              </Sequence.Variables>
              <Assign sap2010:WorkflowViewState.IdRef="Assign_36">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:String">[req2]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:String" xml:space="preserve">["F2PNLI,F2REPN,F2SCOC,F2RPQA,F2TRDT,F2ITNO,F2CONO,F2CUCD,F2DIVI,F2SUNO,F2IVQA,F2PUNO,F2SUDO,F2RCAC,F2RPQT,F2SCOP,F2SERA,F2IVQT,F2ICAC  from FGRECL where F2DIVI = " + DIVISION+" AND F2PUNO = " + pono + " and F2IMST != 9 "]</InArgument>
                </Assign.Value>
              </Assign>
              <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;logfile&quot;,logfile},{&quot;miscValues&quot;,miscValues},{&quot;req2&quot;,req2}}]" ContinueOnError="True" DisplayName="Lines extraction Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_2" OutputArguments="[linesOcrWorkflowOutput]" ResponseCode="[linesOcrWorkflowStatus]" WorkflowFile="[projectPath+&quot;\LinesExtractionWithPO.xaml&quot;]" />
              <If Condition="[linesOcrWorkflowStatus = 200]" sap2010:WorkflowViewState.IdRef="If_12">
                <If.Then>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_37">
                    <Assign.To>
                      <OutArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="scg:List(s:String[])">[CType(linesOcrWorkflowOutput("M3TotalTableRows"), List(Of String()))]</InArgument>
                    </Assign.Value>
                  </Assign>
                </If.Then>
              </If>
              <Assign sap2010:WorkflowViewState.IdRef="Assign_38">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:Decimal">[lineAmt]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:Decimal">[0]</InArgument>
                </Assign.Value>
              </Assign>
              <Assign sap2010:WorkflowViewState.IdRef="Assign_39">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:Decimal">[qty]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:Decimal">[0]</InArgument>
                </Assign.Value>
              </Assign>
              <ForEach x:TypeArguments="s:String[]" DisplayName="ForEach&lt;String[]&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_1" Values="[M3TotalTableRows]">
                <ActivityAction x:TypeArguments="s:String[]">
                  <ActivityAction.Argument>
                    <DelegateInArgument x:TypeArguments="s:String[]" Name="rows" />
                  </ActivityAction.Argument>
                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_17">
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_40">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:Decimal">[lineAmt]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:Decimal">[lineAmt +convert.ToDecimal(rows(2))*convert.ToDecimal(rows(3))]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_41">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:Decimal">[qty]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:Decimal">[qty + convert.ToDecimal(rows(3))]</InArgument>
                      </Assign.Value>
                    </Assign>
                  </Sequence>
                </ActivityAction>
              </ForEach>
              <Assign sap2010:WorkflowViewState.IdRef="Assign_42">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:String">[diffamt]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:String">[((convert.ToDecimal(DictOcrValues("TOTAL").ToString) - lineAmt)/qty).ToString]</InArgument>
                </Assign.Value>
              </Assign>
              <If Condition="[diffamt &lt;&gt; &quot;0&quot;]" sap2010:WorkflowViewState.IdRef="If_13">
                <If.Then>
                  <ForEach x:TypeArguments="x:Int32" DisplayName="ForEach&lt;Int32&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_2" Values="[Enumerable.Range(0,M3TotalTableRows.count)]">
                    <ActivityAction x:TypeArguments="x:Int32">
                      <ActivityAction.Argument>
                        <DelegateInArgument x:TypeArguments="x:Int32" Name="i" />
                      </ActivityAction.Argument>
                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_18">
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_43">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[M3TotalTableRows(i)(2)]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[(convert.ToDecimal(M3TotalTableRows(i)(2)) + convert.Todecimal(diffamt)).ToString]</InArgument>
                          </Assign.Value>
                        </Assign>
                      </Sequence>
                    </ActivityAction>
                  </ForEach>
                </If.Then>
              </If>
              <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;logfile&quot;,logfile},{&quot;M3TotalTableRows&quot;,M3TotalTableRows},{&quot;vatCodeConfig&quot;,vatCodeConfig},{&quot;division&quot;,division},{&quot;inbnValue&quot;,inbnValue}}]" ContinueOnError="True" DisplayName="Add Lines Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_3" OutputArguments="[AddLineOcrWorkflowOutput]" ResponseCode="[AddLineOcrWorkflowStatus]" WorkflowFile="[projectPath+&quot;\AddLine.xaml&quot;]" />
              <Assign sap2010:WorkflowViewState.IdRef="Assign_44">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:String">[chargeDiff]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:String">0</InArgument>
                </Assign.Value>
              </Assign>
              <If Condition="[DictOcrValues(&quot;SHIPPING_AND_HANDLING&quot;).ToString &lt;&gt; &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_15">
                <If.Then>
                  <If Condition="[CInt(DictOcrValues(&quot;SHIPPING_AND_HANDLING&quot;).ToString) &lt;&gt; 0]" sap2010:WorkflowViewState.IdRef="If_14">
                    <If.Then>
                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_19">
                        <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;logfile&quot;,logfile},{&quot;M3TotalTableRows&quot;,M3TotalTableRows},{&quot;division&quot;,division},{&quot;inbnValue&quot;,inbnValue},{&quot;charge&quot;,DictOcrValues(&quot;SHIPPING_AND_HANDLING&quot;).ToString},{&quot;chargeCode&quot;,chargeCode}}]" ContinueOnError="True" DisplayName="Add Charges Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_4" OutputArguments="[AddLineOcrWorkflowOutput]" ResponseCode="[AddLineOcrWorkflowStatus]" WorkflowFile="[projectPath+&quot;\AddCharge.xaml&quot;]" />
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_45">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[chargeDiff]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[Ctype(AddLineOcrWorkflowOutput("chargeDiff"),String)]</InArgument>
                          </Assign.Value>
                        </Assign>
                      </Sequence>
                    </If.Then>
                  </If>
                </If.Then>
              </If>
              <If Condition="[((DictOcrValues(&quot;VAT_AMOUNT&quot;).Tostring.contains(&quot;,&quot;) AND DictOcrValues(&quot;VAT_PERCENTAGE&quot;).Tostring.contains(&quot;%&quot;)) OR (Cint(DictOcrValues(&quot;VAT_AMOUNT&quot;).Tostring)&lt;&gt;0) )]" sap2010:WorkflowViewState.IdRef="If_17">
                <If.Then>
                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_20">
                    <Sequence.Variables>
                      <Variable x:TypeArguments="x:String" Name="vat" />
                      <Variable x:TypeArguments="iru:ResponseObject" Name="respout1" />
                      <Variable x:TypeArguments="x:String" Name="vatCode" />
                    </Sequence.Variables>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_46">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[vat]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">[DictOcrValues("VAT_AMOUNT").Tostring]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <iad:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_1">
                      <iad:CommentOut.Activities>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_47">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[vatCode]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[miscValues("vatCodeConfig").ToString]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <If Condition="[miscValues(&quot;vatCodeConfig&quot;).ToString = &quot;NA&quot;]" sap2010:WorkflowViewState.IdRef="If_16">
                          <If.Then>
                            <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding VAT" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_5" Response="[respout1]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                              <iai:IONAPIRequestWizard.Headers>
                                <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                  <scg:List x:TypeArguments="x:String" Capacity="4">
                                    <x:String>Accept</x:String>
                                  </scg:List>
                                  <scg:List x:TypeArguments="x:String" Capacity="4">
                                    <x:String>application/json</x:String>
                                  </scg:List>
                                </scg:List>
                              </iai:IONAPIRequestWizard.Headers>
                              <iai:IONAPIRequestWizard.QueryParameters>
                                <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                  <scg:List x:TypeArguments="x:String" Capacity="4">
                                    <x:String>INBN</x:String>
                                    <x:String>RDTP</x:String>
                                    <x:String>DIVI</x:String>
                                    <x:String>GLAM</x:String>
                                  </scg:List>
                                  <scg:List x:TypeArguments="x:String" Capacity="4">
                                    <x:String>inbnValue</x:String>
                                    <x:String>3</x:String>
                                    <x:String>division</x:String>
                                    <x:String>vat</x:String>
                                  </scg:List>
                                </scg:List>
                              </iai:IONAPIRequestWizard.QueryParameters>
                            </iai:IONAPIRequestWizard>
                          </If.Then>
                          <If.Else>
                            <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding VAT" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_6" Response="[respout1]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                              <iai:IONAPIRequestWizard.Headers>
                                <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                  <scg:List x:TypeArguments="x:String" Capacity="4">
                                    <x:String>Accept</x:String>
                                  </scg:List>
                                  <scg:List x:TypeArguments="x:String" Capacity="4">
                                    <x:String>application/json</x:String>
                                  </scg:List>
                                </scg:List>
                              </iai:IONAPIRequestWizard.Headers>
                              <iai:IONAPIRequestWizard.QueryParameters>
                                <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                  <scg:List x:TypeArguments="x:String" Capacity="8">
                                    <x:String>INBN</x:String>
                                    <x:String>RDTP</x:String>
                                    <x:String>DIVI</x:String>
                                    <x:String>VTA1</x:String>
                                    <x:String>VTCD</x:String>
                                  </scg:List>
                                  <scg:List x:TypeArguments="x:String" Capacity="8">
                                    <x:String>inbnValue</x:String>
                                    <x:String>3</x:String>
                                    <x:String>division</x:String>
                                    <x:String>vat</x:String>
                                    <x:String>vatCode</x:String>
                                  </scg:List>
                                </scg:List>
                              </iai:IONAPIRequestWizard.QueryParameters>
                            </iai:IONAPIRequestWizard>
                          </If.Else>
                        </If>
                      </iad:CommentOut.Activities>
                    </iad:CommentOut>
                    <iaw:InvokeWorkflow OutputArguments="{x:Null}" ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;vat&quot;,DictOcrValues(&quot;VAT_AMOUNT&quot;).Tostring},{&quot;VatPercentage&quot;,DictOcrValues(&quot;VAT_PERCENTAGE&quot;).Tostring},{&quot;division&quot;,division},{&quot;tenantID&quot;,tenantID},{&quot;inbnValue&quot;,inbnValue},{&quot;CountryCode&quot;,countryCode},{&quot;AccountStartsWith&quot;,&quot;&quot;},{&quot;logfile&quot;,logfile},{&quot;invoiceType&quot;,&quot;po&quot;}}]" ContinueOnError="True" DisplayName="Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_5" WorkflowFile="[projectPath+&quot;\vatConfiguration.xaml&quot;]" />
                  </Sequence>
                </If.Then>
              </If>
              <If Condition="[Math.abs(Convert.ToDecimal(diffamt)*Convert.ToDecimal(qty) + Convert.ToDecimal(chargeDiff)) &gt; 0]" sap2010:WorkflowViewState.IdRef="If_18">
                <If.Then>
                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_21">
                    <Sequence.Variables>
                      <Variable x:TypeArguments="x:String" Name="diffAmt1" />
                      <Variable x:TypeArguments="iru:ResponseObject" Name="resp1" />
                    </Sequence.Variables>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_48">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[diffAmt1]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">[Math.abs(Convert.ToDecimal(diffamt)*Convert.ToDecimal(qty) + Convert.ToDecimal(chargeDiff)).ToString]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for adding AddInfo" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_7" Response="[resp1]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddAddInfo&quot;]">
                      <iai:IONAPIRequestWizard.Headers>
                        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                          <scg:List x:TypeArguments="x:String" Capacity="4">
                            <x:String>Accept</x:String>
                          </scg:List>
                          <scg:List x:TypeArguments="x:String" Capacity="4">
                            <x:String>application/json</x:String>
                          </scg:List>
                        </scg:List>
                      </iai:IONAPIRequestWizard.Headers>
                      <iai:IONAPIRequestWizard.QueryParameters>
                        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                          <scg:List x:TypeArguments="x:String" Capacity="4">
                            <x:String>INBN</x:String>
                            <x:String>PEXN</x:String>
                            <x:String>PEXI</x:String>
                            <x:String>DIVI</x:String>
                          </scg:List>
                          <scg:List x:TypeArguments="x:String" Capacity="4">
                            <x:String>inbnValue</x:String>
                            <x:String>414</x:String>
                            <x:String>diffAmt1</x:String>
                            <x:String>division</x:String>
                          </scg:List>
                        </scg:List>
                      </iai:IONAPIRequestWizard.QueryParameters>
                    </iai:IONAPIRequestWizard>
                  </Sequence>
                </If.Then>
              </If>
            </Sequence>
          </If.Then>
          <If.Else>
            <Sequence DisplayName="Sequence invoice from mail" sap2010:WorkflowViewState.IdRef="Sequence_23">
              <Assign sap2010:WorkflowViewState.IdRef="Assign_49">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                </Assign.Value>
              </Assign>
              <Assign sap2010:WorkflowViewState.IdRef="Assign_50">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:String">["Invoice Number " + DictOcrValues("INVOICE_RECEIPT_ID").Tostring + " already exists."]</InArgument>
                </Assign.Value>
              </Assign>
              <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_4" Line="[commentStatus]" Source="[logfile]" />
            </Sequence>
          </If.Else>
        </If>
      </If.Then>
      <If.Else>
        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_45">
          <Sequence.Variables>
            <Variable x:TypeArguments="iru:ResponseObject" Name="poLinesResponseObject" />
            <Variable x:TypeArguments="x:Int32" Name="poLinesResponseCode" />
            <Variable x:TypeArguments="scg:Dictionary(x:String, x:Object)" Name="LinesDict" />
            <Variable x:TypeArguments="x:Boolean" Name="itemCodeMatch" />
            <Variable x:TypeArguments="x:Boolean" Name="qtyMatch" />
            <Variable x:TypeArguments="x:Boolean" Name="PriceMatch" />
            <Variable x:TypeArguments="x:Boolean" Name="poExists" />
            <Variable x:TypeArguments="scg:List(x:String)" Name="poList" />
            <Variable x:TypeArguments="scg:Dictionary(x:String, x:String)" Name="itemDict" />
          </Sequence.Variables>
          <If Condition="[DictOcrValues(&quot;PO_NUMBER&quot;).ToString &lt;&gt; &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_20">
            <If.Then>
              <Assign sap2010:WorkflowViewState.IdRef="Assign_51">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:String">[pono]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:String">[DictOcrValues("PO_NUMBER").ToString]</InArgument>
                </Assign.Value>
              </Assign>
            </If.Then>
          </If>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_84">
            <Assign.To>
              <OutArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows1]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="scg:List(s:String[])">[new list(Of String())]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_85">
            <Assign.To>
              <OutArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="scg:List(s:String[])">[new list(Of String())]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_52">
            <Assign.To>
              <OutArgument x:TypeArguments="scg:List(x:String)">[PONumbers]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="scg:List(x:String)">[New List(Of String)]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_53">
            <Assign.To>
              <OutArgument x:TypeArguments="scg:List(x:String)">[PONumbers]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="scg:List(x:String)">[DictOcrValues("PO_NUMBER").ToString.split(","c).ToList]</InArgument>
            </Assign.Value>
          </Assign>
          <ForEach x:TypeArguments="x:String" DisplayName="ForEach&lt;String&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_4" Values="[PONumbers]">
            <ActivityAction x:TypeArguments="x:String">
              <ActivityAction.Argument>
                <DelegateInArgument x:TypeArguments="x:String" Name="pono" />
              </ActivityAction.Argument>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_34">
                <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Message Box" sap2010:WorkflowViewState.IdRef="MessageBox_7" Selection="OK" Text="[pono]" Title="pono" />
                <Assign sap2010:WorkflowViewState.IdRef="Assign_55">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[req2]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String" xml:space="preserve">["F2PNLI,F2REPN,F2SCOC,F2RPQA,F2TRDT,F2ITNO,F2CONO,F2CUCD,F2DIVI,F2SUNO,F2IVQA,F2PUNO,F2SUDO,F2RCAC,F2RPQT,F2SCOP,F2SERA,F2IVQT,F2ICAC  from FGRECL where F2DIVI = " + DIVISION+" AND F2PUNO = " + pono + " and F2IMST != 9 "]</InArgument>
                  </Assign.Value>
                </Assign>
                <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;logfile&quot;,logfile},{&quot;miscValues&quot;,miscValues},{&quot;req2&quot;,req2}}]" ContinueOnError="True" DisplayName="Lines extraction Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_6" OutputArguments="[linesOcrWorkflowOutput]" ResponseCode="[linesOcrWorkflowStatus]" WorkflowFile="[projectPath+&quot;\LinesExtractionWithPO.xaml&quot;]" />
                <If Condition="[linesOcrWorkflowStatus = 200]" sap2010:WorkflowViewState.IdRef="If_26">
                  <If.Then>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_33">
                      <Sequence.Variables>
                        <Variable x:TypeArguments="x:Boolean" Name="DNinM3exists" />
                      </Sequence.Variables>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_56">
                        <Assign.To>
                          <OutArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows1]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="scg:List(s:String[])">[CType(linesOcrWorkflowOutput("M3TotalTableRows"), List(Of String()))]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_57">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:Boolean">[DNinM3exists]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:Boolean">[CType(linesOcrWorkflowOutput("DNinM3exists"), Boolean)]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Message Box" sap2010:WorkflowViewState.IdRef="MessageBox_9" Selection="OK" Text="[M3TotalTableRows1.Count.tostring]" Title="rows1" />
                      <If Condition="[M3TotalTableRows1.Count &gt; 0]" sap2010:WorkflowViewState.IdRef="If_25">
                        <If.Then>
                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_25">
                            <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_5" Line="[&quot;Receipt lines for the purchase order &quot; +pono.trim + &quot; is extracted.&quot;]" Source="[logfile]" />
                            <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" Title="{x:Null}" ContinueOnError="True" DisplayName="Message Box" sap2010:WorkflowViewState.IdRef="MessageBox_10" Selection="OK" Text="after append line" />
                            <ForEach x:TypeArguments="s:String[]" DisplayName="ForEach&lt;String[]&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_3" Values="[M3TotalTableRows1]">
                              <ActivityAction x:TypeArguments="s:String[]">
                                <ActivityAction.Argument>
                                  <DelegateInArgument x:TypeArguments="s:String[]" Name="item" />
                                </ActivityAction.Argument>
                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_24">
                                  <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" Title="{x:Null}" ContinueOnError="True" DisplayName="Message Box" sap2010:WorkflowViewState.IdRef="MessageBox_11" Selection="OK" Text="[item.Count.ToString]" />
                                  <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_1" MethodName="Add">
                                    <InvokeMethod.TargetObject>
                                      <InArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows]</InArgument>
                                    </InvokeMethod.TargetObject>
                                    <InArgument x:TypeArguments="s:String[]">[item]</InArgument>
                                  </InvokeMethod>
                                </Sequence>
                              </ActivityAction>
                            </ForEach>
                          </Sequence>
                        </If.Then>
                        <If.Else>
                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_32">
                            <Sequence.Variables>
                              <Variable x:TypeArguments="x:String" Name="po" />
                            </Sequence.Variables>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_58">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[po]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[po + pono.trim + ", "]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <If Condition="[DNinM3exists]" sap2010:WorkflowViewState.IdRef="If_24">
                              <If.Then>
                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_26">
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_59">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String" xml:space="preserve">["Lines already invoiced for po " +  po.Substring(0,po.Length-2)]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_60">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_6" Line="[&quot;Lines already invoiced for po: &quot; + pono.trim]" Source="[logfile]" />
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_61">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:Boolean">[poExists]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                </Sequence>
                              </If.Then>
                              <If.Else>
                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_31">
                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_30">
                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_62">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="x:String">[req2]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="x:String" xml:space="preserve">["F2PNLI,F2REPN,F2SCOC,F2RPQA,F2TRDT,F2ITNO,F2CONO,F2CUCD,F2DIVI,F2SUNO,F2IVQA,F2PUNO,F2SUDO,F2RCAC,F2RPQT,F2SCOP,F2SERA,F2IVQT,F2ICAC  from FGRECL where F2DIVI = " + DIVISION+" AND F2PUNO = " + pono]</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                    <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;logfile&quot;,logfile},{&quot;miscValues&quot;,miscValues},{&quot;req2&quot;,req2}}]" ContinueOnError="True" DisplayName="Lines extraction Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_7" OutputArguments="[linesOcrWorkflowOutput]" ResponseCode="[linesOcrWorkflowStatus]" WorkflowFile="[projectPath+&quot;\LinesExtractionWithPO.xaml&quot;]" />
                                    <If Condition="[linesOcrWorkflowStatus = 200]" sap2010:WorkflowViewState.IdRef="If_23">
                                      <If.Then>
                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_29">
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_63">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows1]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="scg:List(s:String[])">[CType(linesOcrWorkflowOutput("M3TotalTableRows"), List(Of String()))]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_64">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:Boolean">[DNinM3exists]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:Boolean">[CType(linesOcrWorkflowOutput("DNinM3exists"), Boolean)]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <If Condition="[M3TotalTableRows1.Count &gt; 0]" sap2010:WorkflowViewState.IdRef="If_22">
                                            <If.Else>
                                              <If Condition="[DNinM3exists]" sap2010:WorkflowViewState.IdRef="If_21">
                                                <If.Then>
                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_27">
                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_65">
                                                      <Assign.To>
                                                        <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                      </Assign.To>
                                                      <Assign.Value>
                                                        <InArgument x:TypeArguments="x:String" xml:space="preserve">["Lines already invoiced for po " +  po.Substring(0,po.Length-2)]</InArgument>
                                                      </Assign.Value>
                                                    </Assign>
                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_66">
                                                      <Assign.To>
                                                        <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                      </Assign.To>
                                                      <Assign.Value>
                                                        <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                                                      </Assign.Value>
                                                    </Assign>
                                                    <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_7" Line="[&quot;Lines already invoiced for po: &quot; + pono.trim]" Source="[logfile]" />
                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_67">
                                                      <Assign.To>
                                                        <OutArgument x:TypeArguments="x:Boolean">[poExists]</OutArgument>
                                                      </Assign.To>
                                                      <Assign.Value>
                                                        <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                                                      </Assign.Value>
                                                    </Assign>
                                                  </Sequence>
                                                </If.Then>
                                                <If.Else>
                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_28">
                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_68">
                                                      <Assign.To>
                                                        <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                      </Assign.To>
                                                      <Assign.Value>
                                                        <InArgument x:TypeArguments="x:String">["No receipts available for the given po: " + po.Substring(0,po.Length-2)]</InArgument>
                                                      </Assign.Value>
                                                    </Assign>
                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_69">
                                                      <Assign.To>
                                                        <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                      </Assign.To>
                                                      <Assign.Value>
                                                        <InArgument x:TypeArguments="x:String">PONOTRECEIVED</InArgument>
                                                      </Assign.Value>
                                                    </Assign>
                                                    <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_8" Line="[&quot;No receipts available for the given po: &quot; + pono.trim]" Source="[logfile]" />
                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_70">
                                                      <Assign.To>
                                                        <OutArgument x:TypeArguments="x:Boolean">[poExists]</OutArgument>
                                                      </Assign.To>
                                                      <Assign.Value>
                                                        <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                                                      </Assign.Value>
                                                    </Assign>
                                                  </Sequence>
                                                </If.Else>
                                              </If>
                                            </If.Else>
                                          </If>
                                        </Sequence>
                                      </If.Then>
                                    </If>
                                  </Sequence>
                                </Sequence>
                              </If.Else>
                            </If>
                          </Sequence>
                        </If.Else>
                      </If>
                    </Sequence>
                  </If.Then>
                  <If.Else>
                    <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_9" Line="Lines of the PO not extracted." Source="[logfile]" />
                  </If.Else>
                </If>
              </Sequence>
            </ActivityAction>
          </ForEach>
          <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Message Box" sap2010:WorkflowViewState.IdRef="MessageBox_8" Selection="OK" Text="[M3TotalTableRows.Count.ToString]" Title="M3TotalTableRows.Count" />
          <Assign sap2010:WorkflowViewState.IdRef="Assign_88">
            <Assign.To>
              <OutArgument x:TypeArguments="scg:List(x:String)">[poList]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="scg:List(x:String)">[M3TotalTableRows.Where(Function(arr) arr.Length &gt; 1). Select(Function(arr) arr(6)).Distinct().ToList()]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_90">
            <Assign.To>
              <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:String)">[itemDict]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="scg:Dictionary(x:String, x:String)">[New Dictionary(Of string, string)]</InArgument>
            </Assign.Value>
          </Assign>
          <ForEach x:TypeArguments="x:String" DisplayName="ForEach&lt;String&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_11" Values="[polist]">
            <ActivityAction x:TypeArguments="x:String">
              <ActivityAction.Argument>
                <DelegateInArgument x:TypeArguments="x:String" Name="po" />
              </ActivityAction.Argument>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_47">
                <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="pono IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_9" Response="[poLinesResponseObject]" StatusCode="[poLinesResponseCode]" Url="[TenantID +&quot;M3/m3api-rest/v2/execute/PPS200MI/LstLine?maxrecs=0&amp;dateformat=YMD8&amp;excludeempty=false&amp;righttrim=true&amp;returncols=PUNO%2CITNO%2CSITE%2CORQA%2CRVQA&amp;format=PRETTY&amp;extendedresult=false&quot;]">
                  <iai:IONAPIRequestWizard.Headers>
                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>Accept</x:String>
                      </scg:List>
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>application/json</x:String>
                      </scg:List>
                    </scg:List>
                  </iai:IONAPIRequestWizard.Headers>
                  <iai:IONAPIRequestWizard.QueryParameters>
                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>PUNO</x:String>
                      </scg:List>
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>po</x:String>
                      </scg:List>
                    </scg:List>
                  </iai:IONAPIRequestWizard.QueryParameters>
                </iai:IONAPIRequestWizard>
                <If Condition="[poLinesResponseCode = 200]" sap2010:WorkflowViewState.IdRef="If_29">
                  <If.Then>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_48">
                      <Sequence.Variables>
                        <Variable x:TypeArguments="njl:JToken" Name="out9" />
                      </Sequence.Variables>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_89">
                        <Assign.To>
                          <OutArgument x:TypeArguments="njl:JToken">[out9]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="njl:JToken">[poLinesResponseObject.readasjson("results")(0)("records")]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <If Condition="[out9.ToString &lt;&gt; &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_30">
                        <If.Then>
                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_49">
                            <ForEach x:TypeArguments="njl:JToken" DisplayName="ForEach&lt;JToken&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_13" Values="[poLinesResponseObject.readasjson(&quot;results&quot;)(0)(&quot;records&quot;)]">
                              <ActivityAction x:TypeArguments="njl:JToken">
                                <ActivityAction.Argument>
                                  <DelegateInArgument x:TypeArguments="njl:JToken" Name="item" />
                                </ActivityAction.Argument>
                                <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_2" MethodName="Add">
                                  <InvokeMethod.TargetObject>
                                    <InArgument x:TypeArguments="scg:Dictionary(x:String, x:String)">[itemDict]</InArgument>
                                  </InvokeMethod.TargetObject>
                                  <InArgument x:TypeArguments="x:String">[po+item("ITNO").tostring.trim.tolower]</InArgument>
                                  <InArgument x:TypeArguments="x:String">[item("SITE").tostring.trim.tolower]</InArgument>
                                </InvokeMethod>
                              </ActivityAction>
                            </ForEach>
                          </Sequence>
                        </If.Then>
                      </If>
                    </Sequence>
                  </If.Then>
                </If>
              </Sequence>
            </ActivityAction>
          </ForEach>
          <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" Title="{x:Null}" ContinueOnError="True" DisplayName="Message Box" sap2010:WorkflowViewState.IdRef="MessageBox_24" Selection="OK" Text="[String.Join(&quot;, &quot;, itemDict.Select(Function(kv) kv.Key &amp; &quot;: &quot; &amp; kv.Value.ToString()))]" />
          <ForEach x:TypeArguments="s:String[]" DisplayName="ForEach&lt;String[]&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_17" Values="[M3TotalTableRows]">
            <ActivityAction x:TypeArguments="s:String[]">
              <ActivityAction.Argument>
                <DelegateInArgument x:TypeArguments="s:String[]" Name="row" />
              </ActivityAction.Argument>
              <ForEach x:TypeArguments="scg:Dictionary(x:String, x:Object)" DisplayName="ForEach&lt;Dictionary&lt;String,Object&gt;&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_15" Values="[ListocrLineValues]">
                <ActivityAction x:TypeArguments="scg:Dictionary(x:String, x:Object)">
                  <ActivityAction.Argument>
                    <DelegateInArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)" Name="lines" />
                  </ActivityAction.Argument>
                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_65">
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_103">
                      <Assign.To>
                        <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[New Dictionary(Of String,Object)]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_104">
                      <Assign.To>
                        <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[lines]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_105">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:Boolean">[itemCodeMatch]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                      </Assign.Value>
                    </Assign>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_106">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:Boolean">[qtyMatch]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                      </Assign.Value>
                    </Assign>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_107">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:Boolean">[PriceMatch]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                      </Assign.Value>
                    </Assign>
                    <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Message Box" sap2010:WorkflowViewState.IdRef="MessageBox_22" Selection="OK" Text="[(row(5).tostring.trim.tolower + &quot; &quot;+ itemDict(row(6)+row(5)).tostring.trim.tolower)]" Title="itemCodeMatch" />
                    <Switch x:TypeArguments="x:Boolean" DisplayName="Item code - Switch" Expression="[LinesDict(&quot;SUPPLIER_ITEM_CODE&quot;).ToString.trim.ToLower.contains(row(5).tostring.trim.tolower) OR LinesDict(&quot;SUPPLIER_ITEM_CODE&quot;).ToString.trim.ToLower.contains(itemDict(row(6)+row(5)).tostring.trim.tolower)]" sap2010:WorkflowViewState.IdRef="Switch`1_8">
                      <Sequence x:Key="True" sap2010:WorkflowViewState.IdRef="Sequence_58">
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_108">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:Boolean">[itemCodeMatch]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                          </Assign.Value>
                        </Assign>
                      </Sequence>
                      <Sequence x:Key="False" sap2010:WorkflowViewState.IdRef="Sequence_59">
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_109">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:Boolean">[itemCodeMatch]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                          </Assign.Value>
                        </Assign>
                      </Sequence>
                    </Switch>
                    <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" Title="{x:Null}" ContinueOnError="True" DisplayName="Message Box" sap2010:WorkflowViewState.IdRef="MessageBox_23" Selection="OK" Text="[(LinesDict(&quot;QUANTITY&quot;)).tostring +  &quot; &quot;+ row(3)]" />
                    <Switch x:TypeArguments="x:Boolean" DisplayName="Quantity - Switch" Expression="[Math.Abs(Convert.ToDouble(LinesDict(&quot;QUANTITY&quot;)) - Convert.ToDouble(row(3))) &lt;= 0.05]" sap2010:WorkflowViewState.IdRef="Switch`1_9">
                      <Sequence x:Key="True" sap2010:WorkflowViewState.IdRef="Sequence_60">
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_110">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:Boolean">[qtyMatch]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                          </Assign.Value>
                        </Assign>
                      </Sequence>
                      <Sequence x:Key="False" sap2010:WorkflowViewState.IdRef="Sequence_61">
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_111">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:Boolean">[qtyMatch]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                          </Assign.Value>
                        </Assign>
                      </Sequence>
                    </Switch>
                    <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" Title="{x:Null}" ContinueOnError="True" DisplayName="Message Box" sap2010:WorkflowViewState.IdRef="MessageBox_26" Selection="OK" Text="[(LinesDict(&quot;UNIT_PRICE&quot;)).tostring + &quot; &quot;+row(2)]" />
                    <Switch x:TypeArguments="x:Boolean" DisplayName="Price - Switch" Expression="[Math.Abs(Convert.ToDouble(LinesDict(&quot;UNIT_PRICE&quot;)) - Convert.ToDouble(row(2))) &lt;= 0.05]" sap2010:WorkflowViewState.IdRef="Switch`1_10">
                      <Sequence x:Key="True" sap2010:WorkflowViewState.IdRef="Sequence_62">
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_112">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:Boolean">[PriceMatch]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                          </Assign.Value>
                        </Assign>
                      </Sequence>
                      <Sequence x:Key="False" sap2010:WorkflowViewState.IdRef="Sequence_63">
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_113">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:Boolean">[PriceMatch]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                          </Assign.Value>
                        </Assign>
                      </Sequence>
                    </Switch>
                    <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" Title="{x:Null}" ContinueOnError="True" DisplayName="Message Box" sap2010:WorkflowViewState.IdRef="MessageBox_25" Selection="OK" Text="[&quot;PriceMatch &quot;+PriceMatch.tostring +&quot; qtyMatch &quot;+qtyMatch.tostring +&quot; itemCodeMatch &quot;+itemCodeMatch.tostring]" />
                    <If Condition="[itemCodeMatch and PriceMatch and qtyMatch]" sap2010:WorkflowViewState.IdRef="If_32">
                      <If.Then>
                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_64">
                          <Sequence.Variables>
                            <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="AddLineOcrWorkflowOutput" />
                          </Sequence.Variables>
                          <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;logfile&quot;,logfile},{&quot;M3TotalTableRows&quot;,M3TotalTableRows},{&quot;vatCodeConfig&quot;,vatCodeConfig},{&quot;division&quot;,division},{&quot;inbnValue&quot;,inbnValue}}]" ContinueOnError="True" DisplayName="Add Lines Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_10" OutputArguments="[AddLineOcrWorkflowOutput]" ResponseCode="[AddLineOcrWorkflowStatus]" WorkflowFile="[projectPath+&quot;\AddLine.xaml&quot;]" />
                        </Sequence>
                      </If.Then>
                      <If.Else>
                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_66">
                          <If Condition="[itemCodeMatch = False]" sap2010:WorkflowViewState.IdRef="If_33">
                            <If.Then>
                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_67">
                                <Assign sap2010:WorkflowViewState.IdRef="Assign_114">
                                  <Assign.To>
                                    <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                  </Assign.To>
                                  <Assign.Value>
                                    <InArgument x:TypeArguments="x:String">["Item code mismatch."]</InArgument>
                                  </Assign.Value>
                                </Assign>
                              </Sequence>
                            </If.Then>
                          </If>
                          <If Condition="[PriceMatch = False]" sap2010:WorkflowViewState.IdRef="If_35">
                            <If.Then>
                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_69">
                                <Assign sap2010:WorkflowViewState.IdRef="Assign_116">
                                  <Assign.To>
                                    <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                  </Assign.To>
                                  <Assign.Value>
                                    <InArgument x:TypeArguments="x:String">[commentStatus + "Price mismatch."]</InArgument>
                                  </Assign.Value>
                                </Assign>
                              </Sequence>
                            </If.Then>
                          </If>
                          <If Condition="[qtyMatch = False]" sap2010:WorkflowViewState.IdRef="If_34">
                            <If.Then>
                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_68">
                                <Assign sap2010:WorkflowViewState.IdRef="Assign_115">
                                  <Assign.To>
                                    <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                  </Assign.To>
                                  <Assign.Value>
                                    <InArgument x:TypeArguments="x:String">[commentStatus + "Quantity mismatch."]</InArgument>
                                  </Assign.Value>
                                </Assign>
                              </Sequence>
                            </If.Then>
                          </If>
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_117">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String">[commentStatus + "Please verify"]</InArgument>
                            </Assign.Value>
                          </Assign>
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_118">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                            </Assign.Value>
                          </Assign>
                        </Sequence>
                      </If.Else>
                    </If>
                  </Sequence>
                </ActivityAction>
              </ForEach>
            </ActivityAction>
          </ForEach>
          <iad:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_2">
            <iad:CommentOut.Activities>
              <ForEach x:TypeArguments="x:String" DisplayName="ForEach&lt;String&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_7" Values="[PONumbers]">
                <ActivityAction x:TypeArguments="x:String">
                  <ActivityAction.Argument>
                    <DelegateInArgument x:TypeArguments="x:String" Name="pono" />
                  </ActivityAction.Argument>
                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_44">
                    <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="pono IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_8" Response="[poLinesResponseObject]" StatusCode="[poLinesResponseCode]" Url="[TenantID +&quot;M3/m3api-rest/v2/execute/PPS200MI/LstLine?maxrecs=0&amp;dateformat=YMD8&amp;excludeempty=false&amp;righttrim=true&amp;returncols=PUNO%2CITNO%2CSITE%2CORQA%2CRVQA&amp;format=PRETTY&amp;extendedresult=false&quot;]">
                      <iai:IONAPIRequestWizard.Headers>
                        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                          <scg:List x:TypeArguments="x:String" Capacity="4">
                            <x:String>Accept</x:String>
                          </scg:List>
                          <scg:List x:TypeArguments="x:String" Capacity="4">
                            <x:String>application/json</x:String>
                          </scg:List>
                        </scg:List>
                      </iai:IONAPIRequestWizard.Headers>
                      <iai:IONAPIRequestWizard.QueryParameters>
                        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                          <scg:List x:TypeArguments="x:String" Capacity="4">
                            <x:String>PUNO</x:String>
                          </scg:List>
                          <scg:List x:TypeArguments="x:String" Capacity="4">
                            <x:String>pono</x:String>
                          </scg:List>
                        </scg:List>
                      </iai:IONAPIRequestWizard.QueryParameters>
                    </iai:IONAPIRequestWizard>
                    <Switch x:TypeArguments="x:Boolean" Expression="[poLinesResponseCode = 200]" sap2010:WorkflowViewState.IdRef="Switch`1_4">
                      <Sequence x:Key="True" sap2010:WorkflowViewState.IdRef="Sequence_43">
                        <Sequence.Variables>
                          <Variable x:TypeArguments="njl:JToken" Name="variable1" />
                        </Sequence.Variables>
                        <ForEach x:TypeArguments="njl:JToken" DisplayName="ForEach&lt;JToken&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_9" Values="[poLinesResponseObject.readasjson(&quot;results&quot;)(0)(&quot;records&quot;)]">
                          <ActivityAction x:TypeArguments="njl:JToken">
                            <ActivityAction.Argument>
                              <DelegateInArgument x:TypeArguments="njl:JToken" Name="item" />
                            </ActivityAction.Argument>
                            <ForEach x:TypeArguments="scg:Dictionary(x:String, x:Object)" DisplayName="ForEach&lt;Dictionary&lt;String,Object&gt;&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_5" Values="[ListocrLineValues]">
                              <ActivityAction x:TypeArguments="scg:Dictionary(x:String, x:Object)">
                                <ActivityAction.Argument>
                                  <DelegateInArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)" Name="lines" />
                                </ActivityAction.Argument>
                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_42">
                                  <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" Title="{x:Null}" ContinueOnError="True" DisplayName="Message Box" sap2010:WorkflowViewState.IdRef="MessageBox_17" Selection="OK" Text="[item.tostring]" />
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_71">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[New Dictionary(Of String,Object)]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_72">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[lines]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_73">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:Boolean">[itemCodeMatch]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_74">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:Boolean">[qtyMatch]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_75">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:Boolean">[PriceMatch]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <Switch x:TypeArguments="x:Boolean" DisplayName="Item code - Switch" Expression="[LinesDict(&quot;SUPPLIER_ITEM_CODE&quot;).ToString.trim.ToLower.contains(item(&quot;ITNO&quot;).tostring.trim.tolower) OR LinesDict(&quot;SUPPLIER_ITEM_CODE&quot;).ToString.trim.ToLower.contains(item(&quot;SITE&quot;).tostring.trim.tolower)]" sap2010:WorkflowViewState.IdRef="Switch`1_1">
                                    <Sequence x:Key="True" sap2010:WorkflowViewState.IdRef="Sequence_35">
                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_76">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:Boolean">[itemCodeMatch]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                    </Sequence>
                                    <Sequence x:Key="False" sap2010:WorkflowViewState.IdRef="Sequence_36">
                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_77">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:Boolean">[itemCodeMatch]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                    </Sequence>
                                  </Switch>
                                  <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Message Box" sap2010:WorkflowViewState.IdRef="MessageBox_15" Selection="OK" Text="[LinesDict(&quot;QUANTITY&quot;).tostring + &quot;  &quot;+LinesDict(&quot;UNIT_PRICE&quot;).tostring]" Title="itemCodeMatch" />
                                  <Switch x:TypeArguments="x:Boolean" DisplayName="Quantity - Switch" Expression="[Math.Abs(Convert.ToDouble(LinesDict(&quot;QUANTITY&quot;)) - Convert.ToDouble(Item(&quot;ORQT&quot;))) &lt;= 0.05]" sap2010:WorkflowViewState.IdRef="Switch`1_2">
                                    <Sequence x:Key="True" sap2010:WorkflowViewState.IdRef="Sequence_37">
                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_78">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:Boolean">[qtyMatch]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                    </Sequence>
                                    <Sequence x:Key="False" sap2010:WorkflowViewState.IdRef="Sequence_38">
                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_79">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:Boolean">[qtyMatch]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                    </Sequence>
                                  </Switch>
                                  <Switch x:TypeArguments="x:Boolean" DisplayName="Price - Switch" Expression="[Math.Abs(Convert.ToDouble(LinesDict(&quot;UNIT_PRICE&quot;)) - Convert.ToDouble(item(&quot;PUPR&quot;))) &lt;= 0.05]" sap2010:WorkflowViewState.IdRef="Switch`1_3">
                                    <Sequence x:Key="True" sap2010:WorkflowViewState.IdRef="Sequence_39">
                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_80">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:Boolean">[PriceMatch]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                    </Sequence>
                                    <Sequence x:Key="False" sap2010:WorkflowViewState.IdRef="Sequence_40">
                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_81">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:Boolean">[PriceMatch]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                    </Sequence>
                                  </Switch>
                                  <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" Title="{x:Null}" ContinueOnError="True" DisplayName="Message Box" sap2010:WorkflowViewState.IdRef="MessageBox_12" Selection="OK" Text="[&quot;PriceMatch &quot;+PriceMatch.tostring +&quot; qtyMatch &quot;+qtyMatch.tostring +&quot; itemCodeMatch &quot;+itemCodeMatch.tostring]" />
                                  <If Condition="[itemCodeMatch and PriceMatch and qtyMatch]" sap2010:WorkflowViewState.IdRef="If_27">
                                    <If.Then>
                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_41">
                                        <Sequence.Variables>
                                          <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="AddLineOcrWorkflowOutput" />
                                        </Sequence.Variables>
                                        <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;logfile&quot;,logfile},{&quot;M3TotalTableRows&quot;,M3TotalTableRows},{&quot;vatCodeConfig&quot;,vatCodeConfig},{&quot;division&quot;,division},{&quot;inbnValue&quot;,inbnValue}}]" ContinueOnError="True" DisplayName="Add Lines Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_8" OutputArguments="[AddLineOcrWorkflowOutput]" ResponseCode="[AddLineOcrWorkflowStatus]" WorkflowFile="[projectPath+&quot;\AddLine.xaml&quot;]" />
                                      </Sequence>
                                    </If.Then>
                                  </If>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_87">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:Int32">[Itr]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:Int32">[Itr+1]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                </Sequence>
                              </ActivityAction>
                            </ForEach>
                          </ActivityAction>
                        </ForEach>
                      </Sequence>
                    </Switch>
                  </Sequence>
                </ActivityAction>
              </ForEach>
            </iad:CommentOut.Activities>
          </iad:CommentOut>
        </Sequence>
      </If.Else>
    </If>
    <sads:DebugSymbol.Symbol>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**************************/nAUsDAYAG8gE+8gFxAwH+Be8BP+8BRQMB/AX6AT76AXEDAfoF9wE/9wFNAwH4BYsCPosCYgMB9AWIAj+IAkgDAfIFjgKhAo4CrQIDAe8FjgLNAo4ClgMDAe0FjgK5Ao4CyAIDAesFrgIhrgI2AwHXBbACF9UCIgMB2QXdAj7dAl8DAdQF2gI/2gJFAwHSBeUCPuUCbAMBzwXiAj/iAkUDAc0F7QI+7QKvAQMBywXqAj/qAkcDAckF9QI+9QLRAQMBxAXyAj/yAk4DAcIF+gIXgQMgAwG8BYkDG5ADJAMBtwWKBT+KBUMDAcUEhwVAhwVWAwHDBJMFGZMFSAMBwQSPBT+PBUoDAb8EnAU+nAVtAwG9BJkFP5kFTgMBuwSfBSGfBXQDAbEEoQUXqAUgAwG2BKsFF7IFIAMBsgS+BT++BUQDAa4EuwVAuwVWAwGsBMYFPsYFcwMBqgTDBT/DBUoDAagEzgU+zgVYAwGmBMsFP8sFTgMBpASpBkGpBoEBAwHpA6YGQqYGSwMB5wOxBkGxBmMDAeUDrgZCrgZHAwHjA8cGGc4GIgMB1gPiBhniBo0FAwG9A+MGGeoGIgMBuAP9BkD9BmYDAa8D+gZB+gZGAwGtA8wHZswHngQDAaoDzAeUBcwHxgUDAagD3AdA3AemAQMBoAPZB0HZB0sDAZ4D3wfDAt8HzAIDAZsD3wfRAt8HlwMDAZkD5ggX7QggAwHSAu4IF/UIIAMBzQL2CBf2CIACAwHJAvcIF6EKHAMBygGlCqEBpQrBAQMByAGlCskBpQrUAQMBxgHeChflCiADAacB5goX+QocAwGdAZELWpELfAMBkAGOC1uOC2YDAY4BmQtamQthAwGLAZYLW5YLZgMBiQGhC0GhC0YDAYcBngtCngtRAwGFAakLQakLRgMBgwGmC0KmC0wDAYEBsQtBsQtGAgF/rgtCrgtOAgF9tAvKAbQLqwICAXe0C7ICtAvBAgIBdrULZbULygICAWS2Cxe/CyICAXDACxfJCyICAWvLC9sBywuiAgIBYMwLZMwLyQECAVTNCxfWCyICAVrXCxfgCyICAVXiC9sB4guiAgIBUOMLYeMLyAECAUTkCxftCyICAUruCxf3CyICAUX5C9sB+QvoAgIBP/oLI/oLUAIBDfwLGYEMJAIBNYQMGb8MJAIBEbECGbgCIgMB5gW5AhnUAh4DAdoF/wJC/wJhAwG/BfwCQ/wCUgMBvQWOA0aOA1UDAboFiwNHiwNWAwG4BaYFQqYFUwMBuQSjBUOjBUsDAbcEsAVCsAVJAwG1BK0FQ60FSwMBswTMBkTMBpcBAwHaA8kGRckGXQMB1wPiBrUE4gbRBAMBwwPiBosE4ganBAMBwQPiBjfiBoYDAwHAA+IG3wTiBooFAwG+A+gGROgGegMBuwPlBkXlBlEDAbkD6whO6wiUAQMB1QLoCE/oCGIDAdMC8whD8wh7AwHQAvAIRPAIUgMBzgL2CMsB9gjvAQMBywL2CPYB9gj9AQMBygL3CCX3CEcDAcsB+QgbjAkmAwG2Ao8JG58KJgMBzQHjCkTjCn8DAaoB4ApF4ApLAwGoAeYKJeYKTgMBngHoChv3CiYDAZ8BtwsZvgsiAgFxwQsZyAsiAgFszgsZ1QsiAgFb2AsZ3wsiAgFW5QsZ7AsiAgFL7wsZ9gsiAgFGgAwbgAzDBAIBNoUMG5IMIAIBLZMMG6AMIAIBJKEMG64MIAIBG68MG7YMJAIBFrcMG74MJAIBErYCRrYCaQMB6QWzAkezAk0DAecFuQInuQJ9AwHbBbsCHcQCKAMB4QXHAh3SAigDAdwF+ggd+gicAgMBxAL7CB37CPkBAwHCAvwIHYsJJwMBtwKTCR2aCSYDAbACmwkdngoiAwHOAekKHfYKJwMBoAG8C0W8C0kCAXS5C0a5C1UCAXLGC0XGC0oCAW/DC0bDC1UCAW3TC0XTC0kCAV7QC0bQC1ACAVzdC0XdC0oCAVnaC0baC1ACAVfqC0XqC0kCAU7nC0bnC1ICAUz0C0X0C0oCAUnxC0bxC1ICAUeADO0DgAyJBAIBPIAMwwOADN8DAgE6gAw5gAy/AgIBOYAMlwSADMAEAgE3hQwphQxCAgEuhwwfkAwqAgEwkwwpkww/AgEllQwfngwqAgEnoQwpoQw9AgEcowwfrAwqAgEetAxGtAxnAgEZsQxHsQxWAgEXvAxGvAxNAgEVuQxHuQxPAgETvAIfwwIoAwHiBcgCH9ECKAMB3QX6CKkB+giGAgMBxwL6CI4C+giZAgMBxQL7COMB+wj2AQMBwwL8CJ4B/AizAQMBwAKBCSGJCSwDAbgCmAlImAlfAwGzApUJSZUJTQMBsQKbCSubCTsDAc8BnQkhtwksAwGcAroJIZwKLAMB0QHpCp0B6QruAQMBpgHuCiH0CjADAaEBiAwhjwwqAgExlgwhnQwqAgEopAwhqwwqAgEfwQJKwQJ/AwHlBb4CS74CUQMB4wXOAiXOAlQDAeAFygJLygJRAwHeBYIJI4IJgwIDAb4CgwkjiAkyAwG5Ap4JI6UJLAMBqgKmCSOtCSwDAaYCrgkjrgmAAgMBoQKvCSO2CSwDAZ0CuwkjmwouAwHSAfMKSvMKbgMBpQHyCkryCnEDAaQB8Apm8ApwAwGiAY0MTI0MYwIBNIoMTYoMXAIBMpsMTJsMbwIBK5gMTZgMXAIBKakMTKkMcgIBIqYMTaYMXAIBIIIJ6QGCCYACAwG/AoUJWoUJbAMBvAKHCU6HCVQDAboCoAlPoAleAwGrAqsJTqsJVQMBqQKoCU+oCVcDAacCrgmvAa4J6gEDAaQCrgnyAa4J/QEDAaICtAlPtAlUAwGgArEJULEJWgMBngK8CSXDCS4DAZYCxAklxAn9AwMBjgLFCSWaCioDAdMBvglRvglXAwGXAsQJmwPECbUDAwGUAsQJ8wLECY0DAwGSAsQJQ8QJ6QEDAZECxAnDA8QJ+gMDAY8CxQkzxQlTAwHUAccJKZgKNAMB1gHICSvPCTQDAYoC0Akr1wk0AwGFAtgJK5cKMAMB1wHNCWLNCagBAwGNAsoJY8oJdgMBiwLVCVfVCY8BAwGIAtIJWNIJZgMBhgLYCTnYCVsDAdgB2gkvlQo0AwHaAdoJPdoJTQMB2wHcCTP2CT4DAfEB+Qkzkwo+AwHdAd0JNeQJPgMB/wHlCTXsCT4DAfsB7Qk17QmSAgMB9gHuCTX1CT4DAfIB+gk1gQo+AwHrAYIKNYkKPgMB5wGKCjWKCpsCAwHiAYsKNZIKPgMB3gHfCWHfCXADAYAC6glg6glnAwH+AecJYecJaQMB/AHtCcEB7Qn8AQMB+QHtCYQC7QmPAgMB9wHzCWHzCWYDAfUB8Ali8AlsAwHzAf8JYP8JqgEDAe4B/Alh/AlwAwHsAYcKYIcKbQMB6gGECmGECmkDAegBigrBAYoKhQIDAeUBigqNAooKmAIDAeMBkAphkApmAwHhAY0KYo0KbAMB3wE=</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="Assign_119" sap:VirtualizedContainerService.HintSize="1835,60" />
      <sap2010:ViewStateData Id="Assign_120" sap:VirtualizedContainerService.HintSize="1835,60" />
      <sap2010:ViewStateData Id="MessageBox_1" sap:VirtualizedContainerService.HintSize="1835,22" />
      <sap2010:ViewStateData Id="MessageBox_5" sap:VirtualizedContainerService.HintSize="1835,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_1" sap:VirtualizedContainerService.HintSize="1835,22" />
      <sap2010:ViewStateData Id="Assign_1" sap:VirtualizedContainerService.HintSize="1033,60" />
      <sap2010:ViewStateData Id="Assign_2" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_3" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_4" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_5" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_6" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_7" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_83" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_1" sap:VirtualizedContainerService.HintSize="722,784">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_8" sap:VirtualizedContainerService.HintSize="700,60" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_2" sap:VirtualizedContainerService.HintSize="700,22" />
      <sap2010:ViewStateData Id="Assign_9" sap:VirtualizedContainerService.HintSize="553,60" />
      <sap2010:ViewStateData Id="Assign_10" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_2" sap:VirtualizedContainerService.HintSize="264,184">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_11" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_3" sap:VirtualizedContainerService.HintSize="264,184">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_1" sap:VirtualizedContainerService.HintSize="553,332" />
      <sap2010:ViewStateData Id="Sequence_4" sap:VirtualizedContainerService.HintSize="575,556">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_2" sap:VirtualizedContainerService.HintSize="700,704" />
      <sap2010:ViewStateData Id="Assign_12" sap:VirtualizedContainerService.HintSize="700,60" />
      <sap2010:ViewStateData Id="Assign_13" sap:VirtualizedContainerService.HintSize="700,60" />
      <sap2010:ViewStateData Id="Assign_14" sap:VirtualizedContainerService.HintSize="700,60" />
      <sap2010:ViewStateData Id="Assign_15" sap:VirtualizedContainerService.HintSize="700,60" />
      <sap2010:ViewStateData Id="Assign_16" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_17" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Catch`1_1" sap:VirtualizedContainerService.HintSize="404,21">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_1" sap:VirtualizedContainerService.HintSize="700,287" />
      <sap2010:ViewStateData Id="Sequence_5" sap:VirtualizedContainerService.HintSize="722,1717">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_6" sap:VirtualizedContainerService.HintSize="744,2665">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_18" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_19" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_1" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_7" sap:VirtualizedContainerService.HintSize="264,346">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_3" sap:VirtualizedContainerService.HintSize="1033,2813" />
      <sap2010:ViewStateData Id="Sequence_8" sap:VirtualizedContainerService.HintSize="1055,3037">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_20" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_21" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_2" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_9" sap:VirtualizedContainerService.HintSize="264,346">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_4" sap:VirtualizedContainerService.HintSize="1835,3185">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="MessageBox_4" sap:VirtualizedContainerService.HintSize="1835,22" />
      <sap2010:ViewStateData Id="InvokeWorkflow_12" sap:VirtualizedContainerService.HintSize="1835,22" />
      <sap2010:ViewStateData Id="MessageBox_27" sap:VirtualizedContainerService.HintSize="1835,22" />
      <sap2010:ViewStateData Id="InvokeWorkflow_1" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Assign_22" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="If_5" sap:VirtualizedContainerService.HintSize="464,208" />
      <sap2010:ViewStateData Id="Assign_23" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="Sequence_10" sap:VirtualizedContainerService.HintSize="486,494">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_24" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_11" sap:VirtualizedContainerService.HintSize="264,184">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_6" sap:VirtualizedContainerService.HintSize="464,332" />
      <sap2010:ViewStateData Id="If_7" sap:VirtualizedContainerService.HintSize="1835,642">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="IONAPIRequestWizard_3" sap:VirtualizedContainerService.HintSize="1131,22" />
      <sap2010:ViewStateData Id="MessageBox_2" sap:VirtualizedContainerService.HintSize="1131,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_4" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_8" sap:VirtualizedContainerService.HintSize="1131,208" />
      <sap2010:ViewStateData Id="MessageBox_3" sap:VirtualizedContainerService.HintSize="1131,22" />
      <sap2010:ViewStateData Id="Assign_25" sap:VirtualizedContainerService.HintSize="820,60" />
      <sap2010:ViewStateData Id="Assign_26" sap:VirtualizedContainerService.HintSize="509,60" />
      <sap2010:ViewStateData Id="Assign_27" sap:VirtualizedContainerService.HintSize="509,60" />
      <sap2010:ViewStateData Id="Assign_28" sap:VirtualizedContainerService.HintSize="509,60" />
      <sap2010:ViewStateData Id="Assign_29" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_30" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="If_9" sap:VirtualizedContainerService.HintSize="509,208" />
      <sap2010:ViewStateData Id="Sequence_12" sap:VirtualizedContainerService.HintSize="531,632">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_31" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_32" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_33" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_13" sap:VirtualizedContainerService.HintSize="264,384">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_10" sap:VirtualizedContainerService.HintSize="820,780" />
      <sap2010:ViewStateData Id="Sequence_14" sap:VirtualizedContainerService.HintSize="842,1004">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_34" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_35" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_3" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_15" sap:VirtualizedContainerService.HintSize="264,346">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_11" sap:VirtualizedContainerService.HintSize="1131,1152">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_16" sap:VirtualizedContainerService.HintSize="1835,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="MessageBox_6" sap:VirtualizedContainerService.HintSize="1835,22" />
      <sap2010:ViewStateData Id="Assign_36" sap:VirtualizedContainerService.HintSize="590,61.3333333333333" />
      <sap2010:ViewStateData Id="InvokeWorkflow_2" sap:VirtualizedContainerService.HintSize="590,22" />
      <sap2010:ViewStateData Id="Assign_37" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="If_12" sap:VirtualizedContainerService.HintSize="590,213.333333333333" />
      <sap2010:ViewStateData Id="Assign_38" sap:VirtualizedContainerService.HintSize="590,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_39" sap:VirtualizedContainerService.HintSize="590,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_40" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_41" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Sequence_17" sap:VirtualizedContainerService.HintSize="264,286.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_1" sap:VirtualizedContainerService.HintSize="590,52">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_42" sap:VirtualizedContainerService.HintSize="590,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_43" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Sequence_18" sap:VirtualizedContainerService.HintSize="264,185.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_2" sap:VirtualizedContainerService.HintSize="294.666666666667,336.666666666667" />
      <sap2010:ViewStateData Id="If_13" sap:VirtualizedContainerService.HintSize="590,488.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeWorkflow_3" sap:VirtualizedContainerService.HintSize="590,22" />
      <sap2010:ViewStateData Id="Assign_44" sap:VirtualizedContainerService.HintSize="590,61.3333333333333" />
      <sap2010:ViewStateData Id="InvokeWorkflow_4" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_45" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Sequence_19" sap:VirtualizedContainerService.HintSize="264,247.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_14" sap:VirtualizedContainerService.HintSize="464,359.333333333333" />
      <sap2010:ViewStateData Id="If_15" sap:VirtualizedContainerService.HintSize="590,471.333333333333" />
      <sap2010:ViewStateData Id="Assign_46" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_47" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_5" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_6" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_16" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="CommentOut_1" sap:VirtualizedContainerService.HintSize="242,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeWorkflow_5" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_20" sap:VirtualizedContainerService.HintSize="264,345.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_17" sap:VirtualizedContainerService.HintSize="590,457.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_48" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_7" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_21" sap:VirtualizedContainerService.HintSize="264,247.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_18" sap:VirtualizedContainerService.HintSize="590,359.333333333333" />
      <sap2010:ViewStateData Id="Sequence_22" sap:VirtualizedContainerService.HintSize="200,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_49" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_50" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_4" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_23" sap:VirtualizedContainerService.HintSize="264,346">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_19" sap:VirtualizedContainerService.HintSize="489,494" />
      <sap2010:ViewStateData Id="Assign_51" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="If_20" sap:VirtualizedContainerService.HintSize="1299,208" />
      <sap2010:ViewStateData Id="Assign_84" sap:VirtualizedContainerService.HintSize="1299,60" />
      <sap2010:ViewStateData Id="Assign_85" sap:VirtualizedContainerService.HintSize="1299,60" />
      <sap2010:ViewStateData Id="Assign_52" sap:VirtualizedContainerService.HintSize="1299,60" />
      <sap2010:ViewStateData Id="Assign_53" sap:VirtualizedContainerService.HintSize="1299,60" />
      <sap2010:ViewStateData Id="MessageBox_7" sap:VirtualizedContainerService.HintSize="489,22" />
      <sap2010:ViewStateData Id="Assign_55" sap:VirtualizedContainerService.HintSize="489,60" />
      <sap2010:ViewStateData Id="InvokeWorkflow_6" sap:VirtualizedContainerService.HintSize="489,22" />
      <sap2010:ViewStateData Id="Assign_56" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_57" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="MessageBox_9" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Append_Line_5" sap:VirtualizedContainerService.HintSize="287,22" />
      <sap2010:ViewStateData Id="MessageBox_10" sap:VirtualizedContainerService.HintSize="287,22" />
      <sap2010:ViewStateData Id="MessageBox_11" sap:VirtualizedContainerService.HintSize="218,22" />
      <sap2010:ViewStateData Id="InvokeMethod_1" sap:VirtualizedContainerService.HintSize="218,128" />
      <sap2010:ViewStateData Id="Sequence_24" sap:VirtualizedContainerService.HintSize="240,314">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_3" sap:VirtualizedContainerService.HintSize="287,462" />
      <sap2010:ViewStateData Id="Sequence_25" sap:VirtualizedContainerService.HintSize="309,710">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_58" sap:VirtualizedContainerService.HintSize="1158,60" />
      <sap2010:ViewStateData Id="Assign_59" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_60" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_6" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_61" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_26" sap:VirtualizedContainerService.HintSize="264,446">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_62" sap:VirtualizedContainerService.HintSize="825,60" />
      <sap2010:ViewStateData Id="InvokeWorkflow_7" sap:VirtualizedContainerService.HintSize="825,22" />
      <sap2010:ViewStateData Id="Assign_63" sap:VirtualizedContainerService.HintSize="678,60" />
      <sap2010:ViewStateData Id="Assign_64" sap:VirtualizedContainerService.HintSize="678,60" />
      <sap2010:ViewStateData Id="Assign_65" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_66" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_7" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_67" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_27" sap:VirtualizedContainerService.HintSize="264,446">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_68" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_69" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_8" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_70" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_28" sap:VirtualizedContainerService.HintSize="264,446">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_21" sap:VirtualizedContainerService.HintSize="553,594" />
      <sap2010:ViewStateData Id="If_22" sap:VirtualizedContainerService.HintSize="678,742" />
      <sap2010:ViewStateData Id="Sequence_29" sap:VirtualizedContainerService.HintSize="700,1066">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_23" sap:VirtualizedContainerService.HintSize="825,1214" />
      <sap2010:ViewStateData Id="Sequence_30" sap:VirtualizedContainerService.HintSize="847,1500">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_31" sap:VirtualizedContainerService.HintSize="869,1624">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_24" sap:VirtualizedContainerService.HintSize="1158,1772" />
      <sap2010:ViewStateData Id="Sequence_32" sap:VirtualizedContainerService.HintSize="1180,1996">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_25" sap:VirtualizedContainerService.HintSize="242,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_33" sap:VirtualizedContainerService.HintSize="264,437">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_9" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_26" sap:VirtualizedContainerService.HintSize="489,585" />
      <sap2010:ViewStateData Id="Sequence_34" sap:VirtualizedContainerService.HintSize="511,933">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_4" sap:VirtualizedContainerService.HintSize="1299,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="MessageBox_8" sap:VirtualizedContainerService.HintSize="1299,22" />
      <sap2010:ViewStateData Id="Assign_88" sap:VirtualizedContainerService.HintSize="1299,60" />
      <sap2010:ViewStateData Id="Assign_90" sap:VirtualizedContainerService.HintSize="1299,60" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_9" sap:VirtualizedContainerService.HintSize="611,22" />
      <sap2010:ViewStateData Id="Assign_89" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="InvokeMethod_2" sap:VirtualizedContainerService.HintSize="218,128" />
      <sap2010:ViewStateData Id="ForEach`1_13" sap:VirtualizedContainerService.HintSize="287,276" />
      <sap2010:ViewStateData Id="Sequence_49" sap:VirtualizedContainerService.HintSize="309,400">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_30" sap:VirtualizedContainerService.HintSize="464,548" />
      <sap2010:ViewStateData Id="Sequence_48" sap:VirtualizedContainerService.HintSize="486,772">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_29" sap:VirtualizedContainerService.HintSize="611,920">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_47" sap:VirtualizedContainerService.HintSize="633,1106">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_11" sap:VirtualizedContainerService.HintSize="1299,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="MessageBox_24" sap:VirtualizedContainerService.HintSize="1299,22" />
      <sap2010:ViewStateData Id="Assign_103" sap:VirtualizedContainerService.HintSize="1217,60" />
      <sap2010:ViewStateData Id="Assign_104" sap:VirtualizedContainerService.HintSize="1217,60" />
      <sap2010:ViewStateData Id="Assign_105" sap:VirtualizedContainerService.HintSize="1217,60" />
      <sap2010:ViewStateData Id="Assign_106" sap:VirtualizedContainerService.HintSize="1217,60" />
      <sap2010:ViewStateData Id="Assign_107" sap:VirtualizedContainerService.HintSize="1217,60" />
      <sap2010:ViewStateData Id="MessageBox_22" sap:VirtualizedContainerService.HintSize="1217,22" />
      <sap2010:ViewStateData Id="Assign_108" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_58" sap:VirtualizedContainerService.HintSize="264,181">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_109" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_59" sap:VirtualizedContainerService.HintSize="264,181">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_8" sap:VirtualizedContainerService.HintSize="1217,160">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="MessageBox_23" sap:VirtualizedContainerService.HintSize="1217,22" />
      <sap2010:ViewStateData Id="Assign_110" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_60" sap:VirtualizedContainerService.HintSize="264,181">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_111" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_61" sap:VirtualizedContainerService.HintSize="264,181">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_9" sap:VirtualizedContainerService.HintSize="1217,160">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="MessageBox_26" sap:VirtualizedContainerService.HintSize="1217,22" />
      <sap2010:ViewStateData Id="Assign_112" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_62" sap:VirtualizedContainerService.HintSize="264,181">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_113" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_63" sap:VirtualizedContainerService.HintSize="264,181">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_10" sap:VirtualizedContainerService.HintSize="1217,160">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="MessageBox_25" sap:VirtualizedContainerService.HintSize="1217,22" />
      <sap2010:ViewStateData Id="InvokeWorkflow_10" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Sequence_64" sap:VirtualizedContainerService.HintSize="222,146">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_114" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_67" sap:VirtualizedContainerService.HintSize="264,184">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_33" sap:VirtualizedContainerService.HintSize="464,332" />
      <sap2010:ViewStateData Id="Assign_116" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_69" sap:VirtualizedContainerService.HintSize="264,184">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_35" sap:VirtualizedContainerService.HintSize="464,332" />
      <sap2010:ViewStateData Id="Assign_115" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_68" sap:VirtualizedContainerService.HintSize="264,184">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_34" sap:VirtualizedContainerService.HintSize="464,332" />
      <sap2010:ViewStateData Id="Assign_117" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="Assign_118" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="Sequence_66" sap:VirtualizedContainerService.HintSize="486,1400">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_32" sap:VirtualizedContainerService.HintSize="1217,1548" />
      <sap2010:ViewStateData Id="Sequence_65" sap:VirtualizedContainerService.HintSize="1239,3020">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_15" sap:VirtualizedContainerService.HintSize="1269,3168">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_17" sap:VirtualizedContainerService.HintSize="1299,3316" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_8" sap:VirtualizedContainerService.HintSize="476,22" />
      <sap2010:ViewStateData Id="MessageBox_17" sap:VirtualizedContainerService.HintSize="1188,22" />
      <sap2010:ViewStateData Id="Assign_71" sap:VirtualizedContainerService.HintSize="1188,60" />
      <sap2010:ViewStateData Id="Assign_72" sap:VirtualizedContainerService.HintSize="1188,60" />
      <sap2010:ViewStateData Id="Assign_73" sap:VirtualizedContainerService.HintSize="1188,60" />
      <sap2010:ViewStateData Id="Assign_74" sap:VirtualizedContainerService.HintSize="1188,60" />
      <sap2010:ViewStateData Id="Assign_75" sap:VirtualizedContainerService.HintSize="1188,60" />
      <sap2010:ViewStateData Id="Assign_76" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_35" sap:VirtualizedContainerService.HintSize="264,181">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_77" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_36" sap:VirtualizedContainerService.HintSize="264,181">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_1" sap:VirtualizedContainerService.HintSize="1188,160">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="MessageBox_15" sap:VirtualizedContainerService.HintSize="1188,22" />
      <sap2010:ViewStateData Id="Assign_78" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_37" sap:VirtualizedContainerService.HintSize="264,181">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_79" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_38" sap:VirtualizedContainerService.HintSize="264,181">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_2" sap:VirtualizedContainerService.HintSize="1188,160">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_80" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_39" sap:VirtualizedContainerService.HintSize="264,184">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_81" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_40" sap:VirtualizedContainerService.HintSize="264,184">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_3" sap:VirtualizedContainerService.HintSize="1182,160">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="MessageBox_12" sap:VirtualizedContainerService.HintSize="1182,22" />
      <sap2010:ViewStateData Id="InvokeWorkflow_8" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Sequence_41" sap:VirtualizedContainerService.HintSize="222,146">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_27" sap:VirtualizedContainerService.HintSize="1182,294" />
      <sap2010:ViewStateData Id="Assign_87" sap:VirtualizedContainerService.HintSize="1182,60" />
      <sap2010:ViewStateData Id="Sequence_42" sap:VirtualizedContainerService.HintSize="1210,1804">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_5" sap:VirtualizedContainerService.HintSize="200,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_9" sap:VirtualizedContainerService.HintSize="287,208" />
      <sap2010:ViewStateData Id="Sequence_43" sap:VirtualizedContainerService.HintSize="309,332">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_4" sap:VirtualizedContainerService.HintSize="476,516">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_44" sap:VirtualizedContainerService.HintSize="498,702">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_7" sap:VirtualizedContainerService.HintSize="200,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="CommentOut_2" sap:VirtualizedContainerService.HintSize="1299,147" />
      <sap2010:ViewStateData Id="Sequence_45" sap:VirtualizedContainerService.HintSize="1321,4781">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_28" sap:VirtualizedContainerService.HintSize="1835,4929">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_46" sap:VirtualizedContainerService.HintSize="1857,9685">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="1897,9805" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>