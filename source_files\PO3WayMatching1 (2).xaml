﻿<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:iad="clr-namespace:Infor.Activities.Debug;assembly=Infor.Activities.Debug"
 xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI"
 xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys"
 xmlns:iaw="clr-namespace:Infor.Activities.Workflow;assembly=Infor.Activities.Workflow"
 xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities"
 xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json"
 xmlns:s="clr-namespace:System;assembly=mscorlib"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="includeDatalake" Type="InArgument(x:Boolean)" />
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="commentStatus" Type="OutArgument(x:String)" />
    <x:Property Name="DictOcrValues" Type="InArgument(scg:Dictionary(x:String, x:Object))" />
    <x:Property Name="vendorId" Type="InArgument(x:String)" />
    <x:Property Name="projectPath" Type="InArgument(x:String)" />
    <x:Property Name="company" Type="InOutArgument(x:String)" />
    <x:Property Name="division" Type="InOutArgument(x:String)" />
    <x:Property Name="APResp" Type="InArgument(x:String)" />
    <x:Property Name="correlationID" Type="OutArgument(x:String)" />
    <x:Property Name="authUser" Type="InArgument(x:String)" />
    <x:Property Name="miscValues" Type="InArgument(scg:Dictionary(x:String, x:Object))" />
    <x:Property Name="vatCodeConfig" Type="InArgument(x:String)" />
    <x:Property Name="chargeCode" Type="InArgument(x:String)" />
    <x:Property Name="ListocrLineValues" Type="InArgument(scg:List(scg:Dictionary(x:String, x:Object)))" />
    <x:Property Name="logFile" Type="InArgument(x:String)" />
    <x:Property Name="approvalList" Type="OutArgument(scg:List(x:String))" />
    <x:Property Name="vendorName" Type="OutArgument(x:String)" />
    <x:Property Name="status" Type="OutArgument(x:String)" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Debug</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Infor.Activities.IONAPI</x:String>
      <x:String>Infor.Activities.Sys</x:String>
      <x:String>Infor.Activities.Workflow</x:String>
      <x:String>Infor.RPA.Utilities</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>System</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Linq</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>Microsoft.VisualBasic.CompilerServices</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Debug</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>Infor.Activities.IONAPI</AssemblyReference>
      <AssemblyReference>Infor.Activities.Sys</AssemblyReference>
      <AssemblyReference>Infor.Activities.Workflow</AssemblyReference>
      <AssemblyReference>Infor.RPA.Utilities</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>System.Collections.Immutable</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_46">
    <Sequence.Variables>
      <Variable x:TypeArguments="x:String" Name="inbnValue" />
      <Variable x:TypeArguments="scg:List(x:String)" Name="PONumbers" />
      <Variable x:TypeArguments="x:String" Name="pono" />
      <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="POocrWorkflowOutput" />
      <Variable x:TypeArguments="x:Int32" Name="POocrWorkflowStatus" />
      <Variable x:TypeArguments="x:String" Name="ivdate" />
      <Variable x:TypeArguments="scg:List(x:String)" Name="vendorResult" />
      <Variable x:TypeArguments="x:String" Name="cuam" />
      <Variable x:TypeArguments="x:String" Name="sino" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="respObj0" />
      <Variable x:TypeArguments="x:Int32" Name="StatusCode0" />
      <Variable x:TypeArguments="x:String" Name="tepy" />
      <Variable x:TypeArguments="x:String" Name="req2" />
      <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="linesOcrWorkflowOutput" />
      <Variable x:TypeArguments="x:Int32" Name="linesOcrWorkflowStatus" />
      <Variable x:TypeArguments="x:String" Name="diffamt" />
      <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="AddLineOcrWorkflowOutput" />
      <Variable x:TypeArguments="x:Int32" Name="AddLineOcrWorkflowStatus" />
      <Variable x:TypeArguments="x:String" Name="chargeDiff" />
      <Variable x:TypeArguments="x:String" Name="countryCode" />
      <Variable x:TypeArguments="x:String" Name="bkid" />
      <Variable x:TypeArguments="x:Boolean" Name="InvoiceAlreadyExists" />
      <Variable x:TypeArguments="x:String" Name="authUsertemp" />
      <Variable x:TypeArguments="x:String" Name="cucd" />
      <Variable x:TypeArguments="x:String" Name="pyme" />
      <Variable x:TypeArguments="x:String" Name="geoc" />
      <Variable x:TypeArguments="scg:List(s:String[])" Name="M3TotalTableRows" />
      <Variable x:TypeArguments="scg:List(s:String[])" Name="M3TotalTableRows1" />
      <Variable x:TypeArguments="x:Int32" Default="0" Name="Itr" />
    </Sequence.Variables>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_119">
      <Assign.To>
        <OutArgument x:TypeArguments="scg:List(x:String)">[approvalList]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="scg:List(x:String)">[new list(Of string)]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_120">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[vendorName]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">
          <Literal x:TypeArguments="x:String" Value="" />
        </InArgument>
      </Assign.Value>
    </Assign>
    <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for vendor name" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_1" Response="[respObj0]" StatusCode="[StatusCode0]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/CRS620MI/GetBasicData&quot;]">
      <iai:IONAPIRequestWizard.Headers>
        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>Accept</x:String>
          </scg:List>
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>application/json</x:String>
          </scg:List>
        </scg:List>
      </iai:IONAPIRequestWizard.Headers>
      <iai:IONAPIRequestWizard.QueryParameters>
        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
          <scg:List x:TypeArguments="x:String" Capacity="8">
            <x:String>extendedresult</x:String>
            <x:String>format</x:String>
            <x:String>righttrim</x:String>
            <x:String>excludeempty</x:String>
            <x:String>dateformat</x:String>
            <x:String>SUNO</x:String>
          </scg:List>
          <scg:List x:TypeArguments="x:String" Capacity="8">
            <x:String>false</x:String>
            <x:String>PRETTY</x:String>
            <x:String>true</x:String>
            <x:String>false</x:String>
            <x:String>YMD8</x:String>
            <x:String>vendorId</x:String>
          </scg:List>
        </scg:List>
      </iai:IONAPIRequestWizard.QueryParameters>
    </iai:IONAPIRequestWizard>
    <If Condition="[StatusCode0 = 200]" DisplayName="If fetching vendor details" sap2010:WorkflowViewState.IdRef="If_4">
      <If.Then>
        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_8">
          <Sequence.Variables>
            <Variable x:TypeArguments="njl:JToken" Name="out0" />
          </Sequence.Variables>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_1">
            <Assign.To>
              <OutArgument x:TypeArguments="njl:JToken">[out0]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj0.ReadAsText)]</InArgument>
            </Assign.Value>
          </Assign>
          <If Condition="[out0(&quot;results&quot;)(0)(&quot;records&quot;).ToString &lt;&gt; &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_3">
            <If.Then>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_6">
                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_1">
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_2">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[tepy]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[out0("results")(0)("records")(0)("TEPY").ToString]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_3">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[pyme]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[out0("results")(0)("records")(0)("PYME").ToString]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_4">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[countryCode]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[out0("results")(0)("records")(0)("CSCD").ToString]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_5">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[cucd]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[out0("results")(0)("records")(0)("CUCD").ToString]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_6">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[vendorName]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[out0("results")(0)("records")(0)("SUNM").ToString]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_7">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[geoc]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[out0("results")(0)("records")(0)("GEOC").ToString]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_83">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[authUsertemp]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[out0("results")(0)("records")(0)("RESP").ToString]</InArgument>
                    </Assign.Value>
                  </Assign>
                </Sequence>
                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_5">
                  <Sequence.Variables>
                    <Variable x:TypeArguments="x:String" Name="SupAcho" />
                    <Variable x:TypeArguments="x:Int32" Name="StatusCode4" />
                    <Variable x:TypeArguments="iru:ResponseObject" Name="respObj4" />
                    <Variable x:TypeArguments="njl:JToken" Name="out4" />
                    <Variable x:TypeArguments="s:DateTime" Name="ivdate1" />
                  </Sequence.Variables>
                  <Assign DisplayName="Assign SupAcho" sap2010:WorkflowViewState.IdRef="Assign_8">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[SupAcho]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">["ACHO:"+vendorId + " AND BKTP: 03"]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for bkid" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_2" Response="[respObj4]" StatusCode="[StatusCode4]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/CRS692MI/SearchBnkAcc&quot;]">
                    <iai:IONAPIRequestWizard.Headers>
                      <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                        <scg:List x:TypeArguments="x:String" Capacity="4">
                          <x:String>Accept</x:String>
                        </scg:List>
                        <scg:List x:TypeArguments="x:String" Capacity="4">
                          <x:String>application/json</x:String>
                        </scg:List>
                      </scg:List>
                    </iai:IONAPIRequestWizard.Headers>
                    <iai:IONAPIRequestWizard.QueryParameters>
                      <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                        <scg:List x:TypeArguments="x:String" Capacity="8">
                          <x:String>SQRY</x:String>
                          <x:String>dateformat</x:String>
                          <x:String>excludeempty</x:String>
                          <x:String>righttrim</x:String>
                          <x:String>format</x:String>
                          <x:String>extendedresult</x:String>
                        </scg:List>
                        <scg:List x:TypeArguments="x:String" Capacity="8">
                          <x:String>SupAcho</x:String>
                          <x:String>YMD8</x:String>
                          <x:String>false</x:String>
                          <x:String>true</x:String>
                          <x:String>PRETTY</x:String>
                          <x:String>false</x:String>
                        </scg:List>
                      </scg:List>
                    </iai:IONAPIRequestWizard.QueryParameters>
                  </iai:IONAPIRequestWizard>
                  <If Condition="[StatusCode4 = 200]" sap2010:WorkflowViewState.IdRef="If_2">
                    <If.Then>
                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_4">
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_9">
                          <Assign.To>
                            <OutArgument x:TypeArguments="njl:JToken">[out4]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj4.ReadAsText)]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <If Condition="[out4(&quot;results&quot;)(0)(&quot;records&quot;).ToString &lt;&gt; &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_1">
                          <If.Then>
                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_2">
                              <Assign DisplayName="Assign bkid" sap2010:WorkflowViewState.IdRef="Assign_10">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[bkid]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[(out4("results")(0)("records")(0)("BKID")).ToString]</InArgument>
                                </Assign.Value>
                              </Assign>
                            </Sequence>
                          </If.Then>
                          <If.Else>
                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_3">
                              <Assign DisplayName="Assign bkid" sap2010:WorkflowViewState.IdRef="Assign_11">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[bkid]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">
                                    <Literal x:TypeArguments="x:String" Value="" />
                                  </InArgument>
                                </Assign.Value>
                              </Assign>
                            </Sequence>
                          </If.Else>
                        </If>
                      </Sequence>
                    </If.Then>
                  </If>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_12">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[cuam]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[DictOcrValues("TOTAL").Tostring]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_13">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[sino]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[DictOcrValues("INVOICE_RECEIPT_ID").Tostring]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign DisplayName="Assign ivdate" sap2010:WorkflowViewState.IdRef="Assign_14">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[ivdate]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[DateTime.ParseExact(DictOcrValues("INVOICE_RECEIPT_DATE").Tostring, "dd/MM/yyyy", Nothing).ToString("yyyyMMdd")]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign DisplayName="Assign coID" sap2010:WorkflowViewState.IdRef="Assign_15">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[correlationID]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[System.DateTime.Now.ToString("yyyyMMddHHmmssfff").tostring + " -"+ DictOcrValues("INVOICE_RECEIPT_ID").Tostring + "-" + division + "-" + vendorID]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_1">
                    <TryCatch.Try>
                      <Assign DisplayName="Assign coID" sap2010:WorkflowViewState.IdRef="Assign_16">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[correlationID]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[correlationID.Substring(0,35)]</InArgument>
                        </Assign.Value>
                      </Assign>
                    </TryCatch.Try>
                    <TryCatch.Catches>
                      <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_1">
                        <ActivityAction x:TypeArguments="s:Exception">
                          <ActivityAction.Argument>
                            <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                          </ActivityAction.Argument>
                          <Assign DisplayName="Assign coID" sap2010:WorkflowViewState.IdRef="Assign_17">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:String">[correlationID]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String">[correlationID]</InArgument>
                            </Assign.Value>
                          </Assign>
                        </ActivityAction>
                      </Catch>
                    </TryCatch.Catches>
                  </TryCatch>
                </Sequence>
              </Sequence>
            </If.Then>
            <If.Else>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_7">
                <Assign sap2010:WorkflowViewState.IdRef="Assign_18">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[(out0("results")(0)("errorMessage")).ToString]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_19">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
                  </Assign.Value>
                </Assign>
                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_1" Line="[commentStatus]" Source="[logfile]" />
              </Sequence>
            </If.Else>
          </If>
        </Sequence>
      </If.Then>
      <If.Else>
        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_9">
          <Assign sap2010:WorkflowViewState.IdRef="Assign_20">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">["Received an IONAPI error while fetching Vendor name for the Vendor Id " + vendorId+"."]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_21">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
            </Assign.Value>
          </Assign>
          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_2" Line="[commentStatus]" Source="[logfile]" />
        </Sequence>
      </If.Else>
    </If>
    <If Condition="[miscValues(&quot;BusinessRuleAPResp&quot;).ToString.ToUpper() &lt;&gt; &quot;NA&quot;]" sap2010:WorkflowViewState.IdRef="If_7">
      <If.Then>
        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_10">
          <Sequence.Variables>
            <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="APRespRespDict" />
            <Variable x:TypeArguments="x:Int32" Name="APRespRespCode" />
          </Sequence.Variables>
          <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;division&quot;,division},{&quot;company&quot;,company},{&quot;VendorID&quot;,vendorID}}]" ContinueOnError="True" DisplayName="AP resp Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_1" OutputArguments="[APRespRespDict]" ResponseCode="[APRespRespCode]" WorkflowFile="[projectPath+&quot;\APResp.xaml&quot;]" />
          <If Condition="[APRespRespCode = 200]" sap2010:WorkflowViewState.IdRef="If_5">
            <If.Then>
              <Assign sap2010:WorkflowViewState.IdRef="Assign_22">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:String">[APResp]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:String">[CType(APRespRespDict("APResp"), String)]</InArgument>
                </Assign.Value>
              </Assign>
            </If.Then>
          </If>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_23">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[authUser]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[APResp]</InArgument>
            </Assign.Value>
          </Assign>
        </Sequence>
      </If.Then>
      <If.Else>
        <If Condition="[authUser.ToUpper() = &quot;SUPPLIERAUTHUSER&quot;]" sap2010:WorkflowViewState.IdRef="If_6">
          <If.Then>
            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_11">
              <Assign sap2010:WorkflowViewState.IdRef="Assign_24">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:String">[authUser]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:String">[authUsertemp]</InArgument>
                </Assign.Value>
              </Assign>
            </Sequence>
          </If.Then>
        </If>
      </If.Else>
    </If>
    <Sequence DisplayName="Sequence creating header" sap2010:WorkflowViewState.IdRef="Sequence_16">
      <Sequence.Variables>
        <Variable x:TypeArguments="x:Int32" Name="StatusCode5" />
        <Variable x:TypeArguments="iru:ResponseObject" Name="respObj5" />
        <Variable x:TypeArguments="njl:JToken" Name="out5" />
      </Sequence.Variables>
      <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Invoice header" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_3" Response="[respObj5]" StatusCode="[StatusCode5]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/AddHead&quot;]">
        <iai:IONAPIRequestWizard.Headers>
          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
            <scg:List x:TypeArguments="x:String" Capacity="4">
              <x:String>Accept</x:String>
            </scg:List>
            <scg:List x:TypeArguments="x:String" Capacity="4">
              <x:String>application/json</x:String>
            </scg:List>
          </scg:List>
        </iai:IONAPIRequestWizard.Headers>
        <iai:IONAPIRequestWizard.QueryParameters>
          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
            <scg:List x:TypeArguments="x:String" Capacity="32">
              <x:String>SUNO</x:String>
              <x:String>IVDT</x:String>
              <x:String>DIVI</x:String>
              <x:String>SINO</x:String>
              <x:String>CUCD</x:String>
              <x:String>TEPY</x:String>
              <x:String>PYME</x:String>
              <x:String>CUAM</x:String>
              <x:String>IMCD</x:String>
              <x:String>CRTP</x:String>
              <x:String>dateformat</x:String>
              <x:String>excludeempty</x:String>
              <x:String>righttrim</x:String>
              <x:String>format</x:String>
              <x:String>extendedresult</x:String>
              <x:String>APCD</x:String>
              <x:String>CORI</x:String>
            </scg:List>
            <scg:List x:TypeArguments="x:String" Capacity="32">
              <x:String>vendorId</x:String>
              <x:String>ivdate</x:String>
              <x:String>division</x:String>
              <x:String>sino</x:String>
              <x:String>cucd</x:String>
              <x:String>tepy</x:String>
              <x:String>pyme</x:String>
              <x:String>cuam</x:String>
              <x:String>1</x:String>
              <x:String>1</x:String>
              <x:String>YMD8</x:String>
              <x:String>false</x:String>
              <x:String>true</x:String>
              <x:String>PRETTY</x:String>
              <x:String>false</x:String>
              <x:String>authUser</x:String>
              <x:String>correlationID</x:String>
            </scg:List>
          </scg:List>
        </iai:IONAPIRequestWizard.QueryParameters>
      </iai:IONAPIRequestWizard>
      <If Condition="[StatusCode5 = 200  and respObj5.ReadAsText.Contains(&quot;Bank account identity must be entered&quot;)]" sap2010:WorkflowViewState.IdRef="If_8">
        <If.Then>
          <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Invoice header" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_4" Response="[respObj5]" StatusCode="[StatusCode5]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/AddHead&quot;]">
            <iai:IONAPIRequestWizard.Headers>
              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>Accept</x:String>
                </scg:List>
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>application/json</x:String>
                </scg:List>
              </scg:List>
            </iai:IONAPIRequestWizard.Headers>
            <iai:IONAPIRequestWizard.QueryParameters>
              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                <scg:List x:TypeArguments="x:String" Capacity="32">
                  <x:String>SUNO</x:String>
                  <x:String>IVDT</x:String>
                  <x:String>DIVI</x:String>
                  <x:String>SINO</x:String>
                  <x:String>CUCD</x:String>
                  <x:String>TEPY</x:String>
                  <x:String>PYME</x:String>
                  <x:String>CUAM</x:String>
                  <x:String>IMCD</x:String>
                  <x:String>CRTP</x:String>
                  <x:String>dateformat</x:String>
                  <x:String>excludeempty</x:String>
                  <x:String>righttrim</x:String>
                  <x:String>format</x:String>
                  <x:String>extendedresult</x:String>
                  <x:String>APCD</x:String>
                  <x:String>BKID</x:String>
                  <x:String>CORI</x:String>
                </scg:List>
                <scg:List x:TypeArguments="x:String" Capacity="32">
                  <x:String>vendorId</x:String>
                  <x:String>ivdate</x:String>
                  <x:String>division</x:String>
                  <x:String>sino</x:String>
                  <x:String>cucd</x:String>
                  <x:String>tepy</x:String>
                  <x:String>pyme</x:String>
                  <x:String>cuam</x:String>
                  <x:String>1</x:String>
                  <x:String>1</x:String>
                  <x:String>YMD8</x:String>
                  <x:String>false</x:String>
                  <x:String>true</x:String>
                  <x:String>PRETTY</x:String>
                  <x:String>false</x:String>
                  <x:String>authUser</x:String>
                  <x:String>bkid</x:String>
                  <x:String>correlationID</x:String>
                </scg:List>
              </scg:List>
            </iai:IONAPIRequestWizard.QueryParameters>
          </iai:IONAPIRequestWizard>
        </If.Then>
      </If>
      <If Condition="[StatusCode5 = 200]" sap2010:WorkflowViewState.IdRef="If_11">
        <If.Then>
          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_14">
            <Sequence.Variables>
              <Variable x:TypeArguments="x:Int32" Name="respout1" />
              <Variable x:TypeArguments="x:String" Name="additionalChargeExcp" />
              <Variable x:TypeArguments="x:Int32" Name="m" />
            </Sequence.Variables>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_25">
              <Assign.To>
                <OutArgument x:TypeArguments="njl:JToken">[out5]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj5.ReadAsText)]</InArgument>
              </Assign.Value>
            </Assign>
            <If Condition="[out5(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_10">
              <If.Then>
                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_12">
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_26">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:Boolean">[InvoiceAlreadyExists]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_27">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[inbnValue]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">
                        <Literal x:TypeArguments="x:String" Value="" />
                      </InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_28">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[(out5("results")(0)("errorMessage")).ToString]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <If Condition="[respObj5.ReadAsText.Contains(&quot;Bank account identity must be entered&quot;)]" sap2010:WorkflowViewState.IdRef="If_9">
                    <If.Then>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_29">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">NEEDSVERIFICATION</InArgument>
                        </Assign.Value>
                      </Assign>
                    </If.Then>
                    <If.Else>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_30">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                        </Assign.Value>
                      </Assign>
                    </If.Else>
                  </If>
                </Sequence>
              </If.Then>
              <If.Else>
                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_13">
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_31">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:Boolean">[InvoiceAlreadyExists]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_32">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[inbnValue]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[(out5("results")(0)("records")(0)("INBN")).ToString]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_33">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">["Invoice Header Created"]</InArgument>
                    </Assign.Value>
                  </Assign>
                </Sequence>
              </If.Else>
            </If>
          </Sequence>
        </If.Then>
        <If.Else>
          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_15">
            <Assign sap2010:WorkflowViewState.IdRef="Assign_34">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">["Received an IONAPI error while adding Invoice header."]</InArgument>
              </Assign.Value>
            </Assign>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_35">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
              </Assign.Value>
            </Assign>
            <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_3" Line="[commentStatus]" Source="[logfile]" />
          </Sequence>
        </If.Else>
      </If>
    </Sequence>
    <If Condition="[InvoiceAlreadyExists]" sap2010:WorkflowViewState.IdRef="If_28">
      <If.Then>
        <If Condition="[includeDatalake]" sap2010:WorkflowViewState.IdRef="If_19">
          <If.Then>
            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_119">
              <Sequence.Variables>
                <Variable x:TypeArguments="x:String" Name="checkInvoiceSqry" />
                <Variable x:TypeArguments="iru:ResponseObject" Name="checkInvoiceExportMIResponse" />
                <Variable x:TypeArguments="x:Int32" Name="checkInvoiceExportMIResponseCode" />
                <Variable x:TypeArguments="scg:List(x:String)" Name="parts" />
                <Variable x:TypeArguments="x:String" Name="puno" />
                <Variable x:TypeArguments="x:String" Name="pnls" />
                <Variable x:TypeArguments="x:String" Name="pnli" />
              </Sequence.Variables>
              <Assign DisplayName="SINO queryAssign" sap2010:WorkflowViewState.IdRef="Assign_196">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:String">[checkInvoiceSqry]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:String">["E5INBN from FAPIBH where E5SINO = '"+DictOcrValues("INVOICE_RECEIPT_ID").ToString+"'" + " and E5DIVI = '" + division + "'" + " and E5SUNO = '" + vendorID + "'"]</InArgument>
                </Assign.Value>
              </Assign>
              <iai:IONAPIRequestWizard FileAttachments="{x:Null}" Headers="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="Invoice available in APS450 IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_11" Response="[checkInvoiceExportMIResponse]" StatusCode="[checkInvoiceExportMIResponseCode]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/EXPORTMI/Select?SEPC=~&amp;HDRS=0&amp;dateformat=YMD8&amp;excludeempty=false&amp;righttrim=true&amp;format=PRETTY&amp;extendedresult=false&quot;]">
                <iai:IONAPIRequestWizard.QueryParameters>
                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                    <scg:List x:TypeArguments="x:String" Capacity="4">
                      <x:String>QERY</x:String>
                    </scg:List>
                    <scg:List x:TypeArguments="x:String" Capacity="4">
                      <x:String>checkInvoiceSqry</x:String>
                    </scg:List>
                  </scg:List>
                </iai:IONAPIRequestWizard.QueryParameters>
              </iai:IONAPIRequestWizard>
              <If Condition="[checkInvoiceExportMIResponseCode=200]" DisplayName="Invoice available in APS450 If" sap2010:WorkflowViewState.IdRef="If_64">
                <If.Then>
                  <If Condition="[not checkInvoiceExportMIResponse.readasjson(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_63">
                    <If.Then>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_203">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[inbnValue]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[checkInvoiceExportMIResponse.readasjson("results")(0)("records")(0)("REPL").ToString().split("~"C)(0).ToString]</InArgument>
                        </Assign.Value>
                      </Assign>
                    </If.Then>
                  </If>
                </If.Then>
              </If>
              <ForEach x:TypeArguments="scg:Dictionary(x:String, x:Object)" DisplayName="ForEach&lt;Dictionary&lt;String,Object&gt;&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_27" Values="[ListocrLineValues]">
                <ActivityAction x:TypeArguments="scg:Dictionary(x:String, x:Object)">
                  <ActivityAction.Argument>
                    <DelegateInArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)" Name="item" />
                  </ActivityAction.Argument>
                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_131">
                    <Sequence.Variables>
                      <Variable x:TypeArguments="iru:ResponseObject" Name="respObj7" />
                      <Variable x:TypeArguments="x:Int32" Name="StatusCode7" />
                      <Variable x:TypeArguments="njl:JToken" Name="out7" />
                    </Sequence.Variables>
                    <If Condition="[item(&quot;DESCRIPTION&quot;).ToString.Contains(&quot;~&quot;)]" sap2010:WorkflowViewState.IdRef="If_71">
                      <If.Then>
                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_143">
                          <Sequence.Variables>
                            <Variable x:TypeArguments="scg:Dictionary(x:String, x:Object)" Name="LinesDict" />
                            <Variable x:TypeArguments="x:String" Name="AIT1" />
                            <Variable x:TypeArguments="x:String" Name="AIT2" />
                            <Variable x:TypeArguments="x:String" Name="AIT3" />
                            <Variable x:TypeArguments="x:String" Name="AIT4" />
                            <Variable x:TypeArguments="x:String" Name="AIT5" />
                            <Variable x:TypeArguments="x:String" Name="AIT6" />
                            <Variable x:TypeArguments="x:String" Name="AIT7" />
                            <Variable x:TypeArguments="x:String" Name="amt" />
                            <Variable x:TypeArguments="iru:ResponseObject" Name="respObj2" />
                            <Variable x:TypeArguments="x:Int32" Name="StatusCode2" />
                            <Variable x:TypeArguments="njl:JToken" Name="out1" />
                          </Sequence.Variables>
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_253">
                            <Assign.To>
                              <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[New Dictionary (Of String, Object)]</InArgument>
                            </Assign.Value>
                          </Assign>
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_254">
                            <Assign.To>
                              <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[item]</InArgument>
                            </Assign.Value>
                          </Assign>
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_255">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:String">[AIT1]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(0)]</InArgument>
                            </Assign.Value>
                          </Assign>
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_256">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:String">[AIT2]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(1)]</InArgument>
                            </Assign.Value>
                          </Assign>
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_257">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:String">[AIT3]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(2)]</InArgument>
                            </Assign.Value>
                          </Assign>
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_258">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:String">[AIT4]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(3)]</InArgument>
                            </Assign.Value>
                          </Assign>
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_259">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:String">[AIT5]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(4)]</InArgument>
                            </Assign.Value>
                          </Assign>
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_260">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:String">[AIT6]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(5)]</InArgument>
                            </Assign.Value>
                          </Assign>
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_261">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:String">[AIT7]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(6)]</InArgument>
                            </Assign.Value>
                          </Assign>
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_262">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:String">[amt]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String">[LinesDict("LINE_AMOUNT").ToString]</InArgument>
                            </Assign.Value>
                          </Assign>
                          <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding lines" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_20" Response="[respObj2]" StatusCode="[StatusCode2]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                            <iai:IONAPIRequestWizard.Headers>
                              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                <scg:List x:TypeArguments="x:String" Capacity="4">
                                  <x:String>Accept</x:String>
                                </scg:List>
                                <scg:List x:TypeArguments="x:String" Capacity="4">
                                  <x:String>application/json</x:String>
                                </scg:List>
                              </scg:List>
                            </iai:IONAPIRequestWizard.Headers>
                            <iai:IONAPIRequestWizard.QueryParameters>
                              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                <scg:List x:TypeArguments="x:String" Capacity="16">
                                  <x:String>INBN</x:String>
                                  <x:String>RDTP</x:String>
                                  <x:String>DIVI</x:String>
                                  <x:String>NLAM</x:String>
                                  <x:String>AIT1</x:String>
                                  <x:String>AIT2</x:String>
                                  <x:String>AIT3</x:String>
                                  <x:String>AIT4</x:String>
                                  <x:String>AIT5</x:String>
                                  <x:String>AIT6</x:String>
                                  <x:String>AIT7</x:String>
                                </scg:List>
                                <scg:List x:TypeArguments="x:String" Capacity="16">
                                  <x:String>inbnValue</x:String>
                                  <x:String>8</x:String>
                                  <x:String>division</x:String>
                                  <x:String>amt</x:String>
                                  <x:String>AIT1</x:String>
                                  <x:String>AIT2</x:String>
                                  <x:String>AIT3</x:String>
                                  <x:String>AIT4</x:String>
                                  <x:String>AIT5</x:String>
                                  <x:String>AIT6</x:String>
                                  <x:String>AIT7</x:String>
                                </scg:List>
                              </scg:List>
                            </iai:IONAPIRequestWizard.QueryParameters>
                          </iai:IONAPIRequestWizard>
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_263">
                            <Assign.To>
                              <OutArgument x:TypeArguments="njl:JToken">[out1]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj2.ReadAsText)]</InArgument>
                            </Assign.Value>
                          </Assign>
                          <If Condition="[out1(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_76">
                            <If.Then>
                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_141">
                                <Assign sap2010:WorkflowViewState.IdRef="Assign_264">
                                  <Assign.To>
                                    <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                  </Assign.To>
                                  <Assign.Value>
                                    <InArgument x:TypeArguments="x:String" xml:space="preserve">["Error occured while adding the line.  "  +  (out1("results")(0)("errorMessage")).ToString]</InArgument>
                                  </Assign.Value>
                                </Assign>
                                <Assign sap2010:WorkflowViewState.IdRef="Assign_265">
                                  <Assign.To>
                                    <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                  </Assign.To>
                                  <Assign.Value>
                                    <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                                  </Assign.Value>
                                </Assign>
                                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_27" Line="[commentStatus]" Source="[logfile]" />
                              </Sequence>
                            </If.Then>
                            <If.Else>
                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_142">
                                <Assign sap2010:WorkflowViewState.IdRef="Assign_266">
                                  <Assign.To>
                                    <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                  </Assign.To>
                                  <Assign.Value>
                                    <InArgument x:TypeArguments="x:String">["Invoice Line created"]</InArgument>
                                  </Assign.Value>
                                </Assign>
                                <Assign sap2010:WorkflowViewState.IdRef="Assign_267">
                                  <Assign.To>
                                    <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                  </Assign.To>
                                  <Assign.Value>
                                    <InArgument x:TypeArguments="x:String">["SUCCESS"]</InArgument>
                                  </Assign.Value>
                                </Assign>
                                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_28" Line="[commentStatus]" Source="[logfile]" />
                              </Sequence>
                            </If.Else>
                          </If>
                        </Sequence>
                      </If.Then>
                      <If.Else>
                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_135">
                          <Sequence.Variables>
                            <Variable x:TypeArguments="x:String" Name="itno" />
                          </Sequence.Variables>
                          <Assign DisplayName="Assign parts" sap2010:WorkflowViewState.IdRef="Assign_235">
                            <Assign.To>
                              <OutArgument x:TypeArguments="scg:List(x:String)">[parts]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="scg:List(x:String)">[New List(Of String)]</InArgument>
                            </Assign.Value>
                          </Assign>
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_301">
                            <Assign.To>
                              <OutArgument x:TypeArguments="scg:List(x:String)">[parts]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="scg:List(x:String)">[item("SUPPLIER_ITEM_CODE").ToString.Split("("c).ToList]</InArgument>
                            </Assign.Value>
                          </Assign>
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_302">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:String">[itno]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String">[item("SUPPLIER_ITEM_CODE").ToString.Split("("c)(0)]</InArgument>
                            </Assign.Value>
                          </Assign>
                          <If Condition="[parts.count &gt; 0]" sap2010:WorkflowViewState.IdRef="If_85">
                            <If.Then>
                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_153">
                                <ForEach x:TypeArguments="x:String" DisplayName="ForEach&lt;String&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_29" Values="[parts]">
                                  <ActivityAction x:TypeArguments="x:String">
                                    <ActivityAction.Argument>
                                      <DelegateInArgument x:TypeArguments="x:String" Name="item2" />
                                    </ActivityAction.Argument>
                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_154">
                                      <Sequence.Variables>
                                        <Variable x:TypeArguments="x:String" Name="totPO" />
                                      </Sequence.Variables>
                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_303">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:String">[totPO]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:String">[item2.Split(","c)(1)]</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_307">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:String">[puno]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:String">[totPO.Split("/"c)(0)]</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_308">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:String">[pnli]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:String">[totPO.Split("/"c)(1)]</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_309">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:String">[pnls]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:String">[totPO.Split("/"c)(2)]</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                      <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_17" Response="[respObj7]" StatusCode="[StatusCode7]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/PPS200MI/GetLine?dateformat=YMD8&amp;excludeempty=false&amp;righttrim=true&amp;format=PRETTY&amp;extendedresult=false&quot;]">
                                        <iai:IONAPIRequestWizard.Headers>
                                          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                            <scg:List x:TypeArguments="x:String" Capacity="4">
                                              <x:String>Accept</x:String>
                                            </scg:List>
                                            <scg:List x:TypeArguments="x:String" Capacity="4">
                                              <x:String>application/json</x:String>
                                            </scg:List>
                                          </scg:List>
                                        </iai:IONAPIRequestWizard.Headers>
                                        <iai:IONAPIRequestWizard.QueryParameters>
                                          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                            <scg:List x:TypeArguments="x:String" Capacity="4">
                                              <x:String>PNLI</x:String>
                                              <x:String>PUNO</x:String>
                                            </scg:List>
                                            <scg:List x:TypeArguments="x:String" Capacity="4">
                                              <x:String>pnli</x:String>
                                              <x:String>puno</x:String>
                                            </scg:List>
                                          </scg:List>
                                        </iai:IONAPIRequestWizard.QueryParameters>
                                      </iai:IONAPIRequestWizard>
                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_241">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="njl:JToken">[out7]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj7.ReadAsText)]</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                      <If Condition="[out7(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_75">
                                        <If.Then>
                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_136">
                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_242">
                                              <Assign.To>
                                                <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                              </Assign.To>
                                              <Assign.Value>
                                                <InArgument x:TypeArguments="x:String">["Purchase price UOM and purchase order UOM are not available."]</InArgument>
                                              </Assign.Value>
                                            </Assign>
                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_243">
                                              <Assign.To>
                                                <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                              </Assign.To>
                                              <Assign.Value>
                                                <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                                              </Assign.Value>
                                            </Assign>
                                            <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_24" Line="[commentStatus]" Source="[logfile]" />
                                          </Sequence>
                                        </If.Then>
                                        <If.Else>
                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_140">
                                            <Sequence.Variables>
                                              <Variable x:TypeArguments="njl:JToken" Name="out8" />
                                              <Variable x:TypeArguments="x:Int32" Name="StatusCode8" />
                                              <Variable x:TypeArguments="iru:ResponseObject" Name="respObj8" />
                                              <Variable x:TypeArguments="x:Int32" Name="m" />
                                              <Variable x:TypeArguments="x:String" Name="ppun" />
                                              <Variable x:TypeArguments="x:String" Name="puun" />
                                              <Variable x:TypeArguments="iru:ResponseObject" Name="respObj2" />
                                              <Variable x:TypeArguments="x:Int32" Name="StatusCode2" />
                                              <Variable x:TypeArguments="x:String" Name="repn" />
                                              <Variable x:TypeArguments="x:String" Name="ivqa" />
                                              <Variable x:TypeArguments="x:String" Name="grpr" />
                                            </Sequence.Variables>
                                            <Assign DisplayName="Assign ppun" sap2010:WorkflowViewState.IdRef="Assign_244">
                                              <Assign.To>
                                                <OutArgument x:TypeArguments="x:String">[ppun]</OutArgument>
                                              </Assign.To>
                                              <Assign.Value>
                                                <InArgument x:TypeArguments="x:String">[out7("results")(0)("records")(0)("PPUN").ToString]</InArgument>
                                              </Assign.Value>
                                            </Assign>
                                            <Assign DisplayName="Assign puun" sap2010:WorkflowViewState.IdRef="Assign_245">
                                              <Assign.To>
                                                <OutArgument x:TypeArguments="x:String">[puun]</OutArgument>
                                              </Assign.To>
                                              <Assign.Value>
                                                <InArgument x:TypeArguments="x:String">[out7("results")(0)("records")(0)("PUUN").ToString]</InArgument>
                                              </Assign.Value>
                                            </Assign>
                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_246">
                                              <Assign.To>
                                                <OutArgument x:TypeArguments="x:String">[req2]</OutArgument>
                                              </Assign.To>
                                              <Assign.Value>
                                                <InArgument x:TypeArguments="x:String" xml:space="preserve">["F2REPN,F2SCOC,F2RPQA,F2IVQA  from FGRECL where F2DIVI = " + DIVISION+" AND F2PUNO = " + puno + " AND F2PNLI = " + pnli + " AND F2PNLS = " + pnls + " AND F2IMST != 9"]</InArgument>
                                              </Assign.Value>
                                            </Assign>
                                            <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="Export MI lines IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_18" Response="[respObj2]" StatusCode="[StatusCode2]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/EXPORTMI/Select?HDRS=0&amp;dateformat=YMD8&amp;excludeempty=false&amp;righttrim=true&amp;format=PRETTY&amp;extendedresult=false&quot;]">
                                              <iai:IONAPIRequestWizard.Headers>
                                                <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                  <scg:List x:TypeArguments="x:String" Capacity="4">
                                                    <x:String>Accept</x:String>
                                                  </scg:List>
                                                  <scg:List x:TypeArguments="x:String" Capacity="4">
                                                    <x:String>application/json</x:String>
                                                  </scg:List>
                                                </scg:List>
                                              </iai:IONAPIRequestWizard.Headers>
                                              <iai:IONAPIRequestWizard.QueryParameters>
                                                <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                  <scg:List x:TypeArguments="x:String" Capacity="4">
                                                    <x:String>QERY</x:String>
                                                    <x:String>SEPC</x:String>
                                                    <x:String>maxrecs</x:String>
                                                  </scg:List>
                                                  <scg:List x:TypeArguments="x:String" Capacity="4">
                                                    <x:String>req2</x:String>
                                                    <x:String>~</x:String>
                                                    <x:String>1000</x:String>
                                                  </scg:List>
                                                </scg:List>
                                              </iai:IONAPIRequestWizard.QueryParameters>
                                            </iai:IONAPIRequestWizard>
                                            <If Condition="[StatusCode2 = 200]" sap2010:WorkflowViewState.IdRef="If_73">
                                              <If.Then>
                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_137">
                                                  <Sequence.Variables>
                                                    <Variable x:TypeArguments="njl:JToken" Name="out2" />
                                                  </Sequence.Variables>
                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_247">
                                                    <Assign.To>
                                                      <OutArgument x:TypeArguments="njl:JToken">[out2]</OutArgument>
                                                    </Assign.To>
                                                    <Assign.Value>
                                                      <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj2.ReadAsText)]</InArgument>
                                                    </Assign.Value>
                                                  </Assign>
                                                  <If Condition="[out2(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_72">
                                                    <If.Else>
                                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_150">
                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_248">
                                                          <Assign.To>
                                                            <OutArgument x:TypeArguments="x:String">[repn]</OutArgument>
                                                          </Assign.To>
                                                          <Assign.Value>
                                                            <InArgument x:TypeArguments="x:String">[out2("results")(0)("records")(0)("REPL").ToString().split("~"C)(0).ToString()]</InArgument>
                                                          </Assign.Value>
                                                        </Assign>
                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_291">
                                                          <Assign.To>
                                                            <OutArgument x:TypeArguments="x:String">[ivqa]</OutArgument>
                                                          </Assign.To>
                                                          <Assign.Value>
                                                            <InArgument x:TypeArguments="x:String">[(Convert.ToDecimal(out2("results")(0)("records")(0)("REPL").ToString().split("~"C)(2)) - Convert.ToDecimal(out2("results")(0)("records")(0)("REPL").ToString().split("~"C)(3))).ToString]</InArgument>
                                                          </Assign.Value>
                                                        </Assign>
                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_292">
                                                          <Assign.To>
                                                            <OutArgument x:TypeArguments="x:String">[grpr]</OutArgument>
                                                          </Assign.To>
                                                          <Assign.Value>
                                                            <InArgument x:TypeArguments="x:String">[out2("results")(0)("records")(0)("REPL").ToString().split("~"C)(1).ToString()]</InArgument>
                                                          </Assign.Value>
                                                        </Assign>
                                                      </Sequence>
                                                    </If.Else>
                                                  </If>
                                                </Sequence>
                                              </If.Then>
                                            </If>
                                            <iad:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_10">
                                              <iad:CommentOut.Activities>
                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_299">
                                                  <Assign.To>
                                                    <OutArgument x:TypeArguments="x:String">[ivqa]</OutArgument>
                                                  </Assign.To>
                                                  <Assign.Value>
                                                    <InArgument x:TypeArguments="x:String">[item("QUANTITY").ToString]</InArgument>
                                                  </Assign.Value>
                                                </Assign>
                                              </iad:CommentOut.Activities>
                                            </iad:CommentOut>
                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_300">
                                              <Assign.To>
                                                <OutArgument x:TypeArguments="x:String">[grpr]</OutArgument>
                                              </Assign.To>
                                              <Assign.Value>
                                                <InArgument x:TypeArguments="x:String">[item("UNIT_PRICE").ToString]</InArgument>
                                              </Assign.Value>
                                            </Assign>
                                            <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding lines" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_19" Response="[respObj8]" StatusCode="[StatusCode8]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                                              <iai:IONAPIRequestWizard.Headers>
                                                <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                  <scg:List x:TypeArguments="x:String" Capacity="4">
                                                    <x:String>Accept</x:String>
                                                  </scg:List>
                                                  <scg:List x:TypeArguments="x:String" Capacity="4">
                                                    <x:String>application/json</x:String>
                                                  </scg:List>
                                                </scg:List>
                                              </iai:IONAPIRequestWizard.Headers>
                                              <iai:IONAPIRequestWizard.QueryParameters>
                                                <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                  <scg:List x:TypeArguments="x:String" Capacity="16">
                                                    <x:String>INBN</x:String>
                                                    <x:String>RDTP</x:String>
                                                    <x:String>DIVI</x:String>
                                                    <x:String>GRPR</x:String>
                                                    <x:String>ITNO</x:String>
                                                    <x:String>IVQA</x:String>
                                                    <x:String>PNLI</x:String>
                                                    <x:String>PUNO</x:String>
                                                    <x:String>RELP</x:String>
                                                    <x:String>REPN</x:String>
                                                    <x:String>PNLS</x:String>
                                                  </scg:List>
                                                  <scg:List x:TypeArguments="x:String" Capacity="16">
                                                    <x:String>inbnValue</x:String>
                                                    <x:String>1</x:String>
                                                    <x:String>division</x:String>
                                                    <x:String>grpr</x:String>
                                                    <x:String>itno</x:String>
                                                    <x:String>ivqa</x:String>
                                                    <x:String>pnli</x:String>
                                                    <x:String>puno</x:String>
                                                    <x:String>1</x:String>
                                                    <x:String>repn</x:String>
                                                    <x:String>pnls</x:String>
                                                  </scg:List>
                                                </scg:List>
                                              </iai:IONAPIRequestWizard.QueryParameters>
                                            </iai:IONAPIRequestWizard>
                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_250">
                                              <Assign.To>
                                                <OutArgument x:TypeArguments="njl:JToken">[out8]</OutArgument>
                                              </Assign.To>
                                              <Assign.Value>
                                                <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj8.ReadAsText)]</InArgument>
                                              </Assign.Value>
                                            </Assign>
                                            <If Condition="[out8(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_74">
                                              <If.Then>
                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_138">
                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_251">
                                                    <Assign.To>
                                                      <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                    </Assign.To>
                                                    <Assign.Value>
                                                      <InArgument x:TypeArguments="x:String">[(out8("results")(0)("errorMessage")).ToString]</InArgument>
                                                    </Assign.Value>
                                                  </Assign>
                                                  <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_25" Line="[commentStatus]" Source="[logfile]" />
                                                </Sequence>
                                              </If.Then>
                                              <If.Else>
                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_139">
                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_252">
                                                    <Assign.To>
                                                      <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                    </Assign.To>
                                                    <Assign.Value>
                                                      <InArgument x:TypeArguments="x:String">Invoice Line has been created</InArgument>
                                                    </Assign.Value>
                                                  </Assign>
                                                  <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_26" Line="[commentStatus]" Source="[logfile]" />
                                                </Sequence>
                                              </If.Else>
                                            </If>
                                          </Sequence>
                                        </If.Else>
                                      </If>
                                    </Sequence>
                                  </ActivityAction>
                                </ForEach>
                              </Sequence>
                            </If.Then>
                          </If>
                          <iad:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_9">
                            <iad:CommentOut.Activities>
                              <Assign DisplayName="Assign parts" sap2010:WorkflowViewState.IdRef="Assign_236">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="s:String[]">[parts]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="s:String[]">[If(item("SUPPLIER_ITEM_CODE").ToString.Split({"("c, ")"c}, StringSplitOptions.RemoveEmptyEntries).Length &gt;= 3, item("SUPPLIER_ITEM_CODE").ToString.Split({"("c, ")"c}, StringSplitOptions.RemoveEmptyEntries)(2).Split("/"c), New String() {})]</InArgument>
                                </Assign.Value>
                              </Assign>
                            </iad:CommentOut.Activities>
                          </iad:CommentOut>
                          <iad:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_7">
                            <iad:CommentOut.Activities>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_237">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[puno]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[parts(0)]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_268">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[puno]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[item("SUPPLIER_ITEM_CODE").ToString.Split("("c)(2).Split(")"c)(0)]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_238">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[pnli]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[puno.Split("/"c)(1)]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_4">
                                <TryCatch.Try>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_239">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[pnls]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">[puno.Split("/"c)(2)]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                </TryCatch.Try>
                                <TryCatch.Catches>
                                  <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_4">
                                    <ActivityAction x:TypeArguments="s:Exception">
                                      <ActivityAction.Argument>
                                        <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                                      </ActivityAction.Argument>
                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_240">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:String">[pnls]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:String">0</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                    </ActivityAction>
                                  </Catch>
                                </TryCatch.Catches>
                              </TryCatch>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_290">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[puno]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[puno.Split("/"c)(0)]</InArgument>
                                </Assign.Value>
                              </Assign>
                            </iad:CommentOut.Activities>
                          </iad:CommentOut>
                        </Sequence>
                      </If.Else>
                    </If>
                  </Sequence>
                </ActivityAction>
              </ForEach>
              <If Condition="[Cint(DictOcrValues(&quot;VAT_AMOUNT&quot;).Tostring)&lt;&gt;0]" sap2010:WorkflowViewState.IdRef="If_84">
                <If.Then>
                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_152">
                    <Sequence.Variables>
                      <Variable x:TypeArguments="x:String" Name="vatCode" />
                      <Variable x:TypeArguments="x:String" Name="vat" />
                      <Variable x:TypeArguments="iru:ResponseObject" Name="resp" />
                    </Sequence.Variables>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_296">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[vat]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">[DictOcrValues("VAT_AMOUNT").Tostring]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_297">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[vatCode]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">[miscValues("vatCodeConfig").ToString]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <If Condition="[miscValues(&quot;vatCodeConfig&quot;).ToString = &quot;NA&quot;]" sap2010:WorkflowViewState.IdRef="If_83">
                      <If.Then>
                        <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding VAT" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_26" Response="[resp]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                          <iai:IONAPIRequestWizard.Headers>
                            <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                <x:String>Accept</x:String>
                              </scg:List>
                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                <x:String>application/json</x:String>
                              </scg:List>
                            </scg:List>
                          </iai:IONAPIRequestWizard.Headers>
                          <iai:IONAPIRequestWizard.QueryParameters>
                            <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                <x:String>INBN</x:String>
                                <x:String>RDTP</x:String>
                                <x:String>DIVI</x:String>
                                <x:String>GLAM</x:String>
                              </scg:List>
                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                <x:String>inbnValue</x:String>
                                <x:String>3</x:String>
                                <x:String>division</x:String>
                                <x:String>vat</x:String>
                              </scg:List>
                            </scg:List>
                          </iai:IONAPIRequestWizard.QueryParameters>
                        </iai:IONAPIRequestWizard>
                      </If.Then>
                      <If.Else>
                        <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding VAT" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_27" Response="[resp]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                          <iai:IONAPIRequestWizard.Headers>
                            <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                <x:String>Accept</x:String>
                              </scg:List>
                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                <x:String>application/json</x:String>
                              </scg:List>
                            </scg:List>
                          </iai:IONAPIRequestWizard.Headers>
                          <iai:IONAPIRequestWizard.QueryParameters>
                            <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                              <scg:List x:TypeArguments="x:String" Capacity="8">
                                <x:String>INBN</x:String>
                                <x:String>RDTP</x:String>
                                <x:String>DIVI</x:String>
                                <x:String>VTA1</x:String>
                                <x:String>VTCD</x:String>
                              </scg:List>
                              <scg:List x:TypeArguments="x:String" Capacity="8">
                                <x:String>inbnValue</x:String>
                                <x:String>3</x:String>
                                <x:String>division</x:String>
                                <x:String>vat</x:String>
                                <x:String>vatCode</x:String>
                              </scg:List>
                            </scg:List>
                          </iai:IONAPIRequestWizard.QueryParameters>
                        </iai:IONAPIRequestWizard>
                      </If.Else>
                    </If>
                  </Sequence>
                </If.Then>
              </If>
              <Assign sap2010:WorkflowViewState.IdRef="Assign_298">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:String">SUCCESS</InArgument>
                </Assign.Value>
              </Assign>
              <iad:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_6">
                <iad:CommentOut.Activities>
                  <Sequence DisplayName="Sequence invoice from Excep UI" sap2010:WorkflowViewState.IdRef="Sequence_22">
                    <Sequence.Variables>
                      <Variable x:TypeArguments="scg:List(s:String[])" Name="M3TotalTableRows" />
                      <Variable x:TypeArguments="x:Decimal" Name="lineAmt" />
                      <Variable x:TypeArguments="x:String" Name="variable1" />
                      <Variable x:TypeArguments="x:Decimal" Name="qty" />
                    </Sequence.Variables>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_36">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[req2]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String" xml:space="preserve">["F2PNLI,F2REPN,F2SCOC,F2RPQA,F2TRDT,F2ITNO,F2CONO,F2CUCD,F2DIVI,F2SUNO,F2IVQA,F2PUNO,F2SUDO,F2RCAC,F2RPQT,F2SCOP,F2SERA,F2IVQT,F2ICAC  from FGRECL where F2DIVI = " + DIVISION+" AND F2PUNO = " + pono + " and F2IMST != 9 "]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;logfile&quot;,logfile},{&quot;miscValues&quot;,miscValues},{&quot;req2&quot;,req2}}]" ContinueOnError="True" DisplayName="Lines extraction Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_2" OutputArguments="[linesOcrWorkflowOutput]" ResponseCode="[linesOcrWorkflowStatus]" WorkflowFile="[projectPath+&quot;\LinesExtractionWithPO.xaml&quot;]" />
                    <If Condition="[linesOcrWorkflowStatus = 200]" sap2010:WorkflowViewState.IdRef="If_12">
                      <If.Then>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_37">
                          <Assign.To>
                            <OutArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="scg:List(s:String[])">[CType(linesOcrWorkflowOutput("M3TotalTableRows"), List(Of String()))]</InArgument>
                          </Assign.Value>
                        </Assign>
                      </If.Then>
                    </If>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_38">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:Decimal">[lineAmt]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:Decimal">[0]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_39">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:Decimal">[qty]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:Decimal">[0]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <ForEach x:TypeArguments="s:String[]" DisplayName="ForEach&lt;String[]&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_1" Values="[M3TotalTableRows]">
                      <ActivityAction x:TypeArguments="s:String[]">
                        <ActivityAction.Argument>
                          <DelegateInArgument x:TypeArguments="s:String[]" Name="rows" />
                        </ActivityAction.Argument>
                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_17">
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_40">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:Decimal">[lineAmt]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:Decimal">[lineAmt +convert.ToDecimal(rows(2))*convert.ToDecimal(rows(3))]</InArgument>
                            </Assign.Value>
                          </Assign>
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_41">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:Decimal">[qty]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:Decimal">[qty + convert.ToDecimal(rows(3))]</InArgument>
                            </Assign.Value>
                          </Assign>
                        </Sequence>
                      </ActivityAction>
                    </ForEach>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_42">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[diffamt]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">[((convert.ToDecimal(DictOcrValues("TOTAL").ToString) - lineAmt)/qty).ToString]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <If Condition="[diffamt &lt;&gt; &quot;0&quot;]" sap2010:WorkflowViewState.IdRef="If_13">
                      <If.Then>
                        <ForEach x:TypeArguments="x:Int32" DisplayName="ForEach&lt;Int32&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_2" Values="[Enumerable.Range(0,M3TotalTableRows.count)]">
                          <ActivityAction x:TypeArguments="x:Int32">
                            <ActivityAction.Argument>
                              <DelegateInArgument x:TypeArguments="x:Int32" Name="i" />
                            </ActivityAction.Argument>
                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_18">
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_43">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[M3TotalTableRows(i)(2)]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[(convert.ToDecimal(M3TotalTableRows(i)(2)) + convert.Todecimal(diffamt)).ToString]</InArgument>
                                </Assign.Value>
                              </Assign>
                            </Sequence>
                          </ActivityAction>
                        </ForEach>
                      </If.Then>
                    </If>
                    <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;logfile&quot;,logfile},{&quot;M3TotalTableRows&quot;,M3TotalTableRows},{&quot;vatCodeConfig&quot;,vatCodeConfig},{&quot;division&quot;,division},{&quot;inbnValue&quot;,inbnValue}}]" ContinueOnError="True" DisplayName="Add Lines Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_3" OutputArguments="[AddLineOcrWorkflowOutput]" ResponseCode="[AddLineOcrWorkflowStatus]" WorkflowFile="[projectPath+&quot;\AddLine.xaml&quot;]" />
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_44">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[chargeDiff]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">0</InArgument>
                      </Assign.Value>
                    </Assign>
                    <If Condition="[DictOcrValues(&quot;SHIPPING_AND_HANDLING&quot;).ToString &lt;&gt; &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_15">
                      <If.Then>
                        <If Condition="[CInt(DictOcrValues(&quot;SHIPPING_AND_HANDLING&quot;).ToString) &lt;&gt; 0]" sap2010:WorkflowViewState.IdRef="If_14">
                          <If.Then>
                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_19">
                              <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;logfile&quot;,logfile},{&quot;M3TotalTableRows&quot;,M3TotalTableRows},{&quot;division&quot;,division},{&quot;inbnValue&quot;,inbnValue},{&quot;charge&quot;,DictOcrValues(&quot;SHIPPING_AND_HANDLING&quot;).ToString},{&quot;chargeCode&quot;,chargeCode}}]" ContinueOnError="True" DisplayName="Add Charges Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_4" OutputArguments="[AddLineOcrWorkflowOutput]" ResponseCode="[AddLineOcrWorkflowStatus]" WorkflowFile="[projectPath+&quot;\AddCharge.xaml&quot;]" />
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_45">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[chargeDiff]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[Ctype(AddLineOcrWorkflowOutput("chargeDiff"),String)]</InArgument>
                                </Assign.Value>
                              </Assign>
                            </Sequence>
                          </If.Then>
                        </If>
                      </If.Then>
                    </If>
                    <If Condition="[((DictOcrValues(&quot;VAT_AMOUNT&quot;).Tostring.contains(&quot;,&quot;) AND DictOcrValues(&quot;VAT_PERCENTAGE&quot;).Tostring.contains(&quot;%&quot;)) OR (Cint(DictOcrValues(&quot;VAT_AMOUNT&quot;).Tostring)&lt;&gt;0) )]" sap2010:WorkflowViewState.IdRef="If_17">
                      <If.Then>
                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_20">
                          <Sequence.Variables>
                            <Variable x:TypeArguments="x:String" Name="vat" />
                            <Variable x:TypeArguments="iru:ResponseObject" Name="respout1" />
                            <Variable x:TypeArguments="x:String" Name="vatCode" />
                          </Sequence.Variables>
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_46">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:String">[vat]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String">[DictOcrValues("VAT_AMOUNT").Tostring]</InArgument>
                            </Assign.Value>
                          </Assign>
                          <iad:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_1">
                            <iad:CommentOut.Activities>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_47">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[vatCode]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[miscValues("vatCodeConfig").ToString]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <If Condition="[miscValues(&quot;vatCodeConfig&quot;).ToString = &quot;NA&quot;]" sap2010:WorkflowViewState.IdRef="If_16">
                                <If.Then>
                                  <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding VAT" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_5" Response="[respout1]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                                    <iai:IONAPIRequestWizard.Headers>
                                      <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                        <scg:List x:TypeArguments="x:String" Capacity="4">
                                          <x:String>Accept</x:String>
                                        </scg:List>
                                        <scg:List x:TypeArguments="x:String" Capacity="4">
                                          <x:String>application/json</x:String>
                                        </scg:List>
                                      </scg:List>
                                    </iai:IONAPIRequestWizard.Headers>
                                    <iai:IONAPIRequestWizard.QueryParameters>
                                      <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                        <scg:List x:TypeArguments="x:String" Capacity="4">
                                          <x:String>INBN</x:String>
                                          <x:String>RDTP</x:String>
                                          <x:String>DIVI</x:String>
                                          <x:String>GLAM</x:String>
                                        </scg:List>
                                        <scg:List x:TypeArguments="x:String" Capacity="4">
                                          <x:String>inbnValue</x:String>
                                          <x:String>3</x:String>
                                          <x:String>division</x:String>
                                          <x:String>vat</x:String>
                                        </scg:List>
                                      </scg:List>
                                    </iai:IONAPIRequestWizard.QueryParameters>
                                  </iai:IONAPIRequestWizard>
                                </If.Then>
                                <If.Else>
                                  <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding VAT" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_6" Response="[respout1]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                                    <iai:IONAPIRequestWizard.Headers>
                                      <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                        <scg:List x:TypeArguments="x:String" Capacity="4">
                                          <x:String>Accept</x:String>
                                        </scg:List>
                                        <scg:List x:TypeArguments="x:String" Capacity="4">
                                          <x:String>application/json</x:String>
                                        </scg:List>
                                      </scg:List>
                                    </iai:IONAPIRequestWizard.Headers>
                                    <iai:IONAPIRequestWizard.QueryParameters>
                                      <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                        <scg:List x:TypeArguments="x:String" Capacity="8">
                                          <x:String>INBN</x:String>
                                          <x:String>RDTP</x:String>
                                          <x:String>DIVI</x:String>
                                          <x:String>VTA1</x:String>
                                          <x:String>VTCD</x:String>
                                        </scg:List>
                                        <scg:List x:TypeArguments="x:String" Capacity="8">
                                          <x:String>inbnValue</x:String>
                                          <x:String>3</x:String>
                                          <x:String>division</x:String>
                                          <x:String>vat</x:String>
                                          <x:String>vatCode</x:String>
                                        </scg:List>
                                      </scg:List>
                                    </iai:IONAPIRequestWizard.QueryParameters>
                                  </iai:IONAPIRequestWizard>
                                </If.Else>
                              </If>
                            </iad:CommentOut.Activities>
                          </iad:CommentOut>
                          <iaw:InvokeWorkflow OutputArguments="{x:Null}" ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;vat&quot;,DictOcrValues(&quot;VAT_AMOUNT&quot;).Tostring},{&quot;VatPercentage&quot;,DictOcrValues(&quot;VAT_PERCENTAGE&quot;).Tostring},{&quot;division&quot;,division},{&quot;tenantID&quot;,tenantID},{&quot;inbnValue&quot;,inbnValue},{&quot;CountryCode&quot;,countryCode},{&quot;AccountStartsWith&quot;,&quot;&quot;},{&quot;logfile&quot;,logfile},{&quot;invoiceType&quot;,&quot;po&quot;}}]" ContinueOnError="True" DisplayName="Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_5" WorkflowFile="[projectPath+&quot;\vatConfiguration.xaml&quot;]" />
                        </Sequence>
                      </If.Then>
                    </If>
                    <If Condition="[Math.abs(Convert.ToDecimal(diffamt)*Convert.ToDecimal(qty) + Convert.ToDecimal(chargeDiff)) &gt; 0]" sap2010:WorkflowViewState.IdRef="If_18">
                      <If.Then>
                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_21">
                          <Sequence.Variables>
                            <Variable x:TypeArguments="x:String" Name="diffAmt1" />
                            <Variable x:TypeArguments="iru:ResponseObject" Name="resp1" />
                          </Sequence.Variables>
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_48">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:String">[diffAmt1]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String">[Math.abs(Convert.ToDecimal(diffamt)*Convert.ToDecimal(qty) + Convert.ToDecimal(chargeDiff)).ToString]</InArgument>
                            </Assign.Value>
                          </Assign>
                          <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for adding AddInfo" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_7" Response="[resp1]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddAddInfo&quot;]">
                            <iai:IONAPIRequestWizard.Headers>
                              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                <scg:List x:TypeArguments="x:String" Capacity="4">
                                  <x:String>Accept</x:String>
                                </scg:List>
                                <scg:List x:TypeArguments="x:String" Capacity="4">
                                  <x:String>application/json</x:String>
                                </scg:List>
                              </scg:List>
                            </iai:IONAPIRequestWizard.Headers>
                            <iai:IONAPIRequestWizard.QueryParameters>
                              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                <scg:List x:TypeArguments="x:String" Capacity="4">
                                  <x:String>INBN</x:String>
                                  <x:String>PEXN</x:String>
                                  <x:String>PEXI</x:String>
                                  <x:String>DIVI</x:String>
                                </scg:List>
                                <scg:List x:TypeArguments="x:String" Capacity="4">
                                  <x:String>inbnValue</x:String>
                                  <x:String>414</x:String>
                                  <x:String>diffAmt1</x:String>
                                  <x:String>division</x:String>
                                </scg:List>
                              </scg:List>
                            </iai:IONAPIRequestWizard.QueryParameters>
                          </iai:IONAPIRequestWizard>
                        </Sequence>
                      </If.Then>
                    </If>
                  </Sequence>
                </iad:CommentOut.Activities>
              </iad:CommentOut>
            </Sequence>
          </If.Then>
          <If.Else>
            <Sequence DisplayName="Sequence invoice from mail" sap2010:WorkflowViewState.IdRef="Sequence_23">
              <Assign sap2010:WorkflowViewState.IdRef="Assign_49">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                </Assign.Value>
              </Assign>
              <Assign sap2010:WorkflowViewState.IdRef="Assign_50">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:String">["Invoice Number " + DictOcrValues("INVOICE_RECEIPT_ID").Tostring + " already exists."]</InArgument>
                </Assign.Value>
              </Assign>
              <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_4" Line="[commentStatus]" Source="[logfile]" />
            </Sequence>
          </If.Else>
        </If>
      </If.Then>
      <If.Else>
        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_71">
          <iad:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_3">
            <iad:CommentOut.Activities>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_70">
                <Sequence.Variables>
                  <Variable x:TypeArguments="scg:List(x:String)" Name="deliveryNumbers" />
                  <Variable x:TypeArguments="x:Boolean" Name="dnExists" />
                  <Variable x:TypeArguments="x:String" Name="withPO" />
                  <Variable x:TypeArguments="x:String" Name="dn" />
                </Sequence.Variables>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_142">
                  <Assign.To>
                    <OutArgument x:TypeArguments="scg:List(x:String)">[deliveryNumbers]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="scg:List(x:String)">[New List(Of String)]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_143">
                  <Assign.To>
                    <OutArgument x:TypeArguments="scg:List(x:String)">[deliveryNumbers]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="scg:List(x:String)">[DictOcrValues("DELIVERY_NOTE_DATA").ToString.split(","c).ToList]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_144">
                  <Assign.To>
                    <OutArgument x:TypeArguments="scg:List(x:String)">[deliveryNumbers]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="scg:List(x:String)">[deliveryNumbers.Distinct().ToList()]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_145">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:Boolean">[dnExists]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_146">
                  <Assign.To>
                    <OutArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="scg:List(s:String[])">[new list(OF string())]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_147">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[withPO]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">False</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_148">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[dn]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">
                      <Literal x:TypeArguments="x:String" Value="" />
                    </InArgument>
                  </Assign.Value>
                </Assign>
                <ForEach x:TypeArguments="x:String" DisplayName="ForEach&lt;String&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_21" Values="[deliveryNumbers]">
                  <ActivityAction x:TypeArguments="x:String">
                    <ActivityAction.Argument>
                      <DelegateInArgument x:TypeArguments="x:String" Name="item0" />
                    </ActivityAction.Argument>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_102">
                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_101">
                        <Sequence.Variables>
                          <Variable x:TypeArguments="x:String" Name="tolamount" />
                          <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="linesOcrWorkflowOutput" />
                          <Variable x:TypeArguments="x:Int32" Name="linesOcrWorkflowStatus" />
                        </Sequence.Variables>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_149">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[req2]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String" xml:space="preserve">["F2PNLI,F2REPN,F2SCOC,F2RPQA,F2TRDT,F2ITNO,F2CONO,F2CUCD,F2DIVI,F2SUNO,F2IVQA,F2PUNO,F2SUDO,F2RCAC,F2RPQT,F2SCOP,F2SERA,F2IVQT,F2ICAC  from FGRECL where F2SUDO = " + item0.trim() + " and F2SUNO = " + vendorId + " and F2DIVI = "+division]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;logfile&quot;,logfile},{&quot;miscValues&quot;,miscValues},{&quot;req2&quot;,req2}}]" ContinueOnError="True" DisplayName="Lines extraction Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_13" OutputArguments="[linesOcrWorkflowOutput]" ResponseCode="[linesOcrWorkflowStatus]" WorkflowFile="[projectPath+&quot;\LinesExtractionWithPO.xaml&quot;]" />
                        <If Condition="[linesOcrWorkflowStatus = 200]" sap2010:WorkflowViewState.IdRef="If_51">
                          <If.Then>
                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_99">
                              <Sequence.Variables>
                                <Variable x:TypeArguments="scg:List(s:String[])" Name="M3TotalTableRows1" />
                                <Variable x:TypeArguments="x:Boolean" Name="DNinM3exists" />
                              </Sequence.Variables>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_150">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows1]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="scg:List(s:String[])">[CType(linesOcrWorkflowOutput("M3TotalTableRows"), List(Of String()))]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_151">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:Boolean">[DNinM3exists]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:Boolean">[CType(linesOcrWorkflowOutput("DNinM3exists"), Boolean)]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <If Condition="[M3TotalTableRows1.Count &gt; 0]" sap2010:WorkflowViewState.IdRef="If_50">
                                <If.Then>
                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_87">
                                    <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_15" Line="[&quot;Receipt lines for the delivery note number &quot; + item0.trim + &quot; is extracted.&quot;]" Source="[logfile]" />
                                    <If Condition="[dnExists]" sap2010:WorkflowViewState.IdRef="If_43">
                                      <If.Then>
                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_86">
                                          <ForEach x:TypeArguments="s:String[]" DisplayName="ForEach&lt;String[]&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_20" Values="[M3TotalTableRows1]">
                                            <ActivityAction x:TypeArguments="s:String[]">
                                              <ActivityAction.Argument>
                                                <DelegateInArgument x:TypeArguments="s:String[]" Name="item" />
                                              </ActivityAction.Argument>
                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_85">
                                                <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_4" MethodName="Add">
                                                  <InvokeMethod.TargetObject>
                                                    <InArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows]</InArgument>
                                                  </InvokeMethod.TargetObject>
                                                  <InArgument x:TypeArguments="s:String[]">[item]</InArgument>
                                                </InvokeMethod>
                                              </Sequence>
                                            </ActivityAction>
                                          </ForEach>
                                        </Sequence>
                                      </If.Then>
                                    </If>
                                  </Sequence>
                                </If.Then>
                                <If.Else>
                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_98">
                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_152">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="x:String">[dn]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="x:String">[dn + item0.trim + ", "]</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                    <If Condition="[DNinM3exists]" sap2010:WorkflowViewState.IdRef="If_49">
                                      <If.Then>
                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_88">
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_153">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String" xml:space="preserve">["Lines already invoiced for delivery notes " +  dn.Substring(0,dn.Length-2)]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_154">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_16" Line="[&quot;Lines already invoiced for delivery note: &quot; + item0.trim]" Source="[logfile]" />
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_155">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:Boolean">[dnExists]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                        </Sequence>
                                      </If.Then>
                                      <If.Else>
                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_97">
                                          <If Condition="[deliveryNumbers.Count = 1]" sap2010:WorkflowViewState.IdRef="If_48">
                                            <If.Then>
                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_91">
                                                <Sequence.Variables>
                                                  <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="vendorResponseDictionary" />
                                                  <Variable x:TypeArguments="x:Int32" Name="vendorResponseCode" />
                                                  <Variable x:TypeArguments="x:String" Name="withVendor" />
                                                </Sequence.Variables>
                                                <If Condition="[DictOcrValues(&quot;PO_NUMBER&quot;).ToString &lt;&gt; &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_44">
                                                  <If.Then>
                                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_89">
                                                      <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;vendorId&quot;,vendorId},{&quot;projectPath&quot;,projectPath},{&quot;DictOcrValues&quot;,DictOcrValues},{&quot;logfile&quot;,logfile},{&quot;miscValues&quot;,miscValues},{&quot;division&quot;,division}}]" ContinueOnError="True" DisplayName="OneDelivery Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_14" OutputArguments="[vendorResponseDictionary]" ResponseCode="[vendorResponseCode]" WorkflowFile="[projectPath+&quot;\OneDeliveryNotFound.xaml&quot;]" />
                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_156">
                                                        <Assign.To>
                                                          <OutArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows]</OutArgument>
                                                        </Assign.To>
                                                        <Assign.Value>
                                                          <InArgument x:TypeArguments="scg:List(s:String[])">[CType(vendorResponseDictionary("M3TotalTableRows"), List(Of String()))]</InArgument>
                                                        </Assign.Value>
                                                      </Assign>
                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_157">
                                                        <Assign.To>
                                                          <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                        </Assign.To>
                                                        <Assign.Value>
                                                          <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("commentStatus"), String)]</InArgument>
                                                        </Assign.Value>
                                                      </Assign>
                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_158">
                                                        <Assign.To>
                                                          <OutArgument x:TypeArguments="x:String">[withPO]</OutArgument>
                                                        </Assign.To>
                                                        <Assign.Value>
                                                          <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("withPO"), String)]</InArgument>
                                                        </Assign.Value>
                                                      </Assign>
                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_159">
                                                        <Assign.To>
                                                          <OutArgument x:TypeArguments="x:String">[withVendor]</OutArgument>
                                                        </Assign.To>
                                                        <Assign.Value>
                                                          <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("withVendor"), String)]</InArgument>
                                                        </Assign.Value>
                                                      </Assign>
                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_160">
                                                        <Assign.To>
                                                          <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                        </Assign.To>
                                                        <Assign.Value>
                                                          <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("Status"), String)]</InArgument>
                                                        </Assign.Value>
                                                      </Assign>
                                                    </Sequence>
                                                  </If.Then>
                                                  <If.Else>
                                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_90">
                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_161">
                                                        <Assign.To>
                                                          <OutArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows]</OutArgument>
                                                        </Assign.To>
                                                        <Assign.Value>
                                                          <InArgument x:TypeArguments="scg:List(s:String[])">[new list(OF string())]</InArgument>
                                                        </Assign.Value>
                                                      </Assign>
                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_162">
                                                        <Assign.To>
                                                          <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                        </Assign.To>
                                                        <Assign.Value>
                                                          <InArgument x:TypeArguments="x:String">PO number is empty</InArgument>
                                                        </Assign.Value>
                                                      </Assign>
                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_163">
                                                        <Assign.To>
                                                          <OutArgument x:TypeArguments="x:String">[withPO]</OutArgument>
                                                        </Assign.To>
                                                        <Assign.Value>
                                                          <InArgument x:TypeArguments="x:String">False</InArgument>
                                                        </Assign.Value>
                                                      </Assign>
                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_164">
                                                        <Assign.To>
                                                          <OutArgument x:TypeArguments="x:String">[withVendor]</OutArgument>
                                                        </Assign.To>
                                                        <Assign.Value>
                                                          <InArgument x:TypeArguments="x:String">False</InArgument>
                                                        </Assign.Value>
                                                      </Assign>
                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_165">
                                                        <Assign.To>
                                                          <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                        </Assign.To>
                                                        <Assign.Value>
                                                          <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                                                        </Assign.Value>
                                                      </Assign>
                                                    </Sequence>
                                                  </If.Else>
                                                </If>
                                              </Sequence>
                                            </If.Then>
                                            <If.Else>
                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_96">
                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_95">
                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_166">
                                                    <Assign.To>
                                                      <OutArgument x:TypeArguments="x:String">[req2]</OutArgument>
                                                    </Assign.To>
                                                    <Assign.Value>
                                                      <InArgument x:TypeArguments="x:String" xml:space="preserve">["F2PNLI,F2REPN,F2SCOC,F2RPQA,F2TRDT,F2ITNO,F2CONO,F2CUCD,F2DIVI,F2SUNO,F2IVQA,F2PUNO,F2SUDO,F2RCAC,F2RPQT,F2SCOP,F2SERA,F2IVQT,F2ICAC  from FGRECL where F2SUDO = " + item0.trim() + " and F2SUNO = " + vendorId + " and F2DIVI = "+division]</InArgument>
                                                    </Assign.Value>
                                                  </Assign>
                                                  <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;logfile&quot;,logfile},{&quot;miscValues&quot;,miscValues},{&quot;req2&quot;,req2}}]" ContinueOnError="True" DisplayName="Lines extraction Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_15" OutputArguments="[linesOcrWorkflowOutput]" ResponseCode="[linesOcrWorkflowStatus]" WorkflowFile="[projectPath+&quot;\LinesExtractionWithPO.xaml&quot;]" />
                                                  <If Condition="[linesOcrWorkflowStatus = 200]" sap2010:WorkflowViewState.IdRef="If_47">
                                                    <If.Then>
                                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_94">
                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_167">
                                                          <Assign.To>
                                                            <OutArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows1]</OutArgument>
                                                          </Assign.To>
                                                          <Assign.Value>
                                                            <InArgument x:TypeArguments="scg:List(s:String[])">[CType(linesOcrWorkflowOutput("M3TotalTableRows"), List(Of String()))]</InArgument>
                                                          </Assign.Value>
                                                        </Assign>
                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_168">
                                                          <Assign.To>
                                                            <OutArgument x:TypeArguments="x:Boolean">[DNinM3exists]</OutArgument>
                                                          </Assign.To>
                                                          <Assign.Value>
                                                            <InArgument x:TypeArguments="x:Boolean">[CType(linesOcrWorkflowOutput("DNinM3exists"), Boolean)]</InArgument>
                                                          </Assign.Value>
                                                        </Assign>
                                                        <If Condition="[M3TotalTableRows1.Count &gt; 0]" sap2010:WorkflowViewState.IdRef="If_46">
                                                          <If.Else>
                                                            <If Condition="[DNinM3exists]" sap2010:WorkflowViewState.IdRef="If_45">
                                                              <If.Then>
                                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_92">
                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_169">
                                                                    <Assign.To>
                                                                      <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                    </Assign.To>
                                                                    <Assign.Value>
                                                                      <InArgument x:TypeArguments="x:String">["Lines already invoiced for delivery note numbers " + dn.Substring(0,dn.Length-2)]</InArgument>
                                                                    </Assign.Value>
                                                                  </Assign>
                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_170">
                                                                    <Assign.To>
                                                                      <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                    </Assign.To>
                                                                    <Assign.Value>
                                                                      <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                                                                    </Assign.Value>
                                                                  </Assign>
                                                                  <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_17" Line="[&quot;Lines already invoiced for delivery note number &quot; + item0.trim]" Source="[logfile]" />
                                                                </Sequence>
                                                              </If.Then>
                                                              <If.Else>
                                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_93">
                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_171">
                                                                    <Assign.To>
                                                                      <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                    </Assign.To>
                                                                    <Assign.Value>
                                                                      <InArgument x:TypeArguments="x:String">["No receipts available for the given Delivery Note numbers " + dn.Substring(0,dn.Length-2)]</InArgument>
                                                                    </Assign.Value>
                                                                  </Assign>
                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_172">
                                                                    <Assign.To>
                                                                      <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                    </Assign.To>
                                                                    <Assign.Value>
                                                                      <InArgument x:TypeArguments="x:String">PONOTRECEIVED</InArgument>
                                                                    </Assign.Value>
                                                                  </Assign>
                                                                  <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_18" Line="[&quot;No receipts available for the given Delivery Note number: &quot; + item0.trim]" Source="[logfile]" />
                                                                </Sequence>
                                                              </If.Else>
                                                            </If>
                                                          </If.Else>
                                                        </If>
                                                      </Sequence>
                                                    </If.Then>
                                                  </If>
                                                </Sequence>
                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_173">
                                                  <Assign.To>
                                                    <OutArgument x:TypeArguments="x:Boolean">[dnExists]</OutArgument>
                                                  </Assign.To>
                                                  <Assign.Value>
                                                    <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                                                  </Assign.Value>
                                                </Assign>
                                              </Sequence>
                                            </If.Else>
                                          </If>
                                        </Sequence>
                                      </If.Else>
                                    </If>
                                  </Sequence>
                                </If.Else>
                              </If>
                            </Sequence>
                          </If.Then>
                          <If.Else>
                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_100">
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_174">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[CType(linesOcrWorkflowOutput("commentStatus"), String)]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_175">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[CType(linesOcrWorkflowOutput("Status"), String)]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_176">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:Boolean">[dnExists]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                                </Assign.Value>
                              </Assign>
                            </Sequence>
                          </If.Else>
                        </If>
                      </Sequence>
                    </Sequence>
                  </ActivityAction>
                </ForEach>
              </Sequence>
            </iad:CommentOut.Activities>
          </iad:CommentOut>
          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_45">
            <Sequence.Variables>
              <Variable x:TypeArguments="iru:ResponseObject" Name="poLinesResponseObject" />
              <Variable x:TypeArguments="x:Int32" Name="poLinesResponseCode" />
              <Variable x:TypeArguments="scg:Dictionary(x:String, x:Object)" Name="LinesDict" />
              <Variable x:TypeArguments="x:Boolean" Name="itemCodeMatch" />
              <Variable x:TypeArguments="x:Boolean" Name="qtyMatch" />
              <Variable x:TypeArguments="x:Boolean" Name="PriceMatch" />
              <Variable x:TypeArguments="x:Boolean" Name="poExists" />
              <Variable x:TypeArguments="scg:List(x:String)" Name="poList" />
              <Variable x:TypeArguments="scg:Dictionary(x:String, x:String)" Name="itemDict" />
            </Sequence.Variables>
            <iad:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_4">
              <iad:CommentOut.Activities>
                <If Condition="[M3TotalTableRows.Count &gt; 0]" sap2010:WorkflowViewState.IdRef="If_52" />
              </iad:CommentOut.Activities>
            </iad:CommentOut>
            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_72">
              <If Condition="[DictOcrValues(&quot;PO_NUMBER&quot;).ToString &lt;&gt; &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_20">
                <If.Then>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_51">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[pono]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[DictOcrValues("PO_NUMBER").ToString]</InArgument>
                    </Assign.Value>
                  </Assign>
                </If.Then>
              </If>
              <Assign sap2010:WorkflowViewState.IdRef="Assign_122">
                <Assign.To>
                  <OutArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows1]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="scg:List(s:String[])">[new list(Of String())]</InArgument>
                </Assign.Value>
              </Assign>
              <Assign sap2010:WorkflowViewState.IdRef="Assign_123">
                <Assign.To>
                  <OutArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="scg:List(s:String[])">[new list(Of String())]</InArgument>
                </Assign.Value>
              </Assign>
              <Assign sap2010:WorkflowViewState.IdRef="Assign_124">
                <Assign.To>
                  <OutArgument x:TypeArguments="scg:List(x:String)">[PONumbers]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="scg:List(x:String)">[New List(Of String)]</InArgument>
                </Assign.Value>
              </Assign>
              <Assign sap2010:WorkflowViewState.IdRef="Assign_125">
                <Assign.To>
                  <OutArgument x:TypeArguments="scg:List(x:String)">[PONumbers]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="scg:List(x:String)">[DictOcrValues("PO_NUMBER").ToString.split(","c).ToList]</InArgument>
                </Assign.Value>
              </Assign>
              <ForEach x:TypeArguments="x:String" DisplayName="ForEach&lt;String&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_19" Values="[PONumbers]">
                <ActivityAction x:TypeArguments="x:String">
                  <ActivityAction.Argument>
                    <DelegateInArgument x:TypeArguments="x:String" Name="pono" />
                  </ActivityAction.Argument>
                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_84">
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_126">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[req2]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String" xml:space="preserve">["F2PNLI,F2REPN,F2SCOC,F2RPQA,F2TRDT,F2ITNO,F2CONO,F2CUCD,F2DIVI,F2SUNO,F2IVQA,F2PUNO,F2SUDO,F2RCAC,F2RPQT,F2SCOP,F2SERA,F2IVQT,F2ICAC,F2PNLS  from FGRECL where F2DIVI = " + DIVISION+" AND F2PUNO = " + pono + " and F2IMST != 9 "]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;logfile&quot;,logfile},{&quot;miscValues&quot;,miscValues},{&quot;req2&quot;,req2}}]" ContinueOnError="True" DisplayName="Lines extraction Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_11" OutputArguments="[linesOcrWorkflowOutput]" ResponseCode="[linesOcrWorkflowStatus]" WorkflowFile="[projectPath+&quot;\LinesExtractionWithPO.xaml&quot;]" />
                    <If Condition="[linesOcrWorkflowStatus = 200]" sap2010:WorkflowViewState.IdRef="If_42">
                      <If.Then>
                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_83">
                          <Sequence.Variables>
                            <Variable x:TypeArguments="x:Boolean" Name="DNinM3exists" />
                          </Sequence.Variables>
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_127">
                            <Assign.To>
                              <OutArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows1]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="scg:List(s:String[])">[CType(linesOcrWorkflowOutput("M3TotalTableRows"), List(Of String()))]</InArgument>
                            </Assign.Value>
                          </Assign>
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_128">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:Boolean">[DNinM3exists]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:Boolean">[CType(linesOcrWorkflowOutput("DNinM3exists"), Boolean)]</InArgument>
                            </Assign.Value>
                          </Assign>
                          <If Condition="[M3TotalTableRows1.Count &gt; 0]" sap2010:WorkflowViewState.IdRef="If_41">
                            <If.Then>
                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_75">
                                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_10" Line="[&quot;Receipt lines for the purchase order &quot; +pono.trim + &quot; is extracted.&quot;]" Source="[logfile]" />
                                <ForEach x:TypeArguments="s:String[]" DisplayName="ForEach&lt;String[]&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_18" Values="[M3TotalTableRows1]">
                                  <ActivityAction x:TypeArguments="s:String[]">
                                    <ActivityAction.Argument>
                                      <DelegateInArgument x:TypeArguments="s:String[]" Name="item" />
                                    </ActivityAction.Argument>
                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_74">
                                      <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_3" MethodName="Add">
                                        <InvokeMethod.TargetObject>
                                          <InArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows]</InArgument>
                                        </InvokeMethod.TargetObject>
                                        <InArgument x:TypeArguments="s:String[]">[item]</InArgument>
                                      </InvokeMethod>
                                    </Sequence>
                                  </ActivityAction>
                                </ForEach>
                              </Sequence>
                            </If.Then>
                            <If.Else>
                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_82">
                                <Sequence.Variables>
                                  <Variable x:TypeArguments="x:String" Name="po" />
                                </Sequence.Variables>
                                <Assign sap2010:WorkflowViewState.IdRef="Assign_129">
                                  <Assign.To>
                                    <OutArgument x:TypeArguments="x:String">[po]</OutArgument>
                                  </Assign.To>
                                  <Assign.Value>
                                    <InArgument x:TypeArguments="x:String">[po + pono.trim + ", "]</InArgument>
                                  </Assign.Value>
                                </Assign>
                                <If Condition="[DNinM3exists]" sap2010:WorkflowViewState.IdRef="If_40">
                                  <If.Then>
                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_76">
                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_130">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:String" xml:space="preserve">["Lines already invoiced for po " +  po.Substring(0,po.Length-2)]</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_131">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                      <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_11" Line="[&quot;Lines already invoiced for po: &quot; + pono.trim]" Source="[logfile]" />
                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_132">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:Boolean">[poExists]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                    </Sequence>
                                  </If.Then>
                                  <If.Else>
                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_81">
                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_80">
                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_133">
                                          <Assign.To>
                                            <OutArgument x:TypeArguments="x:String">[req2]</OutArgument>
                                          </Assign.To>
                                          <Assign.Value>
                                            <InArgument x:TypeArguments="x:String" xml:space="preserve">["F2PNLI,F2REPN,F2SCOC,F2RPQA,F2TRDT,F2ITNO,F2CONO,F2CUCD,F2DIVI,F2SUNO,F2IVQA,F2PUNO,F2SUDO,F2RCAC,F2RPQT,F2SCOP,F2SERA,F2IVQT,F2ICAC,F2PNLS  from FGRECL where F2DIVI = " + DIVISION+" AND F2PUNO = " + pono]</InArgument>
                                          </Assign.Value>
                                        </Assign>
                                        <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;logfile&quot;,logfile},{&quot;miscValues&quot;,miscValues},{&quot;req2&quot;,req2}}]" ContinueOnError="True" DisplayName="Lines extraction Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_12" OutputArguments="[linesOcrWorkflowOutput]" ResponseCode="[linesOcrWorkflowStatus]" WorkflowFile="[projectPath+&quot;\LinesExtractionWithPO.xaml&quot;]" />
                                        <If Condition="[linesOcrWorkflowStatus = 200]" sap2010:WorkflowViewState.IdRef="If_39">
                                          <If.Then>
                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_79">
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_134">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows1]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="scg:List(s:String[])">[CType(linesOcrWorkflowOutput("M3TotalTableRows"), List(Of String()))]</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_135">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="x:Boolean">[DNinM3exists]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="x:Boolean">[CType(linesOcrWorkflowOutput("DNinM3exists"), Boolean)]</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                              <If Condition="[M3TotalTableRows1.Count &gt; 0]" sap2010:WorkflowViewState.IdRef="If_38">
                                                <If.Else>
                                                  <If Condition="[DNinM3exists]" sap2010:WorkflowViewState.IdRef="If_37">
                                                    <If.Then>
                                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_77">
                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_136">
                                                          <Assign.To>
                                                            <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                          </Assign.To>
                                                          <Assign.Value>
                                                            <InArgument x:TypeArguments="x:String" xml:space="preserve">["Lines already invoiced for po " +  po.Substring(0,po.Length-2)]</InArgument>
                                                          </Assign.Value>
                                                        </Assign>
                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_137">
                                                          <Assign.To>
                                                            <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                          </Assign.To>
                                                          <Assign.Value>
                                                            <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                                                          </Assign.Value>
                                                        </Assign>
                                                        <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_12" Line="[&quot;Lines already invoiced for po: &quot; + pono.trim]" Source="[logfile]" />
                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_138">
                                                          <Assign.To>
                                                            <OutArgument x:TypeArguments="x:Boolean">[poExists]</OutArgument>
                                                          </Assign.To>
                                                          <Assign.Value>
                                                            <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                                                          </Assign.Value>
                                                        </Assign>
                                                      </Sequence>
                                                    </If.Then>
                                                    <If.Else>
                                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_78">
                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_139">
                                                          <Assign.To>
                                                            <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                          </Assign.To>
                                                          <Assign.Value>
                                                            <InArgument x:TypeArguments="x:String">["No receipts available for the given po: " + po.Substring(0,po.Length-2)]</InArgument>
                                                          </Assign.Value>
                                                        </Assign>
                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_140">
                                                          <Assign.To>
                                                            <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                          </Assign.To>
                                                          <Assign.Value>
                                                            <InArgument x:TypeArguments="x:String">PONOTRECEIVED</InArgument>
                                                          </Assign.Value>
                                                        </Assign>
                                                        <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_13" Line="[&quot;No receipts available for the given po: &quot; + pono.trim]" Source="[logfile]" />
                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_141">
                                                          <Assign.To>
                                                            <OutArgument x:TypeArguments="x:Boolean">[poExists]</OutArgument>
                                                          </Assign.To>
                                                          <Assign.Value>
                                                            <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                                                          </Assign.Value>
                                                        </Assign>
                                                      </Sequence>
                                                    </If.Else>
                                                  </If>
                                                </If.Else>
                                              </If>
                                            </Sequence>
                                          </If.Then>
                                        </If>
                                      </Sequence>
                                    </Sequence>
                                  </If.Else>
                                </If>
                              </Sequence>
                            </If.Else>
                          </If>
                        </Sequence>
                      </If.Then>
                      <If.Else>
                        <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_14" Line="Lines of the PO not extracted." Source="[logfile]" />
                      </If.Else>
                    </If>
                  </Sequence>
                </ActivityAction>
              </ForEach>
            </Sequence>
            <If Condition="[M3TotalTableRows.Count &gt; 0]" sap2010:WorkflowViewState.IdRef="If_53">
              <If.Then>
                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_103">
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_177">
                    <Assign.To>
                      <OutArgument x:TypeArguments="scg:List(x:String)">[poList]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="scg:List(x:String)">[M3TotalTableRows.Where(Function(arr) arr.Length &gt; 1). Select(Function(arr) arr(6)).Distinct().ToList()]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_178">
                    <Assign.To>
                      <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:String)">[itemDict]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="scg:Dictionary(x:String, x:String)">[New Dictionary(Of string, string)]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <ForEach x:TypeArguments="x:String" DisplayName="ForEach&lt;String&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_23" Values="[polist]">
                    <ActivityAction x:TypeArguments="x:String">
                      <ActivityAction.Argument>
                        <DelegateInArgument x:TypeArguments="x:String" Name="po" />
                      </ActivityAction.Argument>
                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_106">
                        <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="pono IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_10" Response="[poLinesResponseObject]" StatusCode="[poLinesResponseCode]" Url="[TenantID +&quot;M3/m3api-rest/v2/execute/PPS200MI/LstLine?maxrecs=0&amp;dateformat=YMD8&amp;excludeempty=false&amp;righttrim=true&amp;returncols=PUNO%2CITNO%2CSITE%2CORQA%2CRVQA&amp;format=PRETTY&amp;extendedresult=false&quot;]">
                          <iai:IONAPIRequestWizard.Headers>
                            <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                <x:String>Accept</x:String>
                              </scg:List>
                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                <x:String>application/json</x:String>
                              </scg:List>
                            </scg:List>
                          </iai:IONAPIRequestWizard.Headers>
                          <iai:IONAPIRequestWizard.QueryParameters>
                            <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                <x:String>PUNO</x:String>
                              </scg:List>
                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                <x:String>po</x:String>
                              </scg:List>
                            </scg:List>
                          </iai:IONAPIRequestWizard.QueryParameters>
                        </iai:IONAPIRequestWizard>
                        <If Condition="[poLinesResponseCode = 200]" sap2010:WorkflowViewState.IdRef="If_55">
                          <If.Then>
                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_105">
                              <Sequence.Variables>
                                <Variable x:TypeArguments="njl:JToken" Name="out9" />
                              </Sequence.Variables>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_179">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="njl:JToken">[out9]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="njl:JToken">[poLinesResponseObject.readasjson("results")(0)("records")]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <If Condition="[out9.ToString &lt;&gt; &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_54">
                                <If.Then>
                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_104">
                                    <ForEach x:TypeArguments="njl:JToken" DisplayName="ForEach&lt;JToken&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_22" Values="[poLinesResponseObject.readasjson(&quot;results&quot;)(0)(&quot;records&quot;)]">
                                      <ActivityAction x:TypeArguments="njl:JToken">
                                        <ActivityAction.Argument>
                                          <DelegateInArgument x:TypeArguments="njl:JToken" Name="item" />
                                        </ActivityAction.Argument>
                                        <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_5" MethodName="Add">
                                          <InvokeMethod.TargetObject>
                                            <InArgument x:TypeArguments="scg:Dictionary(x:String, x:String)">[itemDict]</InArgument>
                                          </InvokeMethod.TargetObject>
                                          <InArgument x:TypeArguments="x:String">[po+item("ITNO").tostring.trim.tolower]</InArgument>
                                          <InArgument x:TypeArguments="x:String">[item("SITE").tostring.trim.tolower]</InArgument>
                                        </InvokeMethod>
                                      </ActivityAction>
                                    </ForEach>
                                  </Sequence>
                                </If.Then>
                              </If>
                            </Sequence>
                          </If.Then>
                        </If>
                      </Sequence>
                    </ActivityAction>
                  </ForEach>
                  <ForEach x:TypeArguments="s:String[]" DisplayName="ForEach&lt;String[]&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_25" Values="[M3TotalTableRows]">
                    <ActivityAction x:TypeArguments="s:String[]">
                      <ActivityAction.Argument>
                        <DelegateInArgument x:TypeArguments="s:String[]" Name="row" />
                      </ActivityAction.Argument>
                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_132">
                        <Sequence.Variables>
                          <Variable x:TypeArguments="x:Int32" Name="count" />
                        </Sequence.Variables>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_221">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:Int32">[count]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:Int32">0</InArgument>
                          </Assign.Value>
                        </Assign>
                        <ForEach x:TypeArguments="scg:Dictionary(x:String, x:Object)" DisplayName="ForEach&lt;Dictionary&lt;String,Object&gt;&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_24" Values="[ListocrLineValues]">
                          <ActivityAction x:TypeArguments="scg:Dictionary(x:String, x:Object)">
                            <ActivityAction.Argument>
                              <DelegateInArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)" Name="lines" />
                            </ActivityAction.Argument>
                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_118">
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_220">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[New Dictionary(Of String,Object)]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_181">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[lines]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_182">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:Boolean">[itemCodeMatch]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_183">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:Boolean">[qtyMatch]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_184">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:Boolean">[PriceMatch]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_218">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:Object">[ListocrLineValues(COUNT)("PO_Number")]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[ListocrLineValues(count)("PO_Number").ToString +"/" +row(0)+"/" + row(11)]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_219">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:Int32">[count]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:Int32">[count + 1]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Switch x:TypeArguments="x:Boolean" DisplayName="Item code - Switch" Expression="[LinesDict(&quot;SUPPLIER_ITEM_CODE&quot;).ToString.trim.ToLower.contains(row(5).tostring.trim.tolower) OR LinesDict(&quot;SUPPLIER_ITEM_CODE&quot;).ToString.trim.ToLower.contains(itemDict(row(6)+row(5)).tostring.trim.tolower)]" sap2010:WorkflowViewState.IdRef="Switch`1_11">
                                <Sequence x:Key="True" sap2010:WorkflowViewState.IdRef="Sequence_107">
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_185">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:Boolean">[itemCodeMatch]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                </Sequence>
                                <Sequence x:Key="False" sap2010:WorkflowViewState.IdRef="Sequence_108">
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_186">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:Boolean">[itemCodeMatch]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                </Sequence>
                              </Switch>
                              <Switch x:TypeArguments="x:Boolean" DisplayName="Quantity - Switch" Expression="[Math.Abs(Convert.ToDouble(LinesDict(&quot;QUANTITY&quot;)) - Convert.ToDouble(row(3))) &lt;= 0.05]" sap2010:WorkflowViewState.IdRef="Switch`1_12">
                                <Sequence x:Key="True" sap2010:WorkflowViewState.IdRef="Sequence_109">
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_187">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:Boolean">[qtyMatch]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                </Sequence>
                                <Sequence x:Key="False" sap2010:WorkflowViewState.IdRef="Sequence_110">
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_188">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:Boolean">[qtyMatch]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                </Sequence>
                              </Switch>
                              <Switch x:TypeArguments="x:Boolean" DisplayName="Price - Switch" Expression="[Math.Abs(Convert.ToDouble(LinesDict(&quot;UNIT_PRICE&quot;)) - Convert.ToDouble(row(2))) &lt;= 0.05]" sap2010:WorkflowViewState.IdRef="Switch`1_13">
                                <Sequence x:Key="True" sap2010:WorkflowViewState.IdRef="Sequence_111">
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_189">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:Boolean">[PriceMatch]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                </Sequence>
                                <Sequence x:Key="False" sap2010:WorkflowViewState.IdRef="Sequence_112">
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_190">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:Boolean">[PriceMatch]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                </Sequence>
                              </Switch>
                              <If Condition="[itemCodeMatch and PriceMatch and qtyMatch]" sap2010:WorkflowViewState.IdRef="If_59">
                                <If.Then>
                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_113">
                                    <Sequence.Variables>
                                      <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="AddLineOcrWorkflowOutput" />
                                    </Sequence.Variables>
                                    <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;logfile&quot;,logfile},{&quot;M3TotalTableRows&quot;,M3TotalTableRows},{&quot;vatCodeConfig&quot;,vatCodeConfig},{&quot;division&quot;,division},{&quot;inbnValue&quot;,inbnValue}}]" ContinueOnError="True" DisplayName="Add Lines Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_16" OutputArguments="[AddLineOcrWorkflowOutput]" ResponseCode="[AddLineOcrWorkflowStatus]" WorkflowFile="[projectPath+&quot;\AddLine.xaml&quot;]" />
                                  </Sequence>
                                </If.Then>
                                <If.Else>
                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_117">
                                    <If Condition="[itemCodeMatch = False]" sap2010:WorkflowViewState.IdRef="If_56">
                                      <If.Then>
                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_114">
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_191">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">["Item code mismatch."]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                        </Sequence>
                                      </If.Then>
                                    </If>
                                    <If Condition="[PriceMatch = False]" sap2010:WorkflowViewState.IdRef="If_57">
                                      <If.Then>
                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_115">
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_192">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">[commentStatus + ". Price mismatch."]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                        </Sequence>
                                      </If.Then>
                                    </If>
                                    <If Condition="[qtyMatch = False]" sap2010:WorkflowViewState.IdRef="If_58">
                                      <If.Then>
                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_116">
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_193">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">[commentStatus + "Quantity mismatch."]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                        </Sequence>
                                      </If.Then>
                                    </If>
                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_194">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="x:String">[commentStatus + "Please verify"]</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_195">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="x:String">NEEDSVERIFICATION</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                  </Sequence>
                                </If.Else>
                              </If>
                            </Sequence>
                          </ActivityAction>
                        </ForEach>
                      </Sequence>
                    </ActivityAction>
                  </ForEach>
                </Sequence>
              </If.Then>
            </If>
            <iad:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_2">
              <iad:CommentOut.Activities>
                <ForEach x:TypeArguments="x:String" DisplayName="ForEach&lt;String&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_7" Values="[PONumbers]">
                  <ActivityAction x:TypeArguments="x:String">
                    <ActivityAction.Argument>
                      <DelegateInArgument x:TypeArguments="x:String" Name="pono" />
                    </ActivityAction.Argument>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_44">
                      <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="pono IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_8" Response="[poLinesResponseObject]" StatusCode="[poLinesResponseCode]" Url="[TenantID +&quot;M3/m3api-rest/v2/execute/PPS200MI/LstLine?maxrecs=0&amp;dateformat=YMD8&amp;excludeempty=false&amp;righttrim=true&amp;returncols=PUNO%2CITNO%2CSITE%2CORQA%2CRVQA&amp;format=PRETTY&amp;extendedresult=false&quot;]">
                        <iai:IONAPIRequestWizard.Headers>
                          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                            <scg:List x:TypeArguments="x:String" Capacity="4">
                              <x:String>Accept</x:String>
                            </scg:List>
                            <scg:List x:TypeArguments="x:String" Capacity="4">
                              <x:String>application/json</x:String>
                            </scg:List>
                          </scg:List>
                        </iai:IONAPIRequestWizard.Headers>
                        <iai:IONAPIRequestWizard.QueryParameters>
                          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                            <scg:List x:TypeArguments="x:String" Capacity="4">
                              <x:String>PUNO</x:String>
                            </scg:List>
                            <scg:List x:TypeArguments="x:String" Capacity="4">
                              <x:String>pono</x:String>
                            </scg:List>
                          </scg:List>
                        </iai:IONAPIRequestWizard.QueryParameters>
                      </iai:IONAPIRequestWizard>
                      <Switch x:TypeArguments="x:Boolean" Expression="[poLinesResponseCode = 200]" sap2010:WorkflowViewState.IdRef="Switch`1_4">
                        <Sequence x:Key="True" sap2010:WorkflowViewState.IdRef="Sequence_43">
                          <Sequence.Variables>
                            <Variable x:TypeArguments="njl:JToken" Name="variable1" />
                          </Sequence.Variables>
                          <ForEach x:TypeArguments="njl:JToken" DisplayName="ForEach&lt;JToken&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_9" Values="[poLinesResponseObject.readasjson(&quot;results&quot;)(0)(&quot;records&quot;)]">
                            <ActivityAction x:TypeArguments="njl:JToken">
                              <ActivityAction.Argument>
                                <DelegateInArgument x:TypeArguments="njl:JToken" Name="item" />
                              </ActivityAction.Argument>
                              <ForEach x:TypeArguments="scg:Dictionary(x:String, x:Object)" DisplayName="ForEach&lt;Dictionary&lt;String,Object&gt;&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_5" Values="[ListocrLineValues]">
                                <ActivityAction x:TypeArguments="scg:Dictionary(x:String, x:Object)">
                                  <ActivityAction.Argument>
                                    <DelegateInArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)" Name="lines" />
                                  </ActivityAction.Argument>
                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_42">
                                    <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" Title="{x:Null}" ContinueOnError="True" DisplayName="Message Box" sap2010:WorkflowViewState.IdRef="MessageBox_17" Selection="OK" Text="[item.tostring]" />
                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_71">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[New Dictionary(Of String,Object)]</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_72">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[lines]</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_73">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="x:Boolean">[itemCodeMatch]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_74">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="x:Boolean">[qtyMatch]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_75">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="x:Boolean">[PriceMatch]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                    <Switch x:TypeArguments="x:Boolean" DisplayName="Item code - Switch" Expression="[LinesDict(&quot;SUPPLIER_ITEM_CODE&quot;).ToString.trim.ToLower.contains(item(&quot;ITNO&quot;).tostring.trim.tolower) OR LinesDict(&quot;SUPPLIER_ITEM_CODE&quot;).ToString.trim.ToLower.contains(item(&quot;SITE&quot;).tostring.trim.tolower)]" sap2010:WorkflowViewState.IdRef="Switch`1_1">
                                      <Sequence x:Key="True" sap2010:WorkflowViewState.IdRef="Sequence_35">
                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_76">
                                          <Assign.To>
                                            <OutArgument x:TypeArguments="x:Boolean">[itemCodeMatch]</OutArgument>
                                          </Assign.To>
                                          <Assign.Value>
                                            <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                                          </Assign.Value>
                                        </Assign>
                                      </Sequence>
                                      <Sequence x:Key="False" sap2010:WorkflowViewState.IdRef="Sequence_36">
                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_77">
                                          <Assign.To>
                                            <OutArgument x:TypeArguments="x:Boolean">[itemCodeMatch]</OutArgument>
                                          </Assign.To>
                                          <Assign.Value>
                                            <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                                          </Assign.Value>
                                        </Assign>
                                      </Sequence>
                                    </Switch>
                                    <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Message Box" sap2010:WorkflowViewState.IdRef="MessageBox_15" Selection="OK" Text="[LinesDict(&quot;QUANTITY&quot;).tostring + &quot;  &quot;+LinesDict(&quot;UNIT_PRICE&quot;).tostring]" Title="itemCodeMatch" />
                                    <Switch x:TypeArguments="x:Boolean" DisplayName="Quantity - Switch" Expression="[Math.Abs(Convert.ToDouble(LinesDict(&quot;QUANTITY&quot;)) - Convert.ToDouble(Item(&quot;ORQT&quot;))) &lt;= 0.05]" sap2010:WorkflowViewState.IdRef="Switch`1_2">
                                      <Sequence x:Key="True" sap2010:WorkflowViewState.IdRef="Sequence_37">
                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_78">
                                          <Assign.To>
                                            <OutArgument x:TypeArguments="x:Boolean">[qtyMatch]</OutArgument>
                                          </Assign.To>
                                          <Assign.Value>
                                            <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                                          </Assign.Value>
                                        </Assign>
                                      </Sequence>
                                      <Sequence x:Key="False" sap2010:WorkflowViewState.IdRef="Sequence_38">
                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_79">
                                          <Assign.To>
                                            <OutArgument x:TypeArguments="x:Boolean">[qtyMatch]</OutArgument>
                                          </Assign.To>
                                          <Assign.Value>
                                            <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                                          </Assign.Value>
                                        </Assign>
                                      </Sequence>
                                    </Switch>
                                    <Switch x:TypeArguments="x:Boolean" DisplayName="Price - Switch" Expression="[Math.Abs(Convert.ToDouble(LinesDict(&quot;UNIT_PRICE&quot;)) - Convert.ToDouble(item(&quot;PUPR&quot;))) &lt;= 0.05]" sap2010:WorkflowViewState.IdRef="Switch`1_3">
                                      <Sequence x:Key="True" sap2010:WorkflowViewState.IdRef="Sequence_39">
                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_80">
                                          <Assign.To>
                                            <OutArgument x:TypeArguments="x:Boolean">[PriceMatch]</OutArgument>
                                          </Assign.To>
                                          <Assign.Value>
                                            <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                                          </Assign.Value>
                                        </Assign>
                                      </Sequence>
                                      <Sequence x:Key="False" sap2010:WorkflowViewState.IdRef="Sequence_40">
                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_81">
                                          <Assign.To>
                                            <OutArgument x:TypeArguments="x:Boolean">[PriceMatch]</OutArgument>
                                          </Assign.To>
                                          <Assign.Value>
                                            <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                                          </Assign.Value>
                                        </Assign>
                                      </Sequence>
                                    </Switch>
                                    <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" Title="{x:Null}" ContinueOnError="True" DisplayName="Message Box" sap2010:WorkflowViewState.IdRef="MessageBox_12" Selection="OK" Text="[&quot;PriceMatch &quot;+PriceMatch.tostring +&quot; qtyMatch &quot;+qtyMatch.tostring +&quot; itemCodeMatch &quot;+itemCodeMatch.tostring]" />
                                    <If Condition="[itemCodeMatch and PriceMatch and qtyMatch]" sap2010:WorkflowViewState.IdRef="If_27">
                                      <If.Then>
                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_41">
                                          <Sequence.Variables>
                                            <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="AddLineOcrWorkflowOutput" />
                                          </Sequence.Variables>
                                          <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;logfile&quot;,logfile},{&quot;M3TotalTableRows&quot;,M3TotalTableRows},{&quot;vatCodeConfig&quot;,vatCodeConfig},{&quot;division&quot;,division},{&quot;inbnValue&quot;,inbnValue}}]" ContinueOnError="True" DisplayName="Add Lines Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_8" OutputArguments="[AddLineOcrWorkflowOutput]" ResponseCode="[AddLineOcrWorkflowStatus]" WorkflowFile="[projectPath+&quot;\AddLine.xaml&quot;]" />
                                        </Sequence>
                                      </If.Then>
                                    </If>
                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_87">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="x:Int32">[Itr]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="x:Int32">[Itr+1]</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                  </Sequence>
                                </ActivityAction>
                              </ForEach>
                            </ActivityAction>
                          </ForEach>
                        </Sequence>
                      </Switch>
                    </Sequence>
                  </ActivityAction>
                </ForEach>
              </iad:CommentOut.Activities>
            </iad:CommentOut>
          </Sequence>
        </Sequence>
      </If.Else>
    </If>
    <sads:DebugSymbol.Symbol>d15DOlxVc2Vyc1xybmFnZW5kcmExXEFwcERhdGFcTG9jYWxcSW5mb3JSUEFcTTNJbnZvaWNlUHJvY2Vzc2luZ0dlbkFJVjNcUE8zV2F5TWF0Y2hpbmcxICgyKS54YW1sjAVdA84XDgIBAXszezYCAQJ9BYQBDgMBvweFAQWOAQ4DAbsHjwEFrgEfAwG0B68BBcEDCgMBrgbCAwXwAwoDAYwG8QMF4wUQAwG/BeQFBcwXCgIBA4IBOoIBTwMBwgd/O39JAwHAB4sBC4sBOgMBvgeHATGHAT0DAbwHjwGaAo8BpgIDAbkHjwHGAo8BjwMDAbcHjwGyAo8BwQIDAbUHrwETrwEoAwGvBrEBCaoDFAMBwAatAwm/AxQDAbEGwgMTwgNsAwGNBsQDCd4DFAMBmAbhAwnuAw4DAY8G9wMHrAQhAwGFBq0EB+gEDAMB/AXpBAfiBQwDAcAF5AUT5AUrAgEE5gUJmg4OAwH2Ap0OCcoXFAIBBrUBC7wBFAMBsAe9AQupAxADAcEGrgMLtQMUAwG7BrYDC70DFAMBtwa+Awu+A74BAwGyBskDC8kDvwMDAaYGygML1QMQAwGeBtYDC90DFAMBmQbhAxfhA0wDAZAG4wMN7AMYAwGSBvcDnwL3A6sCAwGKBvcDywL3A48DAwGIBvcDtwL3A8YCAwGGBq0EFa0EfwMB/QWvBAvmBCUDAf4F6QQV6QQqAwHBBesEC8sFFgMB0QXOBQvgBRYDAcMF5gUX5gUqAwH3AugFDYMOGAMBiAOGDg2YDhgDAfkCng4LwBEcAwH1AsERC8kXFgIBB7oBOLoBWwMBswe3ATm3AT8DAbEHvQEZvQFvAwHCBr8BD5IDGgMB0QaVAw+nAxoDAcMGswM2swOPAQMBvgawAzewA0YDAbwGuwM2uwNBAwG6BrgDN7gDPwMBuAa+A5cBvgOoAQMBtQa+A7ABvgO7AQMBswbJA/QCyQOGAwMBrAbJA9QCyQPmAgMBqgbJAynJA9MBAwGpBskDlAPJA7wDAwGnBsoDGcoDMQMBnwbMAw/TAxgDAaEG2wM22wM+AwGcBtgDN9gDQQMBmgbkAw/rAxgDAZMGrwSjAq8ErwIDAYMGrwTPAq8EkwMDAYEGrwS7Aq8EygIDAf8F8QQN+AQWAwH4BfkEDcoFEgMB0gXPBQ3WBRYDAc0F1wUN3gUWAwHJBd8FDd8FwAEDAcQF8gUP+QUYAwG4BfoFD4UGKQMBsQWGBg+VBhQDAagFlgYPjgsZAwGpA48LD+kLFAMBjgPqCw/xCxgDAYoD8gsPgg4gAwGJA4cOD44OGAMBhAOPDg+WDhgDAf8Clw4Plw7CAQMB+gLNEQ3RER4DAfQC0hEN1hMYAwG9AdcTDY8WEgIBCZAWDcgXHgIBCMABEfkBHAMBkwf6ARGRAxwDAdIGlgMRnQMaAwHNBp4DEaUDGgMByQamAxGmA8QBAwHEBtEDOtEDYwMBpAbOAzvOA0MDAaIG6QM66QNIAwGWBuYDO+YDRQMBlAb2BDr2BF0DAfsF8wQ78wRBAwH5BfkEG/kEagMB0wX7BBGsBRwDAeEFrwURyAUcAwHUBdQFONQFcQMB0AXRBTnRBUgDAc4F3AU43AVDAwHMBdkFOdkFQQMBygXfBZkB3wWqAQMBxwXfBbIB3wW9AQMBxQX3BTr3BdwBAwG7BfQFO/QFTQMBuQX6BcQC+gXkAgMBtgX6BZkD+gXXBAMBtAX6BfAC+gWUAwMBsgWGBh2GBkUDAakFiAYTkwYYAwGrBZYGwAGWBtUBAwGmBZsGE4wLHgMBqgOPCx2PC14DAY8DkQsT5wseAwGRA+8LOu8LQQMBjQPsCzvsC0MDAYsDjA46jA5BAwGHA4kOO4kOQwMBhQOUDjqUDpEBAwGCA5EOO5EOSgMBgAOXDpsBlw6sAQMB/QKXDrQBlw6/AQMB+wLTEQ/eERQDAewC3xEP5hEYAwHoAucRD+4RGAMB5ALvEQ/2ERgDAeAC9xEP/hEYAwHcAv8RD9UTGQMBvgHXExvXEzwCAQrZExGNFhwCAQzBARPIARwDAawHyQET0AEcAwGoB9EBE9gBHAMBpAfZARPgARwDAaAH4QET6AEcAwGcB+kBE/ABHAMBmAfxARP4ARwDAZQHggITiQIcAwGOB4oCE6kCLQMBhweqAhPTAhgDAfMG1AIT2wIcAwHuBtwCE+MCHAMB6QbkAhPrAhwDAeUG7AIT8wIcAwHeBvQCE5ADHgMB0wabAzybA2sDAdAGmAM9mANMAwHOBqMDPKMDRwMBzAagAz2gA0UDAcoGpgOdAaYDrgEDAccGpgO2AaYDwQEDAcUG/AQTgwUcAwH0BYQFE40FHAMB8AWOBROVBRwDAewFlgUTqwUYAwHiBbAFE7cFHAMB3QW4BRO/BRwDAdkFwAUTxwUcAwHVBYgGIYgGlwEDAawFigYXkQYgAwGtBaEGFYsLGgMBqwOXCxWeCx4DAaQDnwsVpgseAwGfA6cLFeYLGgMBkgPTER3TEWQDAe0C1RET3BEcAwHvAuQRRuQRXQMB6wLhEUfhEVoDAekC7BFG7BFdAwHnAukRR+kRWQMB5QL0EUT0EVkDAeMC8RFF8RFQAwHhAvwRRPwRfAMB3wL5EUX5EVADAd0C/xGNAf8RmgEDAdoChBIT0xMeAwG/AdoTE+ETHAMBuQHiExPpExwDAbUB6hMTrRQdAwGYAa4UE4wWHQIBDcYBPsYBcQMBrwfDAT/DAUUDAa0HzgE+zgFxAwGrB8sBP8sBRQMBqQfWAT7WAXEDAacH0wE/0wFMAwGlB94BPt4BcQMBowfbAT/bAUUDAaEH5gE+5gFxAwGfB+MBP+MBSwMBnQfuAT7uAXEDAZsH6wE/6wFFAwGZB/YBPvYBcQMBlwfzAT/zAU0DAZUHhwI+hwJiAwGRB4QCP4QCSAMBjweKAqECigKtAgMBjAeKAs0CigKWAwMBigeKArkCigLIAgMBiAeqAiGqAjYDAfQGrAIX0QIiAwH2BtkCPtkCXwMB8QbWAj/WAkUDAe8G4QI+4QJsAwHsBt4CP94CRQMB6gbpAj7pAq8BAwHoBuYCP+YCRwMB5gbxAj7xAtEBAwHhBu4CP+4CTgMB3wb2Ahf9AiADAdkGhQMbjAMkAwHUBoEFP4EFQwMB9wX+BED+BFYDAfUFigUZigVIAwHzBYYFP4YFSgMB8QWTBT6TBW0DAe8FkAU/kAVOAwHtBZYFIZYFdAMB4wWYBRefBSADAegFogUXqQUgAwHkBbUFP7UFRAMB4AWyBUCyBVYDAd4FvQU+vQVzAwHcBboFP7oFSgMB2gXFBT7FBVgDAdgFwgU/wgVOAwHWBY8GQo8GsgEDAbAFjAZDjAZOAwGuBaEGI6EGZQMBrAOjBhngByQDAcsE4wcZiQskAwGuA5wLQJwLZgMBpwOZC0GZC0YDAaUDpAtApAtmAwGiA6ELQaELSgMBoAOnCyOnC2YDAZMDqQsZxAszAwGaA8cLGeQLMwMBlQPaET7aEWMDAfIC1xE/1xFFAwHwAoUSFYwSHgMB1AKNEhWNEu4DAwHMAo4SFdITGgMBwAHfE0jfE7MBAwG8AdwTSdwTUQMBugHnE1jnE3sDAbgB5BNZ5BNjAwG2AeoTkQHqE5sBAwGzAe8TF6sUIgMBmQGuFJUBrhSpAQMBlgGzFBeKFiICAQ6tAhm0AiIDAYMHtQIZ0AIeAwH3BvsCQvsCYQMB3Ab4AkP4AlIDAdoGigNGigNVAwHXBocDR4cDVgMB1QadBUKdBVMDAesFmgVDmgVLAwHpBacFQqcFSQMB5wWkBUOkBUsDAeUFsgYbuQYkAwGiBboGG8EGJAMBnQXCBhvJBiQDAZgFygYb0QYkAwGTBdIGG9kGJAMBjgXaBhvhBiQDAYkF4gYb6QYkAwGEBeoGG/EGJAMB/wTyBhv5BiQDAfoE+gYbgQckAwH1BIIHG6sHNQMB7gSsBxuzByQDAeoEtAcb3wcgAwHMBOcHG+4HJAMBxwTvBxv2ByQDAcME9wcb/gckAwG+BP8HG7sKIAMBsQO8ChvHCiwDAbADyAobiAssAwGvA6kLxAKpC8wCAwGdA6kL0QKpC5QDAwGbA8cLxALHC8wCAwGYA8cL0QLHC5QDAwGWA4cSQYcSRwMB1QKNEowDjRKmAwMB0gKNEuQCjRL+AgMB0AKNEjONEtkBAwHPAo0StAONEusDAwHNAo4SI44SQwMBwQGQEhnNEyQDAccB0BMZ0BPcAQMBwwHwExmFFDMDAawBhhQZqhQeAwGaAbcUGb4UIgMBkgG/FBmJFiMCAQ+yAkayAmkDAYYHrwJHrwJNAwGEB7UCJ7UCfQMB+Aa3Ah3AAigDAf4GwwIdzgIoAwH5BrcGYLcGhAEDAaUFtAZhtAZsAwGjBb8GYL8GZgMBoAW8BmG8BmwDAZ4FxwZGxwZ4AwGbBcQGR8QGTQMBmQXPBkbPBngDAZYFzAZHzAZNAwGUBdcGRtcGeAMBkQXUBkfUBk0DAY8F3wZG3wZ4AwGMBdwGR9wGTQMBigXnBkbnBngDAYcF5AZH5AZNAwGFBe8GRu8GeAMBggXsBkfsBk0DAYAF9wZG9wZ4AwH9BPQGR/QGTQMB+wT/Bkb/BmkDAfgE/AZH/AZMAwH2BIIHsgKCB74CAwHzBIIH3gKCB6EDAwHxBIIHygKCB9kCAwHvBLEHSLEHawMB7QSuB0muB08DAesEtAcptAd4AwHNBLYHH8gHKgMB3ATLBx/dByoDAc4E7AdQ7AdlAwHKBOkHUekHWAMByAT0B1D0B4gBAwHGBPEHUfEHWAMBxAT8B0b8B3oDAcEE+QdH+QdNAwG/BP8HKf8HPwMBsgOBCB+5CioDAbQDlBIbmxIkAwHIApwSG6MSJAMBwwKkEhvMEyADAcgB0BOmAdATxgEDAcYB0BPOAdAT2QEDAcQB8BOkAvATvQIDAbEB8BPlAvATywQDAa8B8BPJAvAT4AIDAa0BhhQnhhREAwGbAYgUHagUKAMBnQG8FEO8FEQDAZUBuRREuRRLAwGTAb8UygG/FN8BAwGQAcQUHYcWKAIBELgCH78CKAMB/wbEAh/NAigDAfoGtwchvgcqAwHmBL8HIcYHKgMB4gTHByHHB9UBAwHdBMwHIdMHKgMB2ATUByHbByoDAdQE3Ach3AfVAQMBzwSCCCG4CisDAbUDmRJSmRKYAQMBywKWElOWEmYDAckCoRJHoRJ/AwHGAp4SSJ4SVgMBxAKkEimkEksDAckBphIftxIqAwG0AroSH8oTKgMBywGMFB+TFCgDAagBlBQfpxQkAwGeAcUUH8wUKAMBjAHNFB/UFCgDAYcB1RQf3BQoAwGDAd0UH+QUKAIBf+UUH+wUKAIBe+0UH/QUKAIBcvUUH/wUKAIBbf0UH5IVKAIBW5MVH6gVKAIBT6kVH74VKAIBQ78VH4YWJAIBEb0CSr0CfwMBgge6Aku6AlEDAYAHygIlygJUAwH9BsYCS8YCUQMB+wa5B025B1wDAecExAdMxAdTAwHlBMEHTcEHVQMB4wTHB64Bxwe/AQMB4ATHB8cBxwfSAQMB3gTRB0zRB2QDAdsEzgdNzgdcAwHZBNkHTNkHVwMB1wTWB03WB1UDAdUE3AeuAdwHvwEDAdIE3AfHAdwH0gEDAdAEggifAYIIqAEDAbwEhwgltgowAwG2A6cSIacSoQIDAb4CqBIhthIrAwG1Ar4SIcUSKgMBrgLGEiHJEyYDAcwBkRRMkRSHAQMBqwGOFE2OFFMDAakBlBQtlBRWAwGfAZYUI6UULgMBoAHKFGTKFIYBAwGPAccUZccUcAMBjQHSFGTSFGsDAYoBzxRlzxRwAwGIAdoUS9oUUAMBhgHXFEzXFFsDAYQB4hRL4hRQAwGCAd8UTN8UVgMBgAHqFEvqFFACAX7nFEznFFgCAXzyFEryFJUBAgF27xRL7xRyAgFz+hRJ+hRUAgFw9xRK9xRRAgFu/RRv/RTUAgIBXP4UIYcVLAIBaIgVIZEVLAIBY5MVbpMV0wECAVCUFSGdFSwCAVaeFSGnFSwCAVGpFWupFdIBAgFEqhUhsxUsAgFKtBUhvRUsAgFFvxUtvxVaAgESwRUjxhUuAgE6yRUjhBYuAgEWiwgnkggwAwG3BJMIJ5oIMAMBsgSbCCeiCDADAa0EowgnqggwAwGoBKsIJ8IIQQMBoQTDCCfKCDADAZ0EywgntQosAwG3A6cSrgGnEosCAwHBAqcSkwKnEp4CAwG/AqgSowGoErgBAwG8Aq0SJbQSMAMBtgLDEkzDEmMDAbECwBJNwBJRAwGvAsYSL8YSPwMBzQHIEiXiEjADAZoC5RIlxxMwAwHPAZcUJaQULwMBoQH/FCOGFSwCAWmJFSOQFSwCAWSVFSOcFSwCAVefFSOmFSwCAVKrFSOyFSwCAUu1FSO8FSwCAUbFFSXFFc0EAgE7yhUl1xUqAgEy2BUl5RUqAgEp5hUl8xUqAgEg9BUl+xUuAgEb/BUlgxYuAgEXkAhSkAhoAwG6BI0IU40IWgMBuASYCFKYCGgDAbUElQhTlQhZAwGzBKAIUqAIaAMBsASdCFOdCFkDAa4EqAhSqAhoAwGrBKUIU6UIWQMBqQSrCK0Cqwi5AgMBpgSrCNkCqwiCBAMBpASrCMUCqwjUAgMBogTICFTICHcDAaAExQhVxQhbAwGeBMsINcsIhAEDAbgDzQgr3wg2AwGPBOIIK7MKNgMBuQOuEiezEjYDAbcCyRIn0BIwAwGoAtESJ9gSMAMBpALZEifZEoUCAwGfAtoSJ+ESMAMBmwLmEifGEzIDAdABlxSlAZcU9gEDAacBnBQpohQ4AwGiAYQVT4QVUwIBbIEVUIEVXwIBao4VT44VVAIBZ4sVUIsVXwIBZZoVT5oVUwIBWpcVUJcVWgIBWKQVT6QVVAIBVaEVUKEVWgIBU7AVT7AVUwIBTq0VUK0VXAIBTLoVT7oVVAIBSbcVULcVXAIBR8UV9wPFFZMEAgFBxRXNA8UV6QMCAT/FFUPFFckCAgE+xRWhBMUVygQCATzKFTPKFUwCATPMFSnVFTQCATXYFTPYFUkCASraFSnjFTQCASzmFTPmFUcCASHoFSnxFTQCASP5FVD5FXECAR72FVH2FWACARyBFlCBFmECARr+FVH+FVkCARjOCC3VCDYDAZkE1ggt3Qg2AwGVBN4ILd4I4QEDAZAE8Agt9wg2AwGLBPgILf8INgMBhwSACS2HCTYDAf8DiAktoQlHAwH4A6IJLdAJMgMB4QPRCS3cCT4DAeAD3Qkt5Ak2AwHbA+UJLY4KRwMB1AOPCi2WCjYDAdADlwotsgoyAwG6A7ASXrAScAMBugKyElKyElgDAbgCyxJTyxJiAwGpAtYSUtYSWQMBpwLTElPTElsDAaUC2RK0AdkS7wEDAaIC2RL3AdkSggIDAaAC3xJT3xJYAwGeAtwSVNwSXgMBnALnEinuEjIDAZQC7xIp7xKCBAMBjALwEinFEy4DAdEBoRRSoRR2AwGmAaAUUqAUeQMBpQGeFG6eFHgDAaMBzRUr1BU0AgE22xUr4hU0AgEt6RUr8BU0AgEk0whY0wiYAQMBnATQCFnQCGgDAZoE2whY2whfAwGYBNgIWdgIYQMBlgTeCLoB3gjLAQMBkwTeCNMB3gjeAQMBkQT1CFj1CIsBAwGOBPIIWfIIXwMBjAT9CFj9CIsBAwGKBPoIWfoIXwMBiASCCVmCCV8DAYAEiAnDAogJzwIDAf0DiAnvAogJogQDAfsDiAnbAogJ6gIDAfkDogk7oglQAwHiA6QJMc4JPAMB5APiCVjiCXUDAd4D3wlZ3wlfAwHcA+UJxALlCdACAwHZA+UJ8ALlCbMDAwHXA+UJ3ALlCesCAwHVA5QKWpQKfQMB0wORCluRCmEDAdEDlwo7lwqKAQMBuwOZCjGjCjwDAcYDpgoxsAo8AwG8A+kSVekSWwMBlQLvEqAD7xK6AwMBkgLvEvgC7xKSAwMBkALvEkfvEu0BAwGPAu8SyAPvEv8DAwGNAvASN/ASVwMB0gHyEi3DEzgDAdQB0hVW0hVtAgE5zxVXzxVmAgE34BVW4BV7AgEw3RVX3RVmAgEu7hVW7hV8AgEn6xVX6xVmAgElqAkzrwk8AwH0A7AJM80JOAMB5QOaCjOhCjwDAcwDogozogrnAQMBxwOnCjOuCjwDAcIDrwozrwrnAQMBvQPzEi/6EjgDAYgC+xIvghM4AwGDAoMTL8ITNAMB1QGtCWCtCYMBAwH3A6oJYaoJZwMB9QOwCUGwCZABAwHmA7IJN8sJQgMB5wOfCl6fCo0BAwHPA5wKX5wKbgMBzQOiCsABogrRAQMBygOiCtkBogrkAQMByAOsCl6sCnsDAcUDqQpfqQpuAwHDA68KwAGvCtEBAwHAA68K2QGvCuQBAwG+A/gSZvgSrAEDAYsC9RJn9RJ6AwGJAoATW4ATkwEDAYYC/RJc/RJqAwGEAoMTPYMTXwMB1gGFEzPAEzgDAdgBswk5uglCAwHwA7sJOcIJQgMB7APDCTnKCUIDAegDhRNBhRNRAwHZAYcTN6ETQgMB7wGkEze+E0IDAdsBuAlkuAmzAQMB8wO1CWW1CWsDAfEDwAlkwAmeAgMB7wO9CWW9CWsDAe0DyAlkyAmzAQMB6wPFCWXFCWsDAekDiBM5jxNCAwH9AZATOZcTQgMB+QGYEzmYE5cCAwH0AZkTOaATQgMB8AGlEzmsE0IDAekBrRM5tBNCAwHlAbUTObUToAIDAeABthM5vRNCAwHcAYoTZYoTdAMB/gGVE2SVE2sDAfwBkhNlkhNtAwH6AZgTxgGYE4ECAwH3AZgTiQKYE5QCAwH1AZ4TZZ4TagMB8wGbE2abE3ADAfEBqhNkqhOuAQMB7AGnE2WnE3QDAeoBshNkshNxAwHoAa8TZa8TbQMB5gG1E8YBtROKAgMB4wG1E5ICtROdAgMB4QG7E2W7E2oDAd8BuBNmuBNwAwHdAQ==</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="Assign_119" sap:VirtualizedContainerService.HintSize="2110.66666666667,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_120" sap:VirtualizedContainerService.HintSize="2110.66666666667,61.3333333333333" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_1" sap:VirtualizedContainerService.HintSize="2110.66666666667,22" />
      <sap2010:ViewStateData Id="Assign_1" sap:VirtualizedContainerService.HintSize="1036,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_2" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_3" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_4" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_5" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_6" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_7" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_83" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Sequence_1" sap:VirtualizedContainerService.HintSize="724,793.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_8" sap:VirtualizedContainerService.HintSize="702,61.3333333333333" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_2" sap:VirtualizedContainerService.HintSize="702,22" />
      <sap2010:ViewStateData Id="Assign_9" sap:VirtualizedContainerService.HintSize="554,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_10" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Sequence_2" sap:VirtualizedContainerService.HintSize="264,185.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_11" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Sequence_3" sap:VirtualizedContainerService.HintSize="264,185.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_1" sap:VirtualizedContainerService.HintSize="554,337.333333333333" />
      <sap2010:ViewStateData Id="Sequence_4" sap:VirtualizedContainerService.HintSize="576,562.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_2" sap:VirtualizedContainerService.HintSize="702,714.666666666667" />
      <sap2010:ViewStateData Id="Assign_12" sap:VirtualizedContainerService.HintSize="702,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_13" sap:VirtualizedContainerService.HintSize="702,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_14" sap:VirtualizedContainerService.HintSize="702,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_15" sap:VirtualizedContainerService.HintSize="702,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_16" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_17" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Catch`1_1" sap:VirtualizedContainerService.HintSize="400,131.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_1" sap:VirtualizedContainerService.HintSize="702,300.666666666667" />
      <sap2010:ViewStateData Id="Sequence_5" sap:VirtualizedContainerService.HintSize="724,1748">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_6" sap:VirtualizedContainerService.HintSize="746,2705.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_18" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_19" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Append_Line_1" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_7" sap:VirtualizedContainerService.HintSize="264,348.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_3" sap:VirtualizedContainerService.HintSize="1036,2857.33333333333" />
      <sap2010:ViewStateData Id="Sequence_8" sap:VirtualizedContainerService.HintSize="1058,3082.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_20" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_21" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Append_Line_2" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_9" sap:VirtualizedContainerService.HintSize="264,348.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_4" sap:VirtualizedContainerService.HintSize="2110.66666666667,3234.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeWorkflow_1" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Assign_22" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="If_5" sap:VirtualizedContainerService.HintSize="464,213.333333333333" />
      <sap2010:ViewStateData Id="Assign_23" sap:VirtualizedContainerService.HintSize="464,61.3333333333333" />
      <sap2010:ViewStateData Id="Sequence_10" sap:VirtualizedContainerService.HintSize="486,500.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_24" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Sequence_11" sap:VirtualizedContainerService.HintSize="264,185.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_6" sap:VirtualizedContainerService.HintSize="464,337.333333333333" />
      <sap2010:ViewStateData Id="If_7" sap:VirtualizedContainerService.HintSize="2110.66666666667,652.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="IONAPIRequestWizard_3" sap:VirtualizedContainerService.HintSize="1134,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_4" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_8" sap:VirtualizedContainerService.HintSize="1134,212" />
      <sap2010:ViewStateData Id="Assign_25" sap:VirtualizedContainerService.HintSize="822,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_26" sap:VirtualizedContainerService.HintSize="510,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_27" sap:VirtualizedContainerService.HintSize="510,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_28" sap:VirtualizedContainerService.HintSize="510,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_29" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_30" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="If_9" sap:VirtualizedContainerService.HintSize="510,213.333333333333" />
      <sap2010:ViewStateData Id="Sequence_12" sap:VirtualizedContainerService.HintSize="532,641.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_31" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_32" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_33" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Sequence_13" sap:VirtualizedContainerService.HintSize="264,388">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_10" sap:VirtualizedContainerService.HintSize="822,793.333333333333" />
      <sap2010:ViewStateData Id="Sequence_14" sap:VirtualizedContainerService.HintSize="844,1018.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_34" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_35" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Append_Line_3" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_15" sap:VirtualizedContainerService.HintSize="264,348.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_11" sap:VirtualizedContainerService.HintSize="1134,1170.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_16" sap:VirtualizedContainerService.HintSize="2110.66666666667,1608.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_196" sap:VirtualizedContainerService.HintSize="1801.33333333333,61.3333333333333" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_11" sap:VirtualizedContainerService.HintSize="1801.33333333333,22" />
      <sap2010:ViewStateData Id="Assign_203" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="If_63" sap:VirtualizedContainerService.HintSize="464,213.333333333333" />
      <sap2010:ViewStateData Id="If_64" sap:VirtualizedContainerService.HintSize="1801.33333333333,365.333333333333" />
      <sap2010:ViewStateData Id="Assign_253" sap:VirtualizedContainerService.HintSize="554,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_254" sap:VirtualizedContainerService.HintSize="554,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_255" sap:VirtualizedContainerService.HintSize="554,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_256" sap:VirtualizedContainerService.HintSize="554,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_257" sap:VirtualizedContainerService.HintSize="554,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_258" sap:VirtualizedContainerService.HintSize="554,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_259" sap:VirtualizedContainerService.HintSize="554,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_260" sap:VirtualizedContainerService.HintSize="554,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_261" sap:VirtualizedContainerService.HintSize="554,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_262" sap:VirtualizedContainerService.HintSize="554,61.3333333333333" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_20" sap:VirtualizedContainerService.HintSize="554,22" />
      <sap2010:ViewStateData Id="Assign_263" sap:VirtualizedContainerService.HintSize="554,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_264" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_265" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Append_Line_27" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_141" sap:VirtualizedContainerService.HintSize="264,348.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_266" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_267" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Append_Line_28" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_142" sap:VirtualizedContainerService.HintSize="264,348.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_76" sap:VirtualizedContainerService.HintSize="554,500.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_143" sap:VirtualizedContainerService.HintSize="576,1801.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_235" sap:VirtualizedContainerService.HintSize="1124.66666666667,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_301" sap:VirtualizedContainerService.HintSize="1124.66666666667,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_302" sap:VirtualizedContainerService.HintSize="1124.66666666667,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_303" sap:VirtualizedContainerService.HintSize="924,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_307" sap:VirtualizedContainerService.HintSize="924,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_308" sap:VirtualizedContainerService.HintSize="924,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_309" sap:VirtualizedContainerService.HintSize="924,61.3333333333333" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_17" sap:VirtualizedContainerService.HintSize="924,22" />
      <sap2010:ViewStateData Id="Assign_241" sap:VirtualizedContainerService.HintSize="924,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_242" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_243" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Append_Line_24" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_136" sap:VirtualizedContainerService.HintSize="264,348.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_244" sap:VirtualizedContainerService.HintSize="612,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_245" sap:VirtualizedContainerService.HintSize="612,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_246" sap:VirtualizedContainerService.HintSize="612,61.3333333333333" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_18" sap:VirtualizedContainerService.HintSize="612,22" />
      <sap2010:ViewStateData Id="Assign_247" sap:VirtualizedContainerService.HintSize="464,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_248" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_291" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_292" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Sequence_150" sap:VirtualizedContainerService.HintSize="264,388">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_72" sap:VirtualizedContainerService.HintSize="464,540" />
      <sap2010:ViewStateData Id="Sequence_137" sap:VirtualizedContainerService.HintSize="486,765.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_73" sap:VirtualizedContainerService.HintSize="612,917.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_299" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="CommentOut_10" sap:VirtualizedContainerService.HintSize="612,157.333333333333" />
      <sap2010:ViewStateData Id="Assign_300" sap:VirtualizedContainerService.HintSize="612,61.3333333333333" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_19" sap:VirtualizedContainerService.HintSize="612,22" />
      <sap2010:ViewStateData Id="Assign_250" sap:VirtualizedContainerService.HintSize="612,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_251" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Append_Line_25" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_138" sap:VirtualizedContainerService.HintSize="264,247.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_252" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Append_Line_26" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_139" sap:VirtualizedContainerService.HintSize="264,247.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_74" sap:VirtualizedContainerService.HintSize="612,399.333333333333" />
      <sap2010:ViewStateData Id="Sequence_140" sap:VirtualizedContainerService.HintSize="634,2308.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_75" sap:VirtualizedContainerService.HintSize="924,2460.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_154" sap:VirtualizedContainerService.HintSize="946,3153.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_29" sap:VirtualizedContainerService.HintSize="976.666666666667,3304.66666666667" />
      <sap2010:ViewStateData Id="Sequence_153" sap:VirtualizedContainerService.HintSize="998.666666666667,3428.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_85" sap:VirtualizedContainerService.HintSize="1124.66666666667,3580.66666666667" />
      <sap2010:ViewStateData Id="Assign_236" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="CommentOut_9" sap:VirtualizedContainerService.HintSize="1124.66666666667,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_237" sap:VirtualizedContainerService.HintSize="418,60" />
      <sap2010:ViewStateData Id="Assign_268" sap:VirtualizedContainerService.HintSize="418,60" />
      <sap2010:ViewStateData Id="Assign_238" sap:VirtualizedContainerService.HintSize="418,60" />
      <sap2010:ViewStateData Id="Assign_239" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_240" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Catch`1_4" sap:VirtualizedContainerService.HintSize="404,21">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_4" sap:VirtualizedContainerService.HintSize="418,287">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_290" sap:VirtualizedContainerService.HintSize="418,60" />
      <sap2010:ViewStateData Id="CommentOut_7" sap:VirtualizedContainerService.HintSize="1124.66666666667,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_135" sap:VirtualizedContainerService.HintSize="1146.66666666667,4204.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_71" sap:VirtualizedContainerService.HintSize="1748.66666666667,4356.66666666667" />
      <sap2010:ViewStateData Id="Sequence_131" sap:VirtualizedContainerService.HintSize="1770.66666666667,4480.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_27" sap:VirtualizedContainerService.HintSize="1801.33333333333,4632" />
      <sap2010:ViewStateData Id="Assign_296" sap:VirtualizedContainerService.HintSize="464,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_297" sap:VirtualizedContainerService.HintSize="464,61.3333333333333" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_26" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_27" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_83" sap:VirtualizedContainerService.HintSize="464,212" />
      <sap2010:ViewStateData Id="Sequence_152" sap:VirtualizedContainerService.HintSize="486,538.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_84" sap:VirtualizedContainerService.HintSize="1801.33333333333,690.666666666667" />
      <sap2010:ViewStateData Id="Assign_298" sap:VirtualizedContainerService.HintSize="1801.33333333333,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_36" sap:VirtualizedContainerService.HintSize="590,60" />
      <sap2010:ViewStateData Id="InvokeWorkflow_2" sap:VirtualizedContainerService.HintSize="590,22" />
      <sap2010:ViewStateData Id="Assign_37" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="If_12" sap:VirtualizedContainerService.HintSize="590,208" />
      <sap2010:ViewStateData Id="Assign_38" sap:VirtualizedContainerService.HintSize="590,60" />
      <sap2010:ViewStateData Id="Assign_39" sap:VirtualizedContainerService.HintSize="590,60" />
      <sap2010:ViewStateData Id="Assign_40" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_41" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Sequence_17" sap:VirtualizedContainerService.HintSize="264,286.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_1" sap:VirtualizedContainerService.HintSize="590,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_42" sap:VirtualizedContainerService.HintSize="590,60" />
      <sap2010:ViewStateData Id="Assign_43" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_18" sap:VirtualizedContainerService.HintSize="264,184">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_2" sap:VirtualizedContainerService.HintSize="294,332" />
      <sap2010:ViewStateData Id="If_13" sap:VirtualizedContainerService.HintSize="590,480">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeWorkflow_3" sap:VirtualizedContainerService.HintSize="590,22" />
      <sap2010:ViewStateData Id="Assign_44" sap:VirtualizedContainerService.HintSize="590,60" />
      <sap2010:ViewStateData Id="InvokeWorkflow_4" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_45" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_19" sap:VirtualizedContainerService.HintSize="264,246">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_14" sap:VirtualizedContainerService.HintSize="464,394" />
      <sap2010:ViewStateData Id="If_15" sap:VirtualizedContainerService.HintSize="590,542" />
      <sap2010:ViewStateData Id="Assign_46" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_47" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_5" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_6" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_16" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="CommentOut_1" sap:VirtualizedContainerService.HintSize="242,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeWorkflow_5" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_20" sap:VirtualizedContainerService.HintSize="264,345.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_17" sap:VirtualizedContainerService.HintSize="590,457.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_48" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_7" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_21" sap:VirtualizedContainerService.HintSize="264,247.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_18" sap:VirtualizedContainerService.HintSize="590,359.333333333333" />
      <sap2010:ViewStateData Id="Sequence_22" sap:VirtualizedContainerService.HintSize="200,52">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="CommentOut_6" sap:VirtualizedContainerService.HintSize="1801.33333333333,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_119" sap:VirtualizedContainerService.HintSize="1823.33333333333,6254.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_49" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_50" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Append_Line_4" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_23" sap:VirtualizedContainerService.HintSize="264,348.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_19" sap:VirtualizedContainerService.HintSize="200,52">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_142" sap:VirtualizedContainerService.HintSize="287,60" />
      <sap2010:ViewStateData Id="Assign_143" sap:VirtualizedContainerService.HintSize="287,60" />
      <sap2010:ViewStateData Id="Assign_144" sap:VirtualizedContainerService.HintSize="287,60" />
      <sap2010:ViewStateData Id="Assign_145" sap:VirtualizedContainerService.HintSize="287,60" />
      <sap2010:ViewStateData Id="Assign_146" sap:VirtualizedContainerService.HintSize="287,60" />
      <sap2010:ViewStateData Id="Assign_147" sap:VirtualizedContainerService.HintSize="287,60" />
      <sap2010:ViewStateData Id="Assign_148" sap:VirtualizedContainerService.HintSize="287,60" />
      <sap2010:ViewStateData Id="Assign_149" sap:VirtualizedContainerService.HintSize="2624,60" />
      <sap2010:ViewStateData Id="InvokeWorkflow_13" sap:VirtualizedContainerService.HintSize="2624,22" />
      <sap2010:ViewStateData Id="Assign_150" sap:VirtualizedContainerService.HintSize="2313,60" />
      <sap2010:ViewStateData Id="Assign_151" sap:VirtualizedContainerService.HintSize="2313,60" />
      <sap2010:ViewStateData Id="Append_Line_15" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="InvokeMethod_4" sap:VirtualizedContainerService.HintSize="218,128" />
      <sap2010:ViewStateData Id="Sequence_85" sap:VirtualizedContainerService.HintSize="240,252">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_20" sap:VirtualizedContainerService.HintSize="287,400" />
      <sap2010:ViewStateData Id="Sequence_86" sap:VirtualizedContainerService.HintSize="309,524">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_43" sap:VirtualizedContainerService.HintSize="464,672" />
      <sap2010:ViewStateData Id="Sequence_87" sap:VirtualizedContainerService.HintSize="486,858">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_152" sap:VirtualizedContainerService.HintSize="1780,60" />
      <sap2010:ViewStateData Id="Assign_153" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_154" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_16" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_155" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_88" sap:VirtualizedContainerService.HintSize="264,446">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeWorkflow_14" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_156" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_157" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_158" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_159" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_160" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_89" sap:VirtualizedContainerService.HintSize="264,646">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_161" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_162" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_163" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_164" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_165" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_90" sap:VirtualizedContainerService.HintSize="264,584">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_44" sap:VirtualizedContainerService.HintSize="553,794" />
      <sap2010:ViewStateData Id="Sequence_91" sap:VirtualizedContainerService.HintSize="575,918">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_166" sap:VirtualizedContainerService.HintSize="825,60" />
      <sap2010:ViewStateData Id="InvokeWorkflow_15" sap:VirtualizedContainerService.HintSize="825,22" />
      <sap2010:ViewStateData Id="Assign_167" sap:VirtualizedContainerService.HintSize="678,60" />
      <sap2010:ViewStateData Id="Assign_168" sap:VirtualizedContainerService.HintSize="678,60" />
      <sap2010:ViewStateData Id="Assign_169" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_170" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_17" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_92" sap:VirtualizedContainerService.HintSize="264,346">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_171" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_172" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_18" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_93" sap:VirtualizedContainerService.HintSize="264,346">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_45" sap:VirtualizedContainerService.HintSize="553,494" />
      <sap2010:ViewStateData Id="If_46" sap:VirtualizedContainerService.HintSize="678,642" />
      <sap2010:ViewStateData Id="Sequence_94" sap:VirtualizedContainerService.HintSize="700,966">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_47" sap:VirtualizedContainerService.HintSize="825,1114" />
      <sap2010:ViewStateData Id="Sequence_95" sap:VirtualizedContainerService.HintSize="847,1400">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_173" sap:VirtualizedContainerService.HintSize="847,60" />
      <sap2010:ViewStateData Id="Sequence_96" sap:VirtualizedContainerService.HintSize="869,1624">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_48" sap:VirtualizedContainerService.HintSize="1469,1772" />
      <sap2010:ViewStateData Id="Sequence_97" sap:VirtualizedContainerService.HintSize="1491,1896">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_49" sap:VirtualizedContainerService.HintSize="1780,2044" />
      <sap2010:ViewStateData Id="Sequence_98" sap:VirtualizedContainerService.HintSize="1802,2268">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_50" sap:VirtualizedContainerService.HintSize="2313,2416" />
      <sap2010:ViewStateData Id="Sequence_99" sap:VirtualizedContainerService.HintSize="2335,2740">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_174" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_175" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_176" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_100" sap:VirtualizedContainerService.HintSize="264,390">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_51" sap:VirtualizedContainerService.HintSize="2624,2888" />
      <sap2010:ViewStateData Id="Sequence_101" sap:VirtualizedContainerService.HintSize="200,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_102" sap:VirtualizedContainerService.HintSize="222,175">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_21" sap:VirtualizedContainerService.HintSize="287,323">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_70" sap:VirtualizedContainerService.HintSize="200,52">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="CommentOut_3" sap:VirtualizedContainerService.HintSize="1862.66666666667,148" />
      <sap2010:ViewStateData Id="If_52" sap:VirtualizedContainerService.HintSize="464,208" />
      <sap2010:ViewStateData Id="CommentOut_4" sap:VirtualizedContainerService.HintSize="1840.66666666667,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_51" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="If_20" sap:VirtualizedContainerService.HintSize="1818.66666666667,213.333333333333" />
      <sap2010:ViewStateData Id="Assign_122" sap:VirtualizedContainerService.HintSize="1818.66666666667,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_123" sap:VirtualizedContainerService.HintSize="1818.66666666667,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_124" sap:VirtualizedContainerService.HintSize="1818.66666666667,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_125" sap:VirtualizedContainerService.HintSize="1818.66666666667,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_126" sap:VirtualizedContainerService.HintSize="1766,61.3333333333333" />
      <sap2010:ViewStateData Id="InvokeWorkflow_11" sap:VirtualizedContainerService.HintSize="1766,22" />
      <sap2010:ViewStateData Id="Assign_127" sap:VirtualizedContainerService.HintSize="1518,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_128" sap:VirtualizedContainerService.HintSize="1518,61.3333333333333" />
      <sap2010:ViewStateData Id="Append_Line_10" sap:VirtualizedContainerService.HintSize="286,22" />
      <sap2010:ViewStateData Id="InvokeMethod_3" sap:VirtualizedContainerService.HintSize="218,132" />
      <sap2010:ViewStateData Id="Sequence_74" sap:VirtualizedContainerService.HintSize="240,256">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_18" sap:VirtualizedContainerService.HintSize="286,407.333333333333" />
      <sap2010:ViewStateData Id="Sequence_75" sap:VirtualizedContainerService.HintSize="308,593.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_129" sap:VirtualizedContainerService.HintSize="1162,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_130" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_131" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Append_Line_11" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_132" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Sequence_76" sap:VirtualizedContainerService.HintSize="264,450">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_133" sap:VirtualizedContainerService.HintSize="828,61.3333333333333" />
      <sap2010:ViewStateData Id="InvokeWorkflow_12" sap:VirtualizedContainerService.HintSize="828,22" />
      <sap2010:ViewStateData Id="Assign_134" sap:VirtualizedContainerService.HintSize="680,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_135" sap:VirtualizedContainerService.HintSize="680,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_136" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_137" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Append_Line_12" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_138" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Sequence_77" sap:VirtualizedContainerService.HintSize="264,450">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_139" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_140" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Append_Line_13" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_141" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Sequence_78" sap:VirtualizedContainerService.HintSize="264,450">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_37" sap:VirtualizedContainerService.HintSize="554,602" />
      <sap2010:ViewStateData Id="If_38" sap:VirtualizedContainerService.HintSize="680,754" />
      <sap2010:ViewStateData Id="Sequence_79" sap:VirtualizedContainerService.HintSize="702,1080.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_39" sap:VirtualizedContainerService.HintSize="828,1232.66666666667" />
      <sap2010:ViewStateData Id="Sequence_80" sap:VirtualizedContainerService.HintSize="850,1520">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_81" sap:VirtualizedContainerService.HintSize="872,1644">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_40" sap:VirtualizedContainerService.HintSize="1162,1796" />
      <sap2010:ViewStateData Id="Sequence_82" sap:VirtualizedContainerService.HintSize="1184,2021.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_41" sap:VirtualizedContainerService.HintSize="1518,2173.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_83" sap:VirtualizedContainerService.HintSize="1540,2500">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_14" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_42" sap:VirtualizedContainerService.HintSize="1766,2652" />
      <sap2010:ViewStateData Id="Sequence_84" sap:VirtualizedContainerService.HintSize="1788,2939.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_19" sap:VirtualizedContainerService.HintSize="1818.66666666667,3090.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_72" sap:VirtualizedContainerService.HintSize="1840.66666666667,3873.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_177" sap:VirtualizedContainerService.HintSize="1304,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_178" sap:VirtualizedContainerService.HintSize="1304,61.3333333333333" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_10" sap:VirtualizedContainerService.HintSize="612,22" />
      <sap2010:ViewStateData Id="Assign_179" sap:VirtualizedContainerService.HintSize="464,61.3333333333333" />
      <sap2010:ViewStateData Id="InvokeMethod_5" sap:VirtualizedContainerService.HintSize="218,132" />
      <sap2010:ViewStateData Id="ForEach`1_22" sap:VirtualizedContainerService.HintSize="286,283.333333333333" />
      <sap2010:ViewStateData Id="Sequence_104" sap:VirtualizedContainerService.HintSize="308,407.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_54" sap:VirtualizedContainerService.HintSize="464,559.333333333333" />
      <sap2010:ViewStateData Id="Sequence_105" sap:VirtualizedContainerService.HintSize="486,784.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_55" sap:VirtualizedContainerService.HintSize="612,936.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_106" sap:VirtualizedContainerService.HintSize="634,1122.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_23" sap:VirtualizedContainerService.HintSize="1304,1274">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_221" sap:VirtualizedContainerService.HintSize="1251.33333333333,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_220" sap:VirtualizedContainerService.HintSize="1198.66666666667,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_181" sap:VirtualizedContainerService.HintSize="1198.66666666667,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_182" sap:VirtualizedContainerService.HintSize="1198.66666666667,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_183" sap:VirtualizedContainerService.HintSize="1198.66666666667,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_184" sap:VirtualizedContainerService.HintSize="1198.66666666667,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_218" sap:VirtualizedContainerService.HintSize="1198.66666666667,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_219" sap:VirtualizedContainerService.HintSize="1198.66666666667,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_185" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Sequence_107" sap:VirtualizedContainerService.HintSize="264,185.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_186" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Sequence_108" sap:VirtualizedContainerService.HintSize="264,185.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_11" sap:VirtualizedContainerService.HintSize="1198.66666666667,396.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_187" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Sequence_109" sap:VirtualizedContainerService.HintSize="264,185.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_188" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Sequence_110" sap:VirtualizedContainerService.HintSize="264,185.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_12" sap:VirtualizedContainerService.HintSize="1198.66666666667,396.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_189" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Sequence_111" sap:VirtualizedContainerService.HintSize="264,185.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_190" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Sequence_112" sap:VirtualizedContainerService.HintSize="264,185.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_13" sap:VirtualizedContainerService.HintSize="1198.66666666667,396.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeWorkflow_16" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Sequence_113" sap:VirtualizedContainerService.HintSize="222,146">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_191" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Sequence_114" sap:VirtualizedContainerService.HintSize="264,185.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_56" sap:VirtualizedContainerService.HintSize="464,337.333333333333" />
      <sap2010:ViewStateData Id="Assign_192" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Sequence_115" sap:VirtualizedContainerService.HintSize="264,185.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_57" sap:VirtualizedContainerService.HintSize="464,337.333333333333" />
      <sap2010:ViewStateData Id="Assign_193" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Sequence_116" sap:VirtualizedContainerService.HintSize="264,185.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_58" sap:VirtualizedContainerService.HintSize="464,337.333333333333" />
      <sap2010:ViewStateData Id="Assign_194" sap:VirtualizedContainerService.HintSize="464,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_195" sap:VirtualizedContainerService.HintSize="464,61.3333333333333" />
      <sap2010:ViewStateData Id="Sequence_117" sap:VirtualizedContainerService.HintSize="486,1418.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_59" sap:VirtualizedContainerService.HintSize="1198.66666666667,1570.66666666667" />
      <sap2010:ViewStateData Id="Sequence_118" sap:VirtualizedContainerService.HintSize="1220.66666666667,3714">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_24" sap:VirtualizedContainerService.HintSize="1251.33333333333,3865.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_132" sap:VirtualizedContainerService.HintSize="1273.33333333333,4090.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_25" sap:VirtualizedContainerService.HintSize="1304,4242" />
      <sap2010:ViewStateData Id="Sequence_103" sap:VirtualizedContainerService.HintSize="1326,5882.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_53" sap:VirtualizedContainerService.HintSize="1840.66666666667,6034.66666666667" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_8" sap:VirtualizedContainerService.HintSize="476,22" />
      <sap2010:ViewStateData Id="MessageBox_17" sap:VirtualizedContainerService.HintSize="1188,22" />
      <sap2010:ViewStateData Id="Assign_71" sap:VirtualizedContainerService.HintSize="1188,60" />
      <sap2010:ViewStateData Id="Assign_72" sap:VirtualizedContainerService.HintSize="1188,60" />
      <sap2010:ViewStateData Id="Assign_73" sap:VirtualizedContainerService.HintSize="1188,60" />
      <sap2010:ViewStateData Id="Assign_74" sap:VirtualizedContainerService.HintSize="1188,60" />
      <sap2010:ViewStateData Id="Assign_75" sap:VirtualizedContainerService.HintSize="1188,60" />
      <sap2010:ViewStateData Id="Assign_76" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_35" sap:VirtualizedContainerService.HintSize="264,181">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_77" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_36" sap:VirtualizedContainerService.HintSize="264,181">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_1" sap:VirtualizedContainerService.HintSize="1188,160">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="MessageBox_15" sap:VirtualizedContainerService.HintSize="1188,22" />
      <sap2010:ViewStateData Id="Assign_78" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_37" sap:VirtualizedContainerService.HintSize="264,181">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_79" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_38" sap:VirtualizedContainerService.HintSize="264,181">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_2" sap:VirtualizedContainerService.HintSize="1188,160">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_80" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_39" sap:VirtualizedContainerService.HintSize="264,184">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_81" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_40" sap:VirtualizedContainerService.HintSize="264,184">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_3" sap:VirtualizedContainerService.HintSize="1182,160">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="MessageBox_12" sap:VirtualizedContainerService.HintSize="1182,22" />
      <sap2010:ViewStateData Id="InvokeWorkflow_8" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Sequence_41" sap:VirtualizedContainerService.HintSize="222,146">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_27" sap:VirtualizedContainerService.HintSize="1182,294" />
      <sap2010:ViewStateData Id="Assign_87" sap:VirtualizedContainerService.HintSize="1182,60" />
      <sap2010:ViewStateData Id="Sequence_42" sap:VirtualizedContainerService.HintSize="1210,1804">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_5" sap:VirtualizedContainerService.HintSize="200,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_9" sap:VirtualizedContainerService.HintSize="287,208" />
      <sap2010:ViewStateData Id="Sequence_43" sap:VirtualizedContainerService.HintSize="309,332">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_4" sap:VirtualizedContainerService.HintSize="476,516">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_44" sap:VirtualizedContainerService.HintSize="498,702">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_7" sap:VirtualizedContainerService.HintSize="200,52">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="CommentOut_2" sap:VirtualizedContainerService.HintSize="1840.66666666667,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_45" sap:VirtualizedContainerService.HintSize="1862.66666666667,10268">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_71" sap:VirtualizedContainerService.HintSize="1884.66666666667,10580">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_28" sap:VirtualizedContainerService.HintSize="2110.66666666667,10732">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_46" sap:VirtualizedContainerService.HintSize="2132.66666666667,16736.6666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="2172.66666666667,16856.6666666667" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>