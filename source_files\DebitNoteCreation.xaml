﻿<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI"
 xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys"
 xmlns:iaw="clr-namespace:Infor.Activities.Workflow;assembly=Infor.Activities.Workflow"
 xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities"
 xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json"
 xmlns:s="clr-namespace:System;assembly=mscorlib"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="vendorId" Type="InArgument(x:String)" />
    <x:Property Name="ivdate" Type="InArgument(x:String)" />
    <x:Property Name="division" Type="InArgument(x:String)" />
    <x:Property Name="sino" Type="InArgument(x:String)" />
    <x:Property Name="cucd" Type="InArgument(x:String)" />
    <x:Property Name="tepy" Type="InArgument(x:String)" />
    <x:Property Name="pyme" Type="InArgument(x:String)" />
    <x:Property Name="cuam" Type="InArgument(x:String)" />
    <x:Property Name="authUser" Type="InArgument(x:String)" />
    <x:Property Name="bkid" Type="InArgument(x:String)" />
    <x:Property Name="correlationID" Type="InArgument(x:String)" />
    <x:Property Name="projectPath" Type="InArgument(x:String)" />
    <x:Property Name="M3TotalTableRows" Type="InArgument(scg:List(s:String[]))" />
    <x:Property Name="logfile" Type="InArgument(x:String)" />
    <x:Property Name="vatCodeConfig" Type="InArgument(x:String)" />
    <x:Property Name="inbnValue" Type="InArgument(x:String)" />
    <x:Property Name="ListocrLineValues" Type="InArgument(scg:List(scg:Dictionary(x:String, x:Object)))" />
    <x:Property Name="commentStatus" Type="OutArgument(x:String)" />
    <x:Property Name="Status" Type="OutArgument(x:String)" />
    <x:Property Name="DictOcrValues" Type="InArgument(scg:IDictionary(x:String, x:Object))" />
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="miscValues" Type="InArgument(scg:Dictionary(x:String, x:Object))" />
    <x:Property Name="argument1" Type="InArgument(x:String)" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Linq</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>Microsoft.VisualBasic.CompilerServices</x:String>
      <x:String>System</x:String>
      <x:String>Infor.RPA.Utilities</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>Newtonsoft.Json</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>Infor.RPA.Utilities</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_75">
    <Sequence.Variables>
      <Variable x:TypeArguments="iru:ResponseObject" Name="respObj5" />
      <Variable x:TypeArguments="x:Int32" Name="StatusCode5" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="respObj0" />
      <Variable x:TypeArguments="x:Int32" Name="StatusCode0" />
      <Variable x:TypeArguments="x:String" Name="poInvoiceResponseCode" />
      <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="poInvoiceResponseDictionary" />
      <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="AddHeadOcrWorkflowOutput" />
      <Variable x:TypeArguments="x:Int32" Name="AddHeadOcrWorkflowStatus" />
      <Variable x:TypeArguments="x:String" Name="amt" />
      <Variable x:TypeArguments="x:Int32" Name="TotalAmount" />
    </Sequence.Variables>
    <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" Title="{x:Null}" ContinueOnError="True" DisplayName="Message Box &quot;debit in xaml&quot;" sap2010:WorkflowViewState.IdRef="MessageBox_1" Selection="OK" Text="debit in xaml" />
    <Assign sap2010:WorkflowViewState.IdRef="Assign_97">
      <Assign.To>
        <OutArgument x:TypeArguments="x:Int32">[TotalAmount]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:Int32">0</InArgument>
      </Assign.Value>
    </Assign>
    <Sequence DisplayName="Get inbn details Get Head" sap2010:WorkflowViewState.IdRef="Sequence_81">
      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_123">
        <Sequence.Variables>
          <Variable x:TypeArguments="x:String" Name="vser" />
          <Variable x:TypeArguments="iru:ResponseObject" Name="GetHeadRespObj" />
          <Variable x:TypeArguments="x:Int32" Name="getVoucherStatus" />
        </Sequence.Variables>
        <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="Get the voucher satus IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_16" Response="[GetHeadRespObj]" StatusCode="[getVoucherStatus]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS200MI/GetInvTotInfo&quot;]">
          <iai:IONAPIRequestWizard.Headers>
            <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
              <scg:List x:TypeArguments="x:String" Capacity="4">
                <x:String>Accept</x:String>
              </scg:List>
              <scg:List x:TypeArguments="x:String" Capacity="4">
                <x:String>application/json</x:String>
              </scg:List>
            </scg:List>
          </iai:IONAPIRequestWizard.Headers>
          <iai:IONAPIRequestWizard.QueryParameters>
            <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
              <scg:List x:TypeArguments="x:String" Capacity="8">
                <x:String>DIVI</x:String>
                <x:String>SPYN</x:String>
                <x:String>SUNO</x:String>
                <x:String>SINO</x:String>
                <x:String>INYR</x:String>
              </scg:List>
              <scg:List x:TypeArguments="x:String" Capacity="8">
                <x:String>division</x:String>
                <x:String>vendorId</x:String>
                <x:String>vendorId</x:String>
                <x:String>sino</x:String>
                <x:String>inyr</x:String>
              </scg:List>
            </scg:List>
          </iai:IONAPIRequestWizard.QueryParameters>
        </iai:IONAPIRequestWizard>
        <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_13" Line="Get voucher details with first Inbn value" Source="[logfile]" />
        <If Condition="[getVoucherStatus = 200]" sap2010:WorkflowViewState.IdRef="If_90">
          <If.Then>
            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_122">
              <Sequence.Variables>
                <Variable x:TypeArguments="x:String" Name="vono" />
                <Variable x:TypeArguments="x:String" Name="yea4" />
                <Variable x:TypeArguments="x:String" Name="acdt" />
                <Variable x:TypeArguments="njl:JToken" Name="out1" />
              </Sequence.Variables>
              <Assign sap2010:WorkflowViewState.IdRef="Assign_117">
                <Assign.To>
                  <OutArgument x:TypeArguments="njl:JToken">[out1]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(GetHeadRespObj.ReadAsText)]</InArgument>
                </Assign.Value>
              </Assign>
              <If Condition="[out1(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_89">
                <If.Then>
                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_82">
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_118">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">[(out1("results")(0)("errorMessage")).ToString]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_14" Line="[commentStatus]" Source="[logfile]" />
                  </Sequence>
                </If.Then>
                <If.Else>
                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_121">
                    <Sequence.Variables>
                      <Variable x:TypeArguments="iru:ResponseObject" Name="AccountingvoucherResp" />
                      <Variable x:TypeArguments="njl:JToken" Name="out3" />
                      <Variable x:TypeArguments="x:String" Name="jrno" />
                    </Sequence.Variables>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_119">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[vono]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">[(out1("results")(0)("records")(0)("VONO")).ToString]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_120">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[yea4]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">[(out1("results")(0)("records")(0)("YEA4")).ToString]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_121">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[acdt]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">[(out1("results")(0)("records")(0)("ACDT")).ToString]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_122">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[vser]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">[(out1("results")(0)("records")(0)("VSER")).ToString]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_123">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[jrno]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">[(out1("results")(0)("records")(0)("JRNO")).ToString]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_124">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[vser]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">[(out1("results")(0)("records")(0)("VSER")).ToString]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="GLS200get voucher IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_17" Response="[AccountingvoucherResp]" StatusCode="[getVoucherStatus]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/GLS200MI/GetVoucherLine&quot;]">
                      <iai:IONAPIRequestWizard.Headers>
                        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                          <scg:List x:TypeArguments="x:String" Capacity="4">
                            <x:String>Accept</x:String>
                          </scg:List>
                          <scg:List x:TypeArguments="x:String" Capacity="4">
                            <x:String>application/json</x:String>
                          </scg:List>
                        </scg:List>
                      </iai:IONAPIRequestWizard.Headers>
                      <iai:IONAPIRequestWizard.QueryParameters>
                        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                          <scg:List x:TypeArguments="x:String" Capacity="8">
                            <x:String>DIVI</x:String>
                            <x:String>VONO</x:String>
                            <x:String>JSNO</x:String>
                            <x:String>JRNO</x:String>
                            <x:String>YEA4</x:String>
                            <x:String>VSER</x:String>
                          </scg:List>
                          <scg:List x:TypeArguments="x:String" Capacity="8">
                            <x:String>division</x:String>
                            <x:String>vono</x:String>
                            <x:String>2</x:String>
                            <x:String>jrno</x:String>
                            <x:String>yea4</x:String>
                            <x:String>vser</x:String>
                          </scg:List>
                        </scg:List>
                      </iai:IONAPIRequestWizard.QueryParameters>
                    </iai:IONAPIRequestWizard>
                    <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_15" Line="Get accounting lines for the validated line" Source="[logfile]" />
                    <If Condition="[getVoucherStatus = 200]" sap2010:WorkflowViewState.IdRef="If_88">
                      <If.Then>
                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_120">
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_125">
                            <Assign.To>
                              <OutArgument x:TypeArguments="njl:JToken">[out3]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(AccountingvoucherResp.ReadAsText)]</InArgument>
                            </Assign.Value>
                          </Assign>
                          <If Condition="[out3(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_87">
                            <If.Then>
                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_83">
                                <Assign sap2010:WorkflowViewState.IdRef="Assign_126">
                                  <Assign.To>
                                    <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                  </Assign.To>
                                  <Assign.Value>
                                    <InArgument x:TypeArguments="x:String">[(out3("results")(0)("errorMessage")).ToString]</InArgument>
                                  </Assign.Value>
                                </Assign>
                                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_16" Line="[commentStatus]" Source="[logfile]" />
                              </Sequence>
                            </If.Then>
                            <If.Else>
                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_119">
                                <Assign sap2010:WorkflowViewState.IdRef="Assign_127">
                                  <Assign.To>
                                    <OutArgument x:TypeArguments="x:String">[AIT1]</OutArgument>
                                  </Assign.To>
                                  <Assign.Value>
                                    <InArgument x:TypeArguments="x:String">[(out3("results")(0)("records")(0)("AIT1")).ToString]</InArgument>
                                  </Assign.Value>
                                </Assign>
                                <Assign sap2010:WorkflowViewState.IdRef="Assign_128">
                                  <Assign.To>
                                    <OutArgument x:TypeArguments="x:String">[AIT2]</OutArgument>
                                  </Assign.To>
                                  <Assign.Value>
                                    <InArgument x:TypeArguments="x:String">[(out3("results")(0)("records")(0)("AIT2")).ToString]</InArgument>
                                  </Assign.Value>
                                </Assign>
                                <Assign sap2010:WorkflowViewState.IdRef="Assign_129">
                                  <Assign.To>
                                    <OutArgument x:TypeArguments="x:String">[AIT3]</OutArgument>
                                  </Assign.To>
                                  <Assign.Value>
                                    <InArgument x:TypeArguments="x:String">[(out3("results")(0)("records")(0)("AIT3")).ToString]</InArgument>
                                  </Assign.Value>
                                </Assign>
                                <Assign sap2010:WorkflowViewState.IdRef="Assign_130">
                                  <Assign.To>
                                    <OutArgument x:TypeArguments="x:String">[AIT4]</OutArgument>
                                  </Assign.To>
                                  <Assign.Value>
                                    <InArgument x:TypeArguments="x:String">[(out3("results")(0)("records")(0)("AIT4")).ToString]</InArgument>
                                  </Assign.Value>
                                </Assign>
                                <Assign sap2010:WorkflowViewState.IdRef="Assign_131">
                                  <Assign.To>
                                    <OutArgument x:TypeArguments="x:String">[AIT5]</OutArgument>
                                  </Assign.To>
                                  <Assign.Value>
                                    <InArgument x:TypeArguments="x:String">[(out3("results")(0)("records")(0)("AIT5")).ToString]</InArgument>
                                  </Assign.Value>
                                </Assign>
                                <Assign sap2010:WorkflowViewState.IdRef="Assign_132">
                                  <Assign.To>
                                    <OutArgument x:TypeArguments="x:String">[AIT6]</OutArgument>
                                  </Assign.To>
                                  <Assign.Value>
                                    <InArgument x:TypeArguments="x:String">[(out3("results")(0)("records")(0)("AIT6")).ToString]</InArgument>
                                  </Assign.Value>
                                </Assign>
                                <Assign sap2010:WorkflowViewState.IdRef="Assign_133">
                                  <Assign.To>
                                    <OutArgument x:TypeArguments="x:String">[AIT7]</OutArgument>
                                  </Assign.To>
                                  <Assign.Value>
                                    <InArgument x:TypeArguments="x:String">[(out3("results")(0)("records")(0)("AIT7")).ToString]</InArgument>
                                  </Assign.Value>
                                </Assign>
                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_118">
                                  <Sequence.Variables>
                                    <Variable x:TypeArguments="x:Int32" Name="addHeadRecodeStatus" />
                                    <Variable x:TypeArguments="njl:JToken" Name="out2" />
                                    <Variable x:TypeArguments="iru:ResponseObject" Name="AddHeadRespObj" />
                                  </Sequence.Variables>
                                  <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="AddHeadRecode IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_18" Response="[AddHeadRespObj]" StatusCode="[addHeadRecodeStatus]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/AddHeadRecode&quot;]">
                                    <iai:IONAPIRequestWizard.Headers>
                                      <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                        <scg:List x:TypeArguments="x:String" Capacity="4">
                                          <x:String>Accept</x:String>
                                        </scg:List>
                                        <scg:List x:TypeArguments="x:String" Capacity="4">
                                          <x:String>application/json</x:String>
                                        </scg:List>
                                      </scg:List>
                                    </iai:IONAPIRequestWizard.Headers>
                                    <iai:IONAPIRequestWizard.QueryParameters>
                                      <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                        <scg:List x:TypeArguments="x:String" Capacity="8">
                                          <x:String>DIVI</x:String>
                                          <x:String>VONO</x:String>
                                          <x:String>ACDT</x:String>
                                          <x:String>IMCD</x:String>
                                          <x:String>YEA4</x:String>
                                          <x:String>VSER</x:String>
                                        </scg:List>
                                        <scg:List x:TypeArguments="x:String" Capacity="8">
                                          <x:String>division</x:String>
                                          <x:String>vono</x:String>
                                          <x:String>acdt</x:String>
                                          <x:String>0</x:String>
                                          <x:String>yea4</x:String>
                                          <x:String>vser</x:String>
                                        </scg:List>
                                      </scg:List>
                                    </iai:IONAPIRequestWizard.QueryParameters>
                                  </iai:IONAPIRequestWizard>
                                  <If Condition="[addHeadRecodeStatus = 200]" sap2010:WorkflowViewState.IdRef="If_86">
                                    <If.Then>
                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_117">
                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_134">
                                          <Assign.To>
                                            <OutArgument x:TypeArguments="njl:JToken">[out2]</OutArgument>
                                          </Assign.To>
                                          <Assign.Value>
                                            <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(AddHeadRespObj.ReadAsText)]</InArgument>
                                          </Assign.Value>
                                        </Assign>
                                        <If Condition="[out2(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_85">
                                          <If.Then>
                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_84">
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_135">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="x:String">[(out2("results")(0)("errorMessage")).ToString]</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                              <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_17" Line="[commentStatus]" Source="[logfile]" />
                                            </Sequence>
                                          </If.Then>
                                          <If.Else>
                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_116">
                                              <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_18" Line="Recoded Header Added" Source="[logfile]" />
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_136">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="x:String">[inbnValue2]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="x:String">[(out2("results")(0)("records")(0)("INBN")).ToString]</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_137">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="x:String">[amt]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="x:String">["-"+DictOcrValues("TOTAL").Tostring]</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                              <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding recode lines" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_19" Response="[respObj2]" StatusCode="[StatusCode2]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLineRecode&quot;]">
                                                <iai:IONAPIRequestWizard.Headers>
                                                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                                      <x:String>Accept</x:String>
                                                    </scg:List>
                                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                                      <x:String>application/json</x:String>
                                                    </scg:List>
                                                  </scg:List>
                                                </iai:IONAPIRequestWizard.Headers>
                                                <iai:IONAPIRequestWizard.QueryParameters>
                                                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                    <scg:List x:TypeArguments="x:String" Capacity="16">
                                                      <x:String>INBN</x:String>
                                                      <x:String>DIVI</x:String>
                                                      <x:String>NLAM</x:String>
                                                      <x:String>AIT1</x:String>
                                                      <x:String>AIT2</x:String>
                                                      <x:String>AIT3</x:String>
                                                      <x:String>AIT4</x:String>
                                                      <x:String>AIT5</x:String>
                                                      <x:String>AIT6</x:String>
                                                      <x:String>AIT7</x:String>
                                                    </scg:List>
                                                    <scg:List x:TypeArguments="x:String" Capacity="16">
                                                      <x:String>inbnValue2</x:String>
                                                      <x:String>division</x:String>
                                                      <x:String>amt</x:String>
                                                      <x:String>AIT1</x:String>
                                                      <x:String>AIT2</x:String>
                                                      <x:String>AIT3</x:String>
                                                      <x:String>AIT4</x:String>
                                                      <x:String>AIT5</x:String>
                                                      <x:String>AIT6</x:String>
                                                      <x:String>AIT7</x:String>
                                                    </scg:List>
                                                  </scg:List>
                                                </iai:IONAPIRequestWizard.QueryParameters>
                                              </iai:IONAPIRequestWizard>
                                              <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_19" Line="Recoded Line Added" Source="[logfile]" />
                                              <If Condition="[StatusCode2 = 200]" sap2010:WorkflowViewState.IdRef="If_84">
                                                <If.Then>
                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_115">
                                                    <Sequence.Variables>
                                                      <Variable x:TypeArguments="x:String" Name="acqt" />
                                                    </Sequence.Variables>
                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_138">
                                                      <Assign.To>
                                                        <OutArgument x:TypeArguments="njl:JToken">[out4]</OutArgument>
                                                      </Assign.To>
                                                      <Assign.Value>
                                                        <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj2.ReadAsText)]</InArgument>
                                                      </Assign.Value>
                                                    </Assign>
                                                    <If Condition="[out4(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_83">
                                                      <If.Then>
                                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_85">
                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_139">
                                                            <Assign.To>
                                                              <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                            </Assign.To>
                                                            <Assign.Value>
                                                              <InArgument x:TypeArguments="x:String">[(out4("results")(0)("errorMessage")).ToString]</InArgument>
                                                            </Assign.Value>
                                                          </Assign>
                                                          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_20" Line="[commentStatus]" Source="[logfile]" />
                                                        </Sequence>
                                                      </If.Then>
                                                      <If.Else>
                                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_114">
                                                          <If Condition="[GLCode = &quot;DISTRIBUTED&quot; AND ListocrLineValues.Count &gt; 0]" sap2010:WorkflowViewState.IdRef="If_80">
                                                            <If.Then>
                                                              <ForEach x:TypeArguments="scg:Dictionary(x:String, x:Object)" DisplayName="ForEach&lt;Dictionary&lt;String,Object&gt;&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_6" Values="[ListocrLineValues]">
                                                                <ActivityAction x:TypeArguments="scg:Dictionary(x:String, x:Object)">
                                                                  <ActivityAction.Argument>
                                                                    <DelegateInArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)" Name="item" />
                                                                  </ActivityAction.Argument>
                                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_88">
                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_140">
                                                                      <Assign.To>
                                                                        <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</OutArgument>
                                                                      </Assign.To>
                                                                      <Assign.Value>
                                                                        <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[New Dictionary (Of String, Object)]</InArgument>
                                                                      </Assign.Value>
                                                                    </Assign>
                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_141">
                                                                      <Assign.To>
                                                                        <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</OutArgument>
                                                                      </Assign.To>
                                                                      <Assign.Value>
                                                                        <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[item]</InArgument>
                                                                      </Assign.Value>
                                                                    </Assign>
                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_142">
                                                                      <Assign.To>
                                                                        <OutArgument x:TypeArguments="x:String">[AIT1]</OutArgument>
                                                                      </Assign.To>
                                                                      <Assign.Value>
                                                                        <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(0)]</InArgument>
                                                                      </Assign.Value>
                                                                    </Assign>
                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_143">
                                                                      <Assign.To>
                                                                        <OutArgument x:TypeArguments="x:String">[AIT2]</OutArgument>
                                                                      </Assign.To>
                                                                      <Assign.Value>
                                                                        <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(1)]</InArgument>
                                                                      </Assign.Value>
                                                                    </Assign>
                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_144">
                                                                      <Assign.To>
                                                                        <OutArgument x:TypeArguments="x:String">[AIT3]</OutArgument>
                                                                      </Assign.To>
                                                                      <Assign.Value>
                                                                        <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(2)]</InArgument>
                                                                      </Assign.Value>
                                                                    </Assign>
                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_145">
                                                                      <Assign.To>
                                                                        <OutArgument x:TypeArguments="x:String">[AIT4]</OutArgument>
                                                                      </Assign.To>
                                                                      <Assign.Value>
                                                                        <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(3)]</InArgument>
                                                                      </Assign.Value>
                                                                    </Assign>
                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_146">
                                                                      <Assign.To>
                                                                        <OutArgument x:TypeArguments="x:String">[AIT5]</OutArgument>
                                                                      </Assign.To>
                                                                      <Assign.Value>
                                                                        <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(4)]</InArgument>
                                                                      </Assign.Value>
                                                                    </Assign>
                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_147">
                                                                      <Assign.To>
                                                                        <OutArgument x:TypeArguments="x:String">[AIT6]</OutArgument>
                                                                      </Assign.To>
                                                                      <Assign.Value>
                                                                        <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(5)]</InArgument>
                                                                      </Assign.Value>
                                                                    </Assign>
                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_148">
                                                                      <Assign.To>
                                                                        <OutArgument x:TypeArguments="x:String">[AIT7]</OutArgument>
                                                                      </Assign.To>
                                                                      <Assign.Value>
                                                                        <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(6)]</InArgument>
                                                                      </Assign.Value>
                                                                    </Assign>
                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_149">
                                                                      <Assign.To>
                                                                        <OutArgument x:TypeArguments="x:String">[acqt]</OutArgument>
                                                                      </Assign.To>
                                                                      <Assign.Value>
                                                                        <InArgument x:TypeArguments="x:String">[LinesDict("QUANTITY").ToString]</InArgument>
                                                                      </Assign.Value>
                                                                    </Assign>
                                                                    <If Condition="[acqt.trim = &quot;&quot; OR acqt.trim = &quot;NA&quot;]" sap2010:WorkflowViewState.IdRef="If_55">
                                                                      <If.Then>
                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_150">
                                                                          <Assign.To>
                                                                            <OutArgument x:TypeArguments="x:String">[acqt]</OutArgument>
                                                                          </Assign.To>
                                                                          <Assign.Value>
                                                                            <InArgument x:TypeArguments="x:String">
                                                                              <Literal x:TypeArguments="x:String" Value="" />
                                                                            </InArgument>
                                                                          </Assign.Value>
                                                                        </Assign>
                                                                      </If.Then>
                                                                    </If>
                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_151">
                                                                      <Assign.To>
                                                                        <OutArgument x:TypeArguments="x:String">[amt]</OutArgument>
                                                                      </Assign.To>
                                                                      <Assign.Value>
                                                                        <InArgument x:TypeArguments="x:String">[LinesDict("LINE_AMOUNT").ToString]</InArgument>
                                                                      </Assign.Value>
                                                                    </Assign>
                                                                    <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding lines" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_20" Response="[respObj2]" StatusCode="[StatusCode2]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLineRecode&quot;]">
                                                                      <iai:IONAPIRequestWizard.Headers>
                                                                        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                          <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                            <x:String>Accept</x:String>
                                                                          </scg:List>
                                                                          <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                            <x:String>application/json</x:String>
                                                                          </scg:List>
                                                                        </scg:List>
                                                                      </iai:IONAPIRequestWizard.Headers>
                                                                      <iai:IONAPIRequestWizard.QueryParameters>
                                                                        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                          <scg:List x:TypeArguments="x:String" Capacity="16">
                                                                            <x:String>INBN</x:String>
                                                                            <x:String>RDTP</x:String>
                                                                            <x:String>DIVI</x:String>
                                                                            <x:String>NLAM</x:String>
                                                                            <x:String>AIT1</x:String>
                                                                            <x:String>AIT2</x:String>
                                                                            <x:String>AIT3</x:String>
                                                                            <x:String>AIT4</x:String>
                                                                            <x:String>AIT5</x:String>
                                                                            <x:String>AIT6</x:String>
                                                                            <x:String>AIT7</x:String>
                                                                            <x:String>ACQT</x:String>
                                                                          </scg:List>
                                                                          <scg:List x:TypeArguments="x:String" Capacity="16">
                                                                            <x:String>inbnValue2</x:String>
                                                                            <x:String>8</x:String>
                                                                            <x:String>division</x:String>
                                                                            <x:String>amt</x:String>
                                                                            <x:String>AIT1</x:String>
                                                                            <x:String>AIT2</x:String>
                                                                            <x:String>AIT3</x:String>
                                                                            <x:String>AIT4</x:String>
                                                                            <x:String>AIT5</x:String>
                                                                            <x:String>AIT6</x:String>
                                                                            <x:String>AIT7</x:String>
                                                                            <x:String>acqt</x:String>
                                                                          </scg:List>
                                                                        </scg:List>
                                                                      </iai:IONAPIRequestWizard.QueryParameters>
                                                                    </iai:IONAPIRequestWizard>
                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_152">
                                                                      <Assign.To>
                                                                        <OutArgument x:TypeArguments="njl:JToken">[out1]</OutArgument>
                                                                      </Assign.To>
                                                                      <Assign.Value>
                                                                        <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj2.ReadAsText)]</InArgument>
                                                                      </Assign.Value>
                                                                    </Assign>
                                                                    <If Condition="[out1(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_56">
                                                                      <If.Then>
                                                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_86">
                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_153">
                                                                            <Assign.To>
                                                                              <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                            </Assign.To>
                                                                            <Assign.Value>
                                                                              <InArgument x:TypeArguments="x:String" xml:space="preserve">["Error occured while adding the line.  "  +  (out1("results")(0)("errorMessage")).ToString]</InArgument>
                                                                            </Assign.Value>
                                                                          </Assign>
                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_154">
                                                                            <Assign.To>
                                                                              <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                            </Assign.To>
                                                                            <Assign.Value>
                                                                              <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                                                                            </Assign.Value>
                                                                          </Assign>
                                                                          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_21" Line="[commentStatus]" Source="[logfile]" />
                                                                        </Sequence>
                                                                      </If.Then>
                                                                      <If.Else>
                                                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_87">
                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_155">
                                                                            <Assign.To>
                                                                              <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                            </Assign.To>
                                                                            <Assign.Value>
                                                                              <InArgument x:TypeArguments="x:String">["Invoice Line created"]</InArgument>
                                                                            </Assign.Value>
                                                                          </Assign>
                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_156">
                                                                            <Assign.To>
                                                                              <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                            </Assign.To>
                                                                            <Assign.Value>
                                                                              <InArgument x:TypeArguments="x:String">["SUCCESS"]</InArgument>
                                                                            </Assign.Value>
                                                                          </Assign>
                                                                          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_22" Line="[commentStatus]" Source="[logfile]" />
                                                                        </Sequence>
                                                                      </If.Else>
                                                                    </If>
                                                                  </Sequence>
                                                                </ActivityAction>
                                                              </ForEach>
                                                            </If.Then>
                                                            <If.Else>
                                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_112">
                                                                <If Condition="[GLCode = &quot;DISTRIBUTED&quot;]" sap2010:WorkflowViewState.IdRef="If_79">
                                                                  <If.Then>
                                                                    <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_23" Line="[commentStatus]" Source="[logfile]" />
                                                                  </If.Then>
                                                                  <If.Else>
                                                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_111">
                                                                      <If Condition="[ListocrLineValues.Count&gt;0]" DisplayName="If GLCode did not obtain from excep" sap2010:WorkflowViewState.IdRef="If_74">
                                                                        <If.Then>
                                                                          <If Condition="[ListocrLineValues(0)(&quot;DESCRIPTION&quot;).Tostring.Contains(&quot;~&quot;)]" sap2010:WorkflowViewState.IdRef="If_65">
                                                                            <If.Else>
                                                                              <If Condition="[vendorId &lt;&gt; &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_64">
                                                                                <If.Then>
                                                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_96">
                                                                                    <Sequence.Variables>
                                                                                      <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="vendorResponseDictionary" />
                                                                                      <Variable x:TypeArguments="x:Int32" Name="vendorResponseCode" />
                                                                                      <Variable x:TypeArguments="x:String" Name="invoiceType" />
                                                                                    </Sequence.Variables>
                                                                                    <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;vendorID&quot;,vendorID},{&quot;company&quot;,company},{&quot;division&quot;,division}}]" ContinueOnError="True" DisplayName="SupplierInfo Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_1" OutputArguments="[vendorResponseDictionary]" ResponseCode="[vendorResponseCode]" WorkflowFile="[projectPath+&quot;\SupplierInfo.xaml&quot;]" />
                                                                                    <If Condition="[vendorResponseCode = 200]" sap2010:WorkflowViewState.IdRef="If_59">
                                                                                      <If.Then>
                                                                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_91">
                                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_157">
                                                                                            <Assign.To>
                                                                                              <OutArgument x:TypeArguments="x:String">[invoiceType]</OutArgument>
                                                                                            </Assign.To>
                                                                                            <Assign.Value>
                                                                                              <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("invoiceType"), String)]</InArgument>
                                                                                            </Assign.Value>
                                                                                          </Assign>
                                                                                          <If Condition="[invoiceType.ToLower.contains(&quot;non po&quot;) OR invoiceType.ToLower.contains(&quot;expense&quot;)]" sap2010:WorkflowViewState.IdRef="If_58">
                                                                                            <If.Then>
                                                                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_90">
                                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_158">
                                                                                                  <Assign.To>
                                                                                                    <OutArgument x:TypeArguments="scg:List(scg:Dictionary(x:String, x:Object))">[ListocrLineValues]</OutArgument>
                                                                                                  </Assign.To>
                                                                                                  <Assign.Value>
                                                                                                    <InArgument x:TypeArguments="scg:List(scg:Dictionary(x:String, x:Object))">[New List(Of Dictionary(Of String, Object))]</InArgument>
                                                                                                  </Assign.Value>
                                                                                                </Assign>
                                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_159">
                                                                                                  <Assign.To>
                                                                                                    <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</OutArgument>
                                                                                                  </Assign.To>
                                                                                                  <Assign.Value>
                                                                                                    <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[New Dictionary(Of String, Object)]</InArgument>
                                                                                                  </Assign.Value>
                                                                                                </Assign>
                                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_160">
                                                                                                  <Assign.To>
                                                                                                    <OutArgument x:TypeArguments="x:String">[GLCode]</OutArgument>
                                                                                                  </Assign.To>
                                                                                                  <Assign.Value>
                                                                                                    <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("GLCode"), String)]</InArgument>
                                                                                                  </Assign.Value>
                                                                                                </Assign>
                                                                                                <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_1">
                                                                                                  <TryCatch.Try>
                                                                                                    <If Condition="[invoiceType.ToLower.contains(&quot;non po&quot;) OR invoiceType.ToLower.contains(&quot;expense&quot;)]" sap2010:WorkflowViewState.IdRef="If_57">
                                                                                                      <If.Then>
                                                                                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_89">
                                                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_161">
                                                                                                            <Assign.To>
                                                                                                              <OutArgument x:TypeArguments="x:Object">[LinesDict("SUPPLIER_ITEM_CODE")]</OutArgument>
                                                                                                            </Assign.To>
                                                                                                            <Assign.Value>
                                                                                                              <InArgument x:TypeArguments="x:Object">[""]</InArgument>
                                                                                                            </Assign.Value>
                                                                                                          </Assign>
                                                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_162">
                                                                                                            <Assign.To>
                                                                                                              <OutArgument x:TypeArguments="x:Object">[LinesDict("Dimensions")]</OutArgument>
                                                                                                            </Assign.To>
                                                                                                            <Assign.Value>
                                                                                                              <InArgument x:TypeArguments="x:Object">[GLCode]</InArgument>
                                                                                                            </Assign.Value>
                                                                                                          </Assign>
                                                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_163">
                                                                                                            <Assign.To>
                                                                                                              <OutArgument x:TypeArguments="x:Object">[LinesDict("QUANTITY")]</OutArgument>
                                                                                                            </Assign.To>
                                                                                                            <Assign.Value>
                                                                                                              <InArgument x:TypeArguments="x:Object">[""]</InArgument>
                                                                                                            </Assign.Value>
                                                                                                          </Assign>
                                                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_164">
                                                                                                            <Assign.To>
                                                                                                              <OutArgument x:TypeArguments="x:Object">[LinesDict("UNIT_PRICE")]</OutArgument>
                                                                                                            </Assign.To>
                                                                                                            <Assign.Value>
                                                                                                              <InArgument x:TypeArguments="x:Object">[""]</InArgument>
                                                                                                            </Assign.Value>
                                                                                                          </Assign>
                                                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_165">
                                                                                                            <Assign.To>
                                                                                                              <OutArgument x:TypeArguments="x:Object">[LinesDict("LINE_AMOUNT")]</OutArgument>
                                                                                                            </Assign.To>
                                                                                                            <Assign.Value>
                                                                                                              <InArgument x:TypeArguments="x:Object">[DictOcrValues("SUBTOTAL").Tostring]</InArgument>
                                                                                                            </Assign.Value>
                                                                                                          </Assign>
                                                                                                          <InvokeMethod DisplayName="Assign to ocrValueLines InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_1" MethodName="Add">
                                                                                                            <InvokeMethod.TargetObject>
                                                                                                              <InArgument x:TypeArguments="scg:List(scg:Dictionary(x:String, x:Object))">[ListocrLineValues]</InArgument>
                                                                                                            </InvokeMethod.TargetObject>
                                                                                                            <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</InArgument>
                                                                                                          </InvokeMethod>
                                                                                                        </Sequence>
                                                                                                      </If.Then>
                                                                                                    </If>
                                                                                                  </TryCatch.Try>
                                                                                                  <TryCatch.Catches>
                                                                                                    <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_1">
                                                                                                      <ActivityAction x:TypeArguments="s:Exception">
                                                                                                        <ActivityAction.Argument>
                                                                                                          <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                                                                                                        </ActivityAction.Argument>
                                                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_166">
                                                                                                          <Assign.To>
                                                                                                            <OutArgument x:TypeArguments="x:String">[GLCode]</OutArgument>
                                                                                                          </Assign.To>
                                                                                                          <Assign.Value>
                                                                                                            <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("GLCode"), String)]</InArgument>
                                                                                                          </Assign.Value>
                                                                                                        </Assign>
                                                                                                      </ActivityAction>
                                                                                                    </Catch>
                                                                                                  </TryCatch.Catches>
                                                                                                </TryCatch>
                                                                                              </Sequence>
                                                                                            </If.Then>
                                                                                          </If>
                                                                                        </Sequence>
                                                                                      </If.Then>
                                                                                    </If>
                                                                                    <If Condition="[vendorResponseCode &lt;&gt; 200 OR GLCode = &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_63">
                                                                                      <If.Then>
                                                                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_95">
                                                                                          <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;vendorID&quot;,vendorID},{&quot;Division&quot;,division},{&quot;businessRule&quot;,miscValues(&quot;businessRuleForDivisionGLCode&quot;).ToString}}]" ContinueOnError="True" DisplayName="DivisiontoGLCode Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_2" OutputArguments="[vendorResponseDictionary]" ResponseCode="[vendorResponseCode]" WorkflowFile="[projectPath+&quot;\DivisiontoGLCode.xaml&quot;]" />
                                                                                          <If Condition="[vendorResponseCode = 200]" sap2010:WorkflowViewState.IdRef="If_62">
                                                                                            <If.Then>
                                                                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_94">
                                                                                                <Sequence.Variables>
                                                                                                  <Variable x:TypeArguments="x:String" Name="comb" />
                                                                                                </Sequence.Variables>
                                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_167">
                                                                                                  <Assign.To>
                                                                                                    <OutArgument x:TypeArguments="x:String">[comb]</OutArgument>
                                                                                                  </Assign.To>
                                                                                                  <Assign.Value>
                                                                                                    <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("comb"), String)]</InArgument>
                                                                                                  </Assign.Value>
                                                                                                </Assign>
                                                                                                <If Condition="[comb &lt;&gt; &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_61">
                                                                                                  <If.Then>
                                                                                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_93">
                                                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_168">
                                                                                                        <Assign.To>
                                                                                                          <OutArgument x:TypeArguments="x:String">[GLCode]</OutArgument>
                                                                                                        </Assign.To>
                                                                                                        <Assign.Value>
                                                                                                          <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("AIT1"), String)]</InArgument>
                                                                                                        </Assign.Value>
                                                                                                      </Assign>
                                                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_169">
                                                                                                        <Assign.To>
                                                                                                          <OutArgument x:TypeArguments="x:String">[invoiceType]</OutArgument>
                                                                                                        </Assign.To>
                                                                                                        <Assign.Value>
                                                                                                          <InArgument x:TypeArguments="x:String">Expense Invoice</InArgument>
                                                                                                        </Assign.Value>
                                                                                                      </Assign>
                                                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_170">
                                                                                                        <Assign.To>
                                                                                                          <OutArgument x:TypeArguments="scg:List(scg:Dictionary(x:String, x:Object))">[ListocrLineValues]</OutArgument>
                                                                                                        </Assign.To>
                                                                                                        <Assign.Value>
                                                                                                          <InArgument x:TypeArguments="scg:List(scg:Dictionary(x:String, x:Object))">[New List(Of Dictionary(Of String, Object))]</InArgument>
                                                                                                        </Assign.Value>
                                                                                                      </Assign>
                                                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_171">
                                                                                                        <Assign.To>
                                                                                                          <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</OutArgument>
                                                                                                        </Assign.To>
                                                                                                        <Assign.Value>
                                                                                                          <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[New Dictionary(Of String, Object)]</InArgument>
                                                                                                        </Assign.Value>
                                                                                                      </Assign>
                                                                                                      <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_2">
                                                                                                        <TryCatch.Try>
                                                                                                          <If Condition="[invoiceType.ToLower.contains(&quot;non po&quot;) OR invoiceType.ToLower.contains(&quot;expense&quot;)]" sap2010:WorkflowViewState.IdRef="If_60">
                                                                                                            <If.Then>
                                                                                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_92">
                                                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_172">
                                                                                                                  <Assign.To>
                                                                                                                    <OutArgument x:TypeArguments="x:Object">[LinesDict("SUPPLIER_ITEM_CODE")]</OutArgument>
                                                                                                                  </Assign.To>
                                                                                                                  <Assign.Value>
                                                                                                                    <InArgument x:TypeArguments="x:Object">[""]</InArgument>
                                                                                                                  </Assign.Value>
                                                                                                                </Assign>
                                                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_173">
                                                                                                                  <Assign.To>
                                                                                                                    <OutArgument x:TypeArguments="x:Object">[LinesDict("Dimensions")]</OutArgument>
                                                                                                                  </Assign.To>
                                                                                                                  <Assign.Value>
                                                                                                                    <InArgument x:TypeArguments="x:Object">[comb]</InArgument>
                                                                                                                  </Assign.Value>
                                                                                                                </Assign>
                                                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_174">
                                                                                                                  <Assign.To>
                                                                                                                    <OutArgument x:TypeArguments="x:Object">[LinesDict("QUANTITY")]</OutArgument>
                                                                                                                  </Assign.To>
                                                                                                                  <Assign.Value>
                                                                                                                    <InArgument x:TypeArguments="x:Object">[""]</InArgument>
                                                                                                                  </Assign.Value>
                                                                                                                </Assign>
                                                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_175">
                                                                                                                  <Assign.To>
                                                                                                                    <OutArgument x:TypeArguments="x:Object">[LinesDict("UNIT_PRICE")]</OutArgument>
                                                                                                                  </Assign.To>
                                                                                                                  <Assign.Value>
                                                                                                                    <InArgument x:TypeArguments="x:Object">[""]</InArgument>
                                                                                                                  </Assign.Value>
                                                                                                                </Assign>
                                                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_176">
                                                                                                                  <Assign.To>
                                                                                                                    <OutArgument x:TypeArguments="x:Object">[LinesDict("LINE_AMOUNT")]</OutArgument>
                                                                                                                  </Assign.To>
                                                                                                                  <Assign.Value>
                                                                                                                    <InArgument x:TypeArguments="x:Object">[DictOcrValues("SUBTOTAL").Tostring]</InArgument>
                                                                                                                  </Assign.Value>
                                                                                                                </Assign>
                                                                                                                <InvokeMethod DisplayName="Assign to ocrValueLines InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_2" MethodName="Add">
                                                                                                                  <InvokeMethod.TargetObject>
                                                                                                                    <InArgument x:TypeArguments="scg:List(scg:Dictionary(x:String, x:Object))">[ListocrLineValues]</InArgument>
                                                                                                                  </InvokeMethod.TargetObject>
                                                                                                                  <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</InArgument>
                                                                                                                </InvokeMethod>
                                                                                                              </Sequence>
                                                                                                            </If.Then>
                                                                                                          </If>
                                                                                                        </TryCatch.Try>
                                                                                                        <TryCatch.Catches>
                                                                                                          <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_2">
                                                                                                            <ActivityAction x:TypeArguments="s:Exception">
                                                                                                              <ActivityAction.Argument>
                                                                                                                <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                                                                                                              </ActivityAction.Argument>
                                                                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_177">
                                                                                                                <Assign.To>
                                                                                                                  <OutArgument x:TypeArguments="x:String">[GLCode]</OutArgument>
                                                                                                                </Assign.To>
                                                                                                                <Assign.Value>
                                                                                                                  <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("AIT1"), String)]</InArgument>
                                                                                                                </Assign.Value>
                                                                                                              </Assign>
                                                                                                            </ActivityAction>
                                                                                                          </Catch>
                                                                                                        </TryCatch.Catches>
                                                                                                      </TryCatch>
                                                                                                    </Sequence>
                                                                                                  </If.Then>
                                                                                                </If>
                                                                                              </Sequence>
                                                                                            </If.Then>
                                                                                          </If>
                                                                                        </Sequence>
                                                                                      </If.Then>
                                                                                    </If>
                                                                                  </Sequence>
                                                                                </If.Then>
                                                                              </If>
                                                                            </If.Else>
                                                                          </If>
                                                                        </If.Then>
                                                                        <If.Else>
                                                                          <If Condition="[vendorId &lt;&gt; &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_73">
                                                                            <If.Then>
                                                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_104">
                                                                                <Sequence.Variables>
                                                                                  <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="vendorResponseDictionary" />
                                                                                  <Variable x:TypeArguments="x:Int32" Name="vendorResponseCode" />
                                                                                  <Variable x:TypeArguments="x:String" Name="invoiceType" />
                                                                                </Sequence.Variables>
                                                                                <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;vendorID&quot;,vendorID},{&quot;company&quot;,company},{&quot;division&quot;,division}}]" ContinueOnError="True" DisplayName="SupplierInfo Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_3" OutputArguments="[vendorResponseDictionary]" ResponseCode="[vendorResponseCode]" WorkflowFile="[projectPath+&quot;\SupplierInfo.xaml&quot;]" />
                                                                                <If Condition="[vendorResponseCode = 200]" sap2010:WorkflowViewState.IdRef="If_68">
                                                                                  <If.Then>
                                                                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_99">
                                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_178">
                                                                                        <Assign.To>
                                                                                          <OutArgument x:TypeArguments="x:String">[invoiceType]</OutArgument>
                                                                                        </Assign.To>
                                                                                        <Assign.Value>
                                                                                          <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("invoiceType"), String)]</InArgument>
                                                                                        </Assign.Value>
                                                                                      </Assign>
                                                                                      <If Condition="[invoiceType.ToLower.contains(&quot;non po&quot;) OR invoiceType.ToLower.contains(&quot;expense&quot;)]" sap2010:WorkflowViewState.IdRef="If_67">
                                                                                        <If.Then>
                                                                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_98">
                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_179">
                                                                                              <Assign.To>
                                                                                                <OutArgument x:TypeArguments="scg:List(scg:Dictionary(x:String, x:Object))">[ListocrLineValues]</OutArgument>
                                                                                              </Assign.To>
                                                                                              <Assign.Value>
                                                                                                <InArgument x:TypeArguments="scg:List(scg:Dictionary(x:String, x:Object))">[New List(Of Dictionary(Of String, Object))]</InArgument>
                                                                                              </Assign.Value>
                                                                                            </Assign>
                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_180">
                                                                                              <Assign.To>
                                                                                                <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</OutArgument>
                                                                                              </Assign.To>
                                                                                              <Assign.Value>
                                                                                                <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[New Dictionary(Of String, Object)]</InArgument>
                                                                                              </Assign.Value>
                                                                                            </Assign>
                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_181">
                                                                                              <Assign.To>
                                                                                                <OutArgument x:TypeArguments="x:String">[GLCode]</OutArgument>
                                                                                              </Assign.To>
                                                                                              <Assign.Value>
                                                                                                <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("GLCode"), String)]</InArgument>
                                                                                              </Assign.Value>
                                                                                            </Assign>
                                                                                            <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_3">
                                                                                              <TryCatch.Try>
                                                                                                <If Condition="[invoiceType.ToLower.contains(&quot;non po&quot;) OR invoiceType.ToLower.contains(&quot;expense&quot;)]" sap2010:WorkflowViewState.IdRef="If_66">
                                                                                                  <If.Then>
                                                                                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_97">
                                                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_182">
                                                                                                        <Assign.To>
                                                                                                          <OutArgument x:TypeArguments="x:Object">[LinesDict("SUPPLIER_ITEM_CODE")]</OutArgument>
                                                                                                        </Assign.To>
                                                                                                        <Assign.Value>
                                                                                                          <InArgument x:TypeArguments="x:Object">[""]</InArgument>
                                                                                                        </Assign.Value>
                                                                                                      </Assign>
                                                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_183">
                                                                                                        <Assign.To>
                                                                                                          <OutArgument x:TypeArguments="x:Object">[LinesDict("Dimensions")]</OutArgument>
                                                                                                        </Assign.To>
                                                                                                        <Assign.Value>
                                                                                                          <InArgument x:TypeArguments="x:Object">[GLCode]</InArgument>
                                                                                                        </Assign.Value>
                                                                                                      </Assign>
                                                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_184">
                                                                                                        <Assign.To>
                                                                                                          <OutArgument x:TypeArguments="x:Object">[LinesDict("QUANTITY")]</OutArgument>
                                                                                                        </Assign.To>
                                                                                                        <Assign.Value>
                                                                                                          <InArgument x:TypeArguments="x:Object">[""]</InArgument>
                                                                                                        </Assign.Value>
                                                                                                      </Assign>
                                                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_185">
                                                                                                        <Assign.To>
                                                                                                          <OutArgument x:TypeArguments="x:Object">[LinesDict("UNIT_PRICE")]</OutArgument>
                                                                                                        </Assign.To>
                                                                                                        <Assign.Value>
                                                                                                          <InArgument x:TypeArguments="x:Object">[""]</InArgument>
                                                                                                        </Assign.Value>
                                                                                                      </Assign>
                                                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_186">
                                                                                                        <Assign.To>
                                                                                                          <OutArgument x:TypeArguments="x:Object">[LinesDict("LINE_AMOUNT")]</OutArgument>
                                                                                                        </Assign.To>
                                                                                                        <Assign.Value>
                                                                                                          <InArgument x:TypeArguments="x:Object">[DictOcrValues("SUBTOTAL").Tostring]</InArgument>
                                                                                                        </Assign.Value>
                                                                                                      </Assign>
                                                                                                      <InvokeMethod DisplayName="Assign to ocrValueLines InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_3" MethodName="Add">
                                                                                                        <InvokeMethod.TargetObject>
                                                                                                          <InArgument x:TypeArguments="scg:List(scg:Dictionary(x:String, x:Object))">[ListocrLineValues]</InArgument>
                                                                                                        </InvokeMethod.TargetObject>
                                                                                                        <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</InArgument>
                                                                                                      </InvokeMethod>
                                                                                                    </Sequence>
                                                                                                  </If.Then>
                                                                                                </If>
                                                                                              </TryCatch.Try>
                                                                                              <TryCatch.Catches>
                                                                                                <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_3">
                                                                                                  <ActivityAction x:TypeArguments="s:Exception">
                                                                                                    <ActivityAction.Argument>
                                                                                                      <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                                                                                                    </ActivityAction.Argument>
                                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_187">
                                                                                                      <Assign.To>
                                                                                                        <OutArgument x:TypeArguments="x:String">[GLCode]</OutArgument>
                                                                                                      </Assign.To>
                                                                                                      <Assign.Value>
                                                                                                        <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("GLCode"), String)]</InArgument>
                                                                                                      </Assign.Value>
                                                                                                    </Assign>
                                                                                                  </ActivityAction>
                                                                                                </Catch>
                                                                                              </TryCatch.Catches>
                                                                                            </TryCatch>
                                                                                          </Sequence>
                                                                                        </If.Then>
                                                                                      </If>
                                                                                    </Sequence>
                                                                                  </If.Then>
                                                                                </If>
                                                                                <If Condition="[vendorResponseCode &lt;&gt; 200 OR NOT ListOcrLineValues.Count &gt; 0]" sap2010:WorkflowViewState.IdRef="If_72">
                                                                                  <If.Then>
                                                                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_103">
                                                                                      <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;vendorID&quot;,vendorID},{&quot;Division&quot;,division},{&quot;businessRule&quot;,miscValues(&quot;businessRuleForDivisionGLCode&quot;).ToString}}]" ContinueOnError="True" DisplayName="DivisiontoGLCode Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_4" OutputArguments="[vendorResponseDictionary]" ResponseCode="[vendorResponseCode]" WorkflowFile="[projectPath+&quot;\DivisiontoGLCode.xaml&quot;]" />
                                                                                      <If Condition="[vendorResponseCode = 200]" sap2010:WorkflowViewState.IdRef="If_71">
                                                                                        <If.Then>
                                                                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_102">
                                                                                            <Sequence.Variables>
                                                                                              <Variable x:TypeArguments="x:String" Name="comb" />
                                                                                            </Sequence.Variables>
                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_188">
                                                                                              <Assign.To>
                                                                                                <OutArgument x:TypeArguments="x:String">[comb]</OutArgument>
                                                                                              </Assign.To>
                                                                                              <Assign.Value>
                                                                                                <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("comb"), String)]</InArgument>
                                                                                              </Assign.Value>
                                                                                            </Assign>
                                                                                            <If Condition="[comb &lt;&gt; &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_70">
                                                                                              <If.Then>
                                                                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_101">
                                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_189">
                                                                                                    <Assign.To>
                                                                                                      <OutArgument x:TypeArguments="x:String">[GLCode]</OutArgument>
                                                                                                    </Assign.To>
                                                                                                    <Assign.Value>
                                                                                                      <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("AIT1"), String)]</InArgument>
                                                                                                    </Assign.Value>
                                                                                                  </Assign>
                                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_190">
                                                                                                    <Assign.To>
                                                                                                      <OutArgument x:TypeArguments="x:String">[invoiceType]</OutArgument>
                                                                                                    </Assign.To>
                                                                                                    <Assign.Value>
                                                                                                      <InArgument x:TypeArguments="x:String">Expense Invoice</InArgument>
                                                                                                    </Assign.Value>
                                                                                                  </Assign>
                                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_191">
                                                                                                    <Assign.To>
                                                                                                      <OutArgument x:TypeArguments="scg:List(scg:Dictionary(x:String, x:Object))">[ListocrLineValues]</OutArgument>
                                                                                                    </Assign.To>
                                                                                                    <Assign.Value>
                                                                                                      <InArgument x:TypeArguments="scg:List(scg:Dictionary(x:String, x:Object))">[New List(Of Dictionary(Of String, Object))]</InArgument>
                                                                                                    </Assign.Value>
                                                                                                  </Assign>
                                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_192">
                                                                                                    <Assign.To>
                                                                                                      <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</OutArgument>
                                                                                                    </Assign.To>
                                                                                                    <Assign.Value>
                                                                                                      <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[New Dictionary(Of String, Object)]</InArgument>
                                                                                                    </Assign.Value>
                                                                                                  </Assign>
                                                                                                  <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_4">
                                                                                                    <TryCatch.Try>
                                                                                                      <If Condition="[invoiceType.ToLower.contains(&quot;non po&quot;) OR invoiceType.ToLower.contains(&quot;expense&quot;)]" sap2010:WorkflowViewState.IdRef="If_69">
                                                                                                        <If.Then>
                                                                                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_100">
                                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_193">
                                                                                                              <Assign.To>
                                                                                                                <OutArgument x:TypeArguments="x:Object">[LinesDict("SUPPLIER_ITEM_CODE")]</OutArgument>
                                                                                                              </Assign.To>
                                                                                                              <Assign.Value>
                                                                                                                <InArgument x:TypeArguments="x:Object">[""]</InArgument>
                                                                                                              </Assign.Value>
                                                                                                            </Assign>
                                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_194">
                                                                                                              <Assign.To>
                                                                                                                <OutArgument x:TypeArguments="x:Object">[LinesDict("Dimensions")]</OutArgument>
                                                                                                              </Assign.To>
                                                                                                              <Assign.Value>
                                                                                                                <InArgument x:TypeArguments="x:Object">[comb]</InArgument>
                                                                                                              </Assign.Value>
                                                                                                            </Assign>
                                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_195">
                                                                                                              <Assign.To>
                                                                                                                <OutArgument x:TypeArguments="x:Object">[LinesDict("QUANTITY")]</OutArgument>
                                                                                                              </Assign.To>
                                                                                                              <Assign.Value>
                                                                                                                <InArgument x:TypeArguments="x:Object">[""]</InArgument>
                                                                                                              </Assign.Value>
                                                                                                            </Assign>
                                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_196">
                                                                                                              <Assign.To>
                                                                                                                <OutArgument x:TypeArguments="x:Object">[LinesDict("UNIT_PRICE")]</OutArgument>
                                                                                                              </Assign.To>
                                                                                                              <Assign.Value>
                                                                                                                <InArgument x:TypeArguments="x:Object">[""]</InArgument>
                                                                                                              </Assign.Value>
                                                                                                            </Assign>
                                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_197">
                                                                                                              <Assign.To>
                                                                                                                <OutArgument x:TypeArguments="x:Object">[LinesDict("LINE_AMOUNT")]</OutArgument>
                                                                                                              </Assign.To>
                                                                                                              <Assign.Value>
                                                                                                                <InArgument x:TypeArguments="x:Object">[DictOcrValues("SUBTOTAL").Tostring]</InArgument>
                                                                                                              </Assign.Value>
                                                                                                            </Assign>
                                                                                                            <InvokeMethod DisplayName="Assign to ocrValueLines InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_4" MethodName="Add">
                                                                                                              <InvokeMethod.TargetObject>
                                                                                                                <InArgument x:TypeArguments="scg:List(scg:Dictionary(x:String, x:Object))">[ListocrLineValues]</InArgument>
                                                                                                              </InvokeMethod.TargetObject>
                                                                                                              <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</InArgument>
                                                                                                            </InvokeMethod>
                                                                                                          </Sequence>
                                                                                                        </If.Then>
                                                                                                      </If>
                                                                                                    </TryCatch.Try>
                                                                                                    <TryCatch.Catches>
                                                                                                      <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_4">
                                                                                                        <ActivityAction x:TypeArguments="s:Exception">
                                                                                                          <ActivityAction.Argument>
                                                                                                            <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                                                                                                          </ActivityAction.Argument>
                                                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_198">
                                                                                                            <Assign.To>
                                                                                                              <OutArgument x:TypeArguments="x:String">[GLCode]</OutArgument>
                                                                                                            </Assign.To>
                                                                                                            <Assign.Value>
                                                                                                              <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("AIT1"), String)]</InArgument>
                                                                                                            </Assign.Value>
                                                                                                          </Assign>
                                                                                                        </ActivityAction>
                                                                                                      </Catch>
                                                                                                    </TryCatch.Catches>
                                                                                                  </TryCatch>
                                                                                                </Sequence>
                                                                                              </If.Then>
                                                                                            </If>
                                                                                          </Sequence>
                                                                                        </If.Then>
                                                                                      </If>
                                                                                    </Sequence>
                                                                                  </If.Then>
                                                                                </If>
                                                                              </Sequence>
                                                                            </If.Then>
                                                                          </If>
                                                                        </If.Else>
                                                                      </If>
                                                                      <If Condition="[ListocrLineValues.Count &gt; 0]" sap2010:WorkflowViewState.IdRef="If_78">
                                                                        <If.Then>
                                                                          <ForEach x:TypeArguments="scg:Dictionary(x:String, x:Object)" DisplayName="ForEach&lt;Dictionary&lt;String,Object&gt;&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_7" Values="[ListocrLineValues]">
                                                                            <ActivityAction x:TypeArguments="scg:Dictionary(x:String, x:Object)">
                                                                              <ActivityAction.Argument>
                                                                                <DelegateInArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)" Name="item" />
                                                                              </ActivityAction.Argument>
                                                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_107">
                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_199">
                                                                                  <Assign.To>
                                                                                    <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</OutArgument>
                                                                                  </Assign.To>
                                                                                  <Assign.Value>
                                                                                    <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[New Dictionary (Of String, Object)]</InArgument>
                                                                                  </Assign.Value>
                                                                                </Assign>
                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_200">
                                                                                  <Assign.To>
                                                                                    <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</OutArgument>
                                                                                  </Assign.To>
                                                                                  <Assign.Value>
                                                                                    <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[item]</InArgument>
                                                                                  </Assign.Value>
                                                                                </Assign>
                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_201">
                                                                                  <Assign.To>
                                                                                    <OutArgument x:TypeArguments="x:String">[AIT1]</OutArgument>
                                                                                  </Assign.To>
                                                                                  <Assign.Value>
                                                                                    <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(0)]</InArgument>
                                                                                  </Assign.Value>
                                                                                </Assign>
                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_202">
                                                                                  <Assign.To>
                                                                                    <OutArgument x:TypeArguments="x:String">[AIT2]</OutArgument>
                                                                                  </Assign.To>
                                                                                  <Assign.Value>
                                                                                    <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(1)]</InArgument>
                                                                                  </Assign.Value>
                                                                                </Assign>
                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_203">
                                                                                  <Assign.To>
                                                                                    <OutArgument x:TypeArguments="x:String">[AIT3]</OutArgument>
                                                                                  </Assign.To>
                                                                                  <Assign.Value>
                                                                                    <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(2)]</InArgument>
                                                                                  </Assign.Value>
                                                                                </Assign>
                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_204">
                                                                                  <Assign.To>
                                                                                    <OutArgument x:TypeArguments="x:String">[AIT4]</OutArgument>
                                                                                  </Assign.To>
                                                                                  <Assign.Value>
                                                                                    <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(3)]</InArgument>
                                                                                  </Assign.Value>
                                                                                </Assign>
                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_205">
                                                                                  <Assign.To>
                                                                                    <OutArgument x:TypeArguments="x:String">[AIT5]</OutArgument>
                                                                                  </Assign.To>
                                                                                  <Assign.Value>
                                                                                    <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(4)]</InArgument>
                                                                                  </Assign.Value>
                                                                                </Assign>
                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_206">
                                                                                  <Assign.To>
                                                                                    <OutArgument x:TypeArguments="x:String">[AIT6]</OutArgument>
                                                                                  </Assign.To>
                                                                                  <Assign.Value>
                                                                                    <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(5)]</InArgument>
                                                                                  </Assign.Value>
                                                                                </Assign>
                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_207">
                                                                                  <Assign.To>
                                                                                    <OutArgument x:TypeArguments="x:String">[AIT7]</OutArgument>
                                                                                  </Assign.To>
                                                                                  <Assign.Value>
                                                                                    <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(6)]</InArgument>
                                                                                  </Assign.Value>
                                                                                </Assign>
                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_208">
                                                                                  <Assign.To>
                                                                                    <OutArgument x:TypeArguments="x:String">[acqt]</OutArgument>
                                                                                  </Assign.To>
                                                                                  <Assign.Value>
                                                                                    <InArgument x:TypeArguments="x:String">[LinesDict("QUANTITY").ToString]</InArgument>
                                                                                  </Assign.Value>
                                                                                </Assign>
                                                                                <If Condition="[acqt.trim = &quot;&quot; OR acqt.trim = &quot;NA&quot;]" sap2010:WorkflowViewState.IdRef="If_75">
                                                                                  <If.Then>
                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_209">
                                                                                      <Assign.To>
                                                                                        <OutArgument x:TypeArguments="x:String">[acqt]</OutArgument>
                                                                                      </Assign.To>
                                                                                      <Assign.Value>
                                                                                        <InArgument x:TypeArguments="x:String">
                                                                                          <Literal x:TypeArguments="x:String" Value="" />
                                                                                        </InArgument>
                                                                                      </Assign.Value>
                                                                                    </Assign>
                                                                                  </If.Then>
                                                                                </If>
                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_210">
                                                                                  <Assign.To>
                                                                                    <OutArgument x:TypeArguments="x:String">[amt]</OutArgument>
                                                                                  </Assign.To>
                                                                                  <Assign.Value>
                                                                                    <InArgument x:TypeArguments="x:String">[LinesDict("LINE_AMOUNT").ToString]</InArgument>
                                                                                  </Assign.Value>
                                                                                </Assign>
                                                                                <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding lines" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_21" Response="[respObj2]" StatusCode="[StatusCode2]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLineRecode&quot;]">
                                                                                  <iai:IONAPIRequestWizard.Headers>
                                                                                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                      <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                        <x:String>Accept</x:String>
                                                                                      </scg:List>
                                                                                      <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                        <x:String>application/json</x:String>
                                                                                      </scg:List>
                                                                                    </scg:List>
                                                                                  </iai:IONAPIRequestWizard.Headers>
                                                                                  <iai:IONAPIRequestWizard.QueryParameters>
                                                                                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                      <scg:List x:TypeArguments="x:String" Capacity="16">
                                                                                        <x:String>INBN</x:String>
                                                                                        <x:String>RDTP</x:String>
                                                                                        <x:String>DIVI</x:String>
                                                                                        <x:String>NLAM</x:String>
                                                                                        <x:String>AIT1</x:String>
                                                                                        <x:String>AIT2</x:String>
                                                                                        <x:String>AIT3</x:String>
                                                                                        <x:String>AIT4</x:String>
                                                                                        <x:String>AIT5</x:String>
                                                                                        <x:String>AIT6</x:String>
                                                                                        <x:String>AIT7</x:String>
                                                                                        <x:String>ACQT</x:String>
                                                                                      </scg:List>
                                                                                      <scg:List x:TypeArguments="x:String" Capacity="16">
                                                                                        <x:String>inbnValue2</x:String>
                                                                                        <x:String>8</x:String>
                                                                                        <x:String>division</x:String>
                                                                                        <x:String>amt</x:String>
                                                                                        <x:String>AIT1</x:String>
                                                                                        <x:String>AIT2</x:String>
                                                                                        <x:String>AIT3</x:String>
                                                                                        <x:String>AIT4</x:String>
                                                                                        <x:String>AIT5</x:String>
                                                                                        <x:String>AIT6</x:String>
                                                                                        <x:String>AIT7</x:String>
                                                                                        <x:String>acqt</x:String>
                                                                                      </scg:List>
                                                                                    </scg:List>
                                                                                  </iai:IONAPIRequestWizard.QueryParameters>
                                                                                </iai:IONAPIRequestWizard>
                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_211">
                                                                                  <Assign.To>
                                                                                    <OutArgument x:TypeArguments="njl:JToken">[out1]</OutArgument>
                                                                                  </Assign.To>
                                                                                  <Assign.Value>
                                                                                    <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj2.ReadAsText)]</InArgument>
                                                                                  </Assign.Value>
                                                                                </Assign>
                                                                                <If Condition="[out1(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_76">
                                                                                  <If.Then>
                                                                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_105">
                                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_212">
                                                                                        <Assign.To>
                                                                                          <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                        </Assign.To>
                                                                                        <Assign.Value>
                                                                                          <InArgument x:TypeArguments="x:String" xml:space="preserve">["Error occured while adding the line.  "  +  (out1("results")(0)("errorMessage")).ToString]</InArgument>
                                                                                        </Assign.Value>
                                                                                      </Assign>
                                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_213">
                                                                                        <Assign.To>
                                                                                          <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                                        </Assign.To>
                                                                                        <Assign.Value>
                                                                                          <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                                                                                        </Assign.Value>
                                                                                      </Assign>
                                                                                      <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_24" Line="[commentStatus]" Source="[logfile]" />
                                                                                    </Sequence>
                                                                                  </If.Then>
                                                                                  <If.Else>
                                                                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_106">
                                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_214">
                                                                                        <Assign.To>
                                                                                          <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                        </Assign.To>
                                                                                        <Assign.Value>
                                                                                          <InArgument x:TypeArguments="x:String">["Invoice Line created"]</InArgument>
                                                                                        </Assign.Value>
                                                                                      </Assign>
                                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_215">
                                                                                        <Assign.To>
                                                                                          <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                                        </Assign.To>
                                                                                        <Assign.Value>
                                                                                          <InArgument x:TypeArguments="x:String">["SUCCESS"]</InArgument>
                                                                                        </Assign.Value>
                                                                                      </Assign>
                                                                                      <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_25" Line="[commentStatus]" Source="[logfile]" />
                                                                                    </Sequence>
                                                                                  </If.Else>
                                                                                </If>
                                                                              </Sequence>
                                                                            </ActivityAction>
                                                                          </ForEach>
                                                                        </If.Then>
                                                                        <If.Else>
                                                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_110">
                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_216">
                                                                              <Assign.To>
                                                                                <OutArgument x:TypeArguments="x:String">[AIT1]</OutArgument>
                                                                              </Assign.To>
                                                                              <Assign.Value>
                                                                                <InArgument x:TypeArguments="x:String">[GLCode]</InArgument>
                                                                              </Assign.Value>
                                                                            </Assign>
                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_217">
                                                                              <Assign.To>
                                                                                <OutArgument x:TypeArguments="x:String">[AIT2]</OutArgument>
                                                                              </Assign.To>
                                                                              <Assign.Value>
                                                                                <InArgument x:TypeArguments="x:String">
                                                                                  <Literal x:TypeArguments="x:String" Value="" />
                                                                                </InArgument>
                                                                              </Assign.Value>
                                                                            </Assign>
                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_218">
                                                                              <Assign.To>
                                                                                <OutArgument x:TypeArguments="x:String">[AIT3]</OutArgument>
                                                                              </Assign.To>
                                                                              <Assign.Value>
                                                                                <InArgument x:TypeArguments="x:String">
                                                                                  <Literal x:TypeArguments="x:String" Value="" />
                                                                                </InArgument>
                                                                              </Assign.Value>
                                                                            </Assign>
                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_219">
                                                                              <Assign.To>
                                                                                <OutArgument x:TypeArguments="x:String">[AIT4]</OutArgument>
                                                                              </Assign.To>
                                                                              <Assign.Value>
                                                                                <InArgument x:TypeArguments="x:String">
                                                                                  <Literal x:TypeArguments="x:String" Value="" />
                                                                                </InArgument>
                                                                              </Assign.Value>
                                                                            </Assign>
                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_220">
                                                                              <Assign.To>
                                                                                <OutArgument x:TypeArguments="x:String">[AIT5]</OutArgument>
                                                                              </Assign.To>
                                                                              <Assign.Value>
                                                                                <InArgument x:TypeArguments="x:String">
                                                                                  <Literal x:TypeArguments="x:String" Value="" />
                                                                                </InArgument>
                                                                              </Assign.Value>
                                                                            </Assign>
                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_221">
                                                                              <Assign.To>
                                                                                <OutArgument x:TypeArguments="x:String">[AIT6]</OutArgument>
                                                                              </Assign.To>
                                                                              <Assign.Value>
                                                                                <InArgument x:TypeArguments="x:String">
                                                                                  <Literal x:TypeArguments="x:String" Value="" />
                                                                                </InArgument>
                                                                              </Assign.Value>
                                                                            </Assign>
                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_222">
                                                                              <Assign.To>
                                                                                <OutArgument x:TypeArguments="x:String">[AIT7]</OutArgument>
                                                                              </Assign.To>
                                                                              <Assign.Value>
                                                                                <InArgument x:TypeArguments="x:String">
                                                                                  <Literal x:TypeArguments="x:String" Value="" />
                                                                                </InArgument>
                                                                              </Assign.Value>
                                                                            </Assign>
                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_223">
                                                                              <Assign.To>
                                                                                <OutArgument x:TypeArguments="x:String">[amt]</OutArgument>
                                                                              </Assign.To>
                                                                              <Assign.Value>
                                                                                <InArgument x:TypeArguments="x:String">[LinesDict("UNIT_PRICE").ToString]</InArgument>
                                                                              </Assign.Value>
                                                                            </Assign>
                                                                            <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding lines" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_22" Response="[respObj2]" StatusCode="[StatusCode2]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                                                                              <iai:IONAPIRequestWizard.Headers>
                                                                                <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                  <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                    <x:String>Accept</x:String>
                                                                                  </scg:List>
                                                                                  <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                    <x:String>application/json</x:String>
                                                                                  </scg:List>
                                                                                </scg:List>
                                                                              </iai:IONAPIRequestWizard.Headers>
                                                                              <iai:IONAPIRequestWizard.QueryParameters>
                                                                                <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                  <scg:List x:TypeArguments="x:String" Capacity="16">
                                                                                    <x:String>INBN</x:String>
                                                                                    <x:String>RDTP</x:String>
                                                                                    <x:String>DIVI</x:String>
                                                                                    <x:String>NLAM</x:String>
                                                                                    <x:String>AIT1</x:String>
                                                                                    <x:String>AIT2</x:String>
                                                                                    <x:String>AIT3</x:String>
                                                                                    <x:String>AIT4</x:String>
                                                                                    <x:String>AIT5</x:String>
                                                                                    <x:String>AIT6</x:String>
                                                                                    <x:String>AIT7</x:String>
                                                                                  </scg:List>
                                                                                  <scg:List x:TypeArguments="x:String" Capacity="16">
                                                                                    <x:String>inbnValue</x:String>
                                                                                    <x:String>8</x:String>
                                                                                    <x:String>division</x:String>
                                                                                    <x:String>amt</x:String>
                                                                                    <x:String>AIT1</x:String>
                                                                                    <x:String>AIT2</x:String>
                                                                                    <x:String>AIT3</x:String>
                                                                                    <x:String>AIT4</x:String>
                                                                                    <x:String>AIT5</x:String>
                                                                                    <x:String>AIT6</x:String>
                                                                                    <x:String>AIT7</x:String>
                                                                                  </scg:List>
                                                                                </scg:List>
                                                                              </iai:IONAPIRequestWizard.QueryParameters>
                                                                            </iai:IONAPIRequestWizard>
                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_224">
                                                                              <Assign.To>
                                                                                <OutArgument x:TypeArguments="njl:JToken">[out1]</OutArgument>
                                                                              </Assign.To>
                                                                              <Assign.Value>
                                                                                <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj2.ReadAsText)]</InArgument>
                                                                              </Assign.Value>
                                                                            </Assign>
                                                                            <If Condition="[out1(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_77">
                                                                              <If.Then>
                                                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_108">
                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_225">
                                                                                    <Assign.To>
                                                                                      <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                    </Assign.To>
                                                                                    <Assign.Value>
                                                                                      <InArgument x:TypeArguments="x:String" xml:space="preserve">["Error occured while adding the line.  "  +  (out1("results")(0)("errorMessage")).ToString]</InArgument>
                                                                                    </Assign.Value>
                                                                                  </Assign>
                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_226">
                                                                                    <Assign.To>
                                                                                      <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                                    </Assign.To>
                                                                                    <Assign.Value>
                                                                                      <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                                                                                    </Assign.Value>
                                                                                  </Assign>
                                                                                  <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_26" Line="[commentStatus]" Source="[logfile]" />
                                                                                </Sequence>
                                                                              </If.Then>
                                                                              <If.Else>
                                                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_109">
                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_227">
                                                                                    <Assign.To>
                                                                                      <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                    </Assign.To>
                                                                                    <Assign.Value>
                                                                                      <InArgument x:TypeArguments="x:String">["Invoice Line created"]</InArgument>
                                                                                    </Assign.Value>
                                                                                  </Assign>
                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_228">
                                                                                    <Assign.To>
                                                                                      <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                                    </Assign.To>
                                                                                    <Assign.Value>
                                                                                      <InArgument x:TypeArguments="x:String">["SUCCESS"]</InArgument>
                                                                                    </Assign.Value>
                                                                                  </Assign>
                                                                                  <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_27" Line="[commentStatus]" Source="[logfile]" />
                                                                                </Sequence>
                                                                              </If.Else>
                                                                            </If>
                                                                          </Sequence>
                                                                        </If.Else>
                                                                      </If>
                                                                    </Sequence>
                                                                  </If.Else>
                                                                </If>
                                                              </Sequence>
                                                            </If.Else>
                                                          </If>
                                                          <If Condition="[CInt(DictOcrValues(&quot;VAT_AMOUNT&quot;).Tostring) &lt;&gt; 0]" sap2010:WorkflowViewState.IdRef="If_82">
                                                            <If.Then>
                                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_113">
                                                                <Sequence.Variables>
                                                                  <Variable x:TypeArguments="x:String" Name="vat" />
                                                                  <Variable x:TypeArguments="iru:ResponseObject" Name="respout1" />
                                                                  <Variable x:TypeArguments="x:String" Name="vatCode" />
                                                                </Sequence.Variables>
                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_229">
                                                                  <Assign.To>
                                                                    <OutArgument x:TypeArguments="x:String">[vat]</OutArgument>
                                                                  </Assign.To>
                                                                  <Assign.Value>
                                                                    <InArgument x:TypeArguments="x:String">[DictOcrValues("VAT_AMOUNT").Tostring]</InArgument>
                                                                  </Assign.Value>
                                                                </Assign>
                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_230">
                                                                  <Assign.To>
                                                                    <OutArgument x:TypeArguments="x:String">[vatCode]</OutArgument>
                                                                  </Assign.To>
                                                                  <Assign.Value>
                                                                    <InArgument x:TypeArguments="x:String">[miscValues("vatCodeConfig").ToString]</InArgument>
                                                                  </Assign.Value>
                                                                </Assign>
                                                                <If Condition="[miscValues(&quot;vatCodeConfig&quot;).ToString = &quot;NA&quot;]" sap2010:WorkflowViewState.IdRef="If_81">
                                                                  <If.Then>
                                                                    <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding VAT" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_23" Response="[respout1]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                                                                      <iai:IONAPIRequestWizard.Headers>
                                                                        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                          <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                            <x:String>Accept</x:String>
                                                                          </scg:List>
                                                                          <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                            <x:String>application/json</x:String>
                                                                          </scg:List>
                                                                        </scg:List>
                                                                      </iai:IONAPIRequestWizard.Headers>
                                                                      <iai:IONAPIRequestWizard.QueryParameters>
                                                                        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                          <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                            <x:String>INBN</x:String>
                                                                            <x:String>RDTP</x:String>
                                                                            <x:String>DIVI</x:String>
                                                                            <x:String>GLAM</x:String>
                                                                          </scg:List>
                                                                          <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                            <x:String>inbnValue2</x:String>
                                                                            <x:String>3</x:String>
                                                                            <x:String>division</x:String>
                                                                            <x:String>vat</x:String>
                                                                          </scg:List>
                                                                        </scg:List>
                                                                      </iai:IONAPIRequestWizard.QueryParameters>
                                                                    </iai:IONAPIRequestWizard>
                                                                  </If.Then>
                                                                  <If.Else>
                                                                    <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding VAT" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_24" Response="[respout1]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                                                                      <iai:IONAPIRequestWizard.Headers>
                                                                        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                          <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                            <x:String>Accept</x:String>
                                                                          </scg:List>
                                                                          <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                            <x:String>application/json</x:String>
                                                                          </scg:List>
                                                                        </scg:List>
                                                                      </iai:IONAPIRequestWizard.Headers>
                                                                      <iai:IONAPIRequestWizard.QueryParameters>
                                                                        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                          <scg:List x:TypeArguments="x:String" Capacity="8">
                                                                            <x:String>INBN</x:String>
                                                                            <x:String>RDTP</x:String>
                                                                            <x:String>DIVI</x:String>
                                                                            <x:String>VTA1</x:String>
                                                                            <x:String>VTCD</x:String>
                                                                          </scg:List>
                                                                          <scg:List x:TypeArguments="x:String" Capacity="8">
                                                                            <x:String>inbnValue2</x:String>
                                                                            <x:String>3</x:String>
                                                                            <x:String>division</x:String>
                                                                            <x:String>vat</x:String>
                                                                            <x:String>vatCode</x:String>
                                                                          </scg:List>
                                                                        </scg:List>
                                                                      </iai:IONAPIRequestWizard.QueryParameters>
                                                                    </iai:IONAPIRequestWizard>
                                                                  </If.Else>
                                                                </If>
                                                              </Sequence>
                                                            </If.Then>
                                                          </If>
                                                        </Sequence>
                                                      </If.Else>
                                                    </If>
                                                  </Sequence>
                                                </If.Then>
                                              </If>
                                            </Sequence>
                                          </If.Else>
                                        </If>
                                      </Sequence>
                                    </If.Then>
                                  </If>
                                </Sequence>
                              </Sequence>
                            </If.Else>
                          </If>
                        </Sequence>
                      </If.Then>
                    </If>
                  </Sequence>
                </If.Else>
              </If>
            </Sequence>
          </If.Then>
        </If>
      </Sequence>
    </Sequence>
    <Sequence DisplayName="Adding Lines" sap2010:WorkflowViewState.IdRef="Sequence_74">
      <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" Title="{x:Null}" ContinueOnError="True" DisplayName="ListocrLineValues" sap2010:WorkflowViewState.IdRef="MessageBox_5" Selection="OK" Text="[ListocrLineValues.Count.tostring]" />
      <ForEach x:TypeArguments="scg:Dictionary(x:String, x:Object)" DisplayName="ForEach&lt;Dictionary&lt;String,Object&gt;&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_4" Values="[ListocrLineValues]">
        <ActivityAction x:TypeArguments="scg:Dictionary(x:String, x:Object)">
          <ActivityAction.Argument>
            <DelegateInArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)" Name="item" />
          </ActivityAction.Argument>
          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_73">
            <Sequence.Variables>
              <Variable x:TypeArguments="iru:ResponseObject" Name="respObj7" />
              <Variable x:TypeArguments="x:Int32" Name="StatusCode7" />
              <Variable x:TypeArguments="njl:JToken" Name="out7" />
            </Sequence.Variables>
            <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Message Box" sap2010:WorkflowViewState.IdRef="MessageBox_9" Selection="OK" Text="[item(&quot;DESCRIPTION&quot;).ToString]" Title="DESCRIPTION" />
            <If Condition="[item(&quot;DESCRIPTION&quot;).ToString.Contains(&quot;~&quot;)]" sap2010:WorkflowViewState.IdRef="If_52">
              <If.Then>
                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_62">
                  <Sequence.Variables>
                    <Variable x:TypeArguments="scg:Dictionary(x:String, x:Object)" Name="LinesDict" />
                    <Variable x:TypeArguments="x:String" Name="AIT1" />
                    <Variable x:TypeArguments="x:String" Name="AIT2" />
                    <Variable x:TypeArguments="x:String" Name="AIT3" />
                    <Variable x:TypeArguments="x:String" Name="AIT4" />
                    <Variable x:TypeArguments="x:String" Name="AIT5" />
                    <Variable x:TypeArguments="x:String" Name="AIT6" />
                    <Variable x:TypeArguments="x:String" Name="AIT7" />
                    <Variable x:TypeArguments="iru:ResponseObject" Name="respObj2" />
                    <Variable x:TypeArguments="x:Int32" Name="StatusCode2" />
                    <Variable x:TypeArguments="njl:JToken" Name="out1" />
                  </Sequence.Variables>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_52">
                    <Assign.To>
                      <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[New Dictionary (Of String, Object)]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_53">
                    <Assign.To>
                      <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[item]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_54">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[AIT1]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(0)]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_55">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[AIT2]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(1)]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_56">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[AIT3]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(2)]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_57">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[AIT4]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(3)]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_58">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[AIT5]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(4)]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_59">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[AIT6]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(5)]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_60">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[AIT7]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(6)]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_98">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[amt]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[LinesDict("LINE_AMOUNT").ToString]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" Title="{x:Null}" ContinueOnError="True" DisplayName="Message Box" sap2010:WorkflowViewState.IdRef="MessageBox_3" Selection="OK" Text="[amt]" />
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_61">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:Int32">[TotalAmount]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:Int32">[CInt(amt)+CInt(LinesDict("LINE_AMOUNT").ToString)]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding lines" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_11" Response="[respObj2]" StatusCode="[StatusCode2]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                    <iai:IONAPIRequestWizard.Headers>
                      <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                        <scg:List x:TypeArguments="x:String" Capacity="4">
                          <x:String>Accept</x:String>
                        </scg:List>
                        <scg:List x:TypeArguments="x:String" Capacity="4">
                          <x:String>application/json</x:String>
                        </scg:List>
                      </scg:List>
                    </iai:IONAPIRequestWizard.Headers>
                    <iai:IONAPIRequestWizard.QueryParameters>
                      <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                        <scg:List x:TypeArguments="x:String" Capacity="16">
                          <x:String>INBN</x:String>
                          <x:String>RDTP</x:String>
                          <x:String>DIVI</x:String>
                          <x:String>NLAM</x:String>
                          <x:String>AIT1</x:String>
                          <x:String>AIT2</x:String>
                          <x:String>AIT3</x:String>
                          <x:String>AIT4</x:String>
                          <x:String>AIT5</x:String>
                          <x:String>AIT6</x:String>
                          <x:String>AIT7</x:String>
                        </scg:List>
                        <scg:List x:TypeArguments="x:String" Capacity="16">
                          <x:String>inbnValue</x:String>
                          <x:String>8</x:String>
                          <x:String>division</x:String>
                          <x:String>amt</x:String>
                          <x:String>AIT1</x:String>
                          <x:String>AIT2</x:String>
                          <x:String>AIT3</x:String>
                          <x:String>AIT4</x:String>
                          <x:String>AIT5</x:String>
                          <x:String>AIT6</x:String>
                          <x:String>AIT7</x:String>
                        </scg:List>
                      </scg:List>
                    </iai:IONAPIRequestWizard.QueryParameters>
                  </iai:IONAPIRequestWizard>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_62">
                    <Assign.To>
                      <OutArgument x:TypeArguments="njl:JToken">[out1]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj2.ReadAsText)]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <If Condition="[out1(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_45">
                    <If.Then>
                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_60">
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_63">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String" xml:space="preserve">["Error occured while adding the line.  "  +  (out1("results")(0)("errorMessage")).ToString]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_64">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                          </Assign.Value>
                        </Assign>
                        <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_6" Line="[commentStatus]" Source="[logfile]" />
                      </Sequence>
                    </If.Then>
                    <If.Else>
                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_61">
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_65">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">["Invoice Line created"]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_66">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">["SUCCESS"]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_7" Line="[commentStatus]" Source="[logfile]" />
                      </Sequence>
                    </If.Else>
                  </If>
                </Sequence>
              </If.Then>
            </If>
          </Sequence>
        </ActivityAction>
      </ForEach>
    </Sequence>
    <Sequence DisplayName="Adding Header" sap2010:WorkflowViewState.IdRef="Sequence_59">
      <Sequence.Variables>
        <Variable x:TypeArguments="x:String" Name="InvoiceNumber_DR" />
        <Variable x:TypeArguments="x:String" Name="Amount_DR" />
      </Sequence.Variables>
      <Assign sap2010:WorkflowViewState.IdRef="Assign_99">
        <Assign.To>
          <OutArgument x:TypeArguments="x:String">[InvoiceNumber_DR]</OutArgument>
        </Assign.To>
        <Assign.Value>
          <InArgument x:TypeArguments="x:String">[sino+"_DR"]</InArgument>
        </Assign.Value>
      </Assign>
      <Assign sap2010:WorkflowViewState.IdRef="Assign_100">
        <Assign.To>
          <OutArgument x:TypeArguments="x:String">[Amount_DR]</OutArgument>
        </Assign.To>
        <Assign.Value>
          <InArgument x:TypeArguments="x:String">["-"+TotalAmount.ToString]</InArgument>
        </Assign.Value>
      </Assign>
      <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Invoice header" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_9" Response="[respObj5]" StatusCode="[StatusCode5]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/AddHead&quot;]">
        <iai:IONAPIRequestWizard.Headers>
          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
            <scg:List x:TypeArguments="x:String" Capacity="4">
              <x:String>Accept</x:String>
            </scg:List>
            <scg:List x:TypeArguments="x:String" Capacity="4">
              <x:String>application/json</x:String>
            </scg:List>
          </scg:List>
        </iai:IONAPIRequestWizard.Headers>
        <iai:IONAPIRequestWizard.QueryParameters>
          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
            <scg:List x:TypeArguments="x:String" Capacity="32">
              <x:String>SUNO</x:String>
              <x:String>IVDT</x:String>
              <x:String>DIVI</x:String>
              <x:String>DINO</x:String>
              <x:String>CUCD</x:String>
              <x:String>TEPY</x:String>
              <x:String>PYME</x:String>
              <x:String>CUAM</x:String>
              <x:String>IMCD</x:String>
              <x:String>CRTP</x:String>
              <x:String>dateformat</x:String>
              <x:String>excludeempty</x:String>
              <x:String>righttrim</x:String>
              <x:String>format</x:String>
              <x:String>extendedresult</x:String>
              <x:String>APCD</x:String>
              <x:String>CORI</x:String>
            </scg:List>
            <scg:List x:TypeArguments="x:String" Capacity="32">
              <x:String>vendorId</x:String>
              <x:String>ivdate</x:String>
              <x:String>division</x:String>
              <x:String>InvoiceNumber_DR</x:String>
              <x:String>cucd</x:String>
              <x:String>tepy</x:String>
              <x:String>pyme</x:String>
              <x:String>Amount_DR</x:String>
              <x:String>1</x:String>
              <x:String>1</x:String>
              <x:String>YMD8</x:String>
              <x:String>false</x:String>
              <x:String>true</x:String>
              <x:String>PRETTY</x:String>
              <x:String>false</x:String>
              <x:String>authUser</x:String>
              <x:String>correlationID</x:String>
            </scg:List>
          </scg:List>
        </iai:IONAPIRequestWizard.QueryParameters>
      </iai:IONAPIRequestWizard>
      <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Message Box" sap2010:WorkflowViewState.IdRef="MessageBox_6" Selection="OK" Text="[StatusCode5.ToString]" Title="StatusCode5" />
      <If Condition="[StatusCode5 = 200  and respObj5.ReadAsText.Contains(&quot;Bank account identity must be entered&quot;)]" sap2010:WorkflowViewState.IdRef="If_44">
        <If.Then>
          <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Invoice header" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_10" Response="[respObj5]" StatusCode="[StatusCode5]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/AddHead&quot;]">
            <iai:IONAPIRequestWizard.Headers>
              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>Accept</x:String>
                </scg:List>
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>application/json</x:String>
                </scg:List>
              </scg:List>
            </iai:IONAPIRequestWizard.Headers>
            <iai:IONAPIRequestWizard.QueryParameters>
              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                <scg:List x:TypeArguments="x:String" Capacity="32">
                  <x:String>SUNO</x:String>
                  <x:String>IVDT</x:String>
                  <x:String>DIVI</x:String>
                  <x:String>SINO</x:String>
                  <x:String>CUCD</x:String>
                  <x:String>TEPY</x:String>
                  <x:String>PYME</x:String>
                  <x:String>CUAM</x:String>
                  <x:String>IMCD</x:String>
                  <x:String>CRTP</x:String>
                  <x:String>dateformat</x:String>
                  <x:String>excludeempty</x:String>
                  <x:String>righttrim</x:String>
                  <x:String>format</x:String>
                  <x:String>extendedresult</x:String>
                  <x:String>APCD</x:String>
                  <x:String>BKID</x:String>
                  <x:String>CORI</x:String>
                  <x:String>DINO</x:String>
                </scg:List>
                <scg:List x:TypeArguments="x:String" Capacity="32">
                  <x:String>vendorId</x:String>
                  <x:String>ivdate</x:String>
                  <x:String>division</x:String>
                  <x:String>sino</x:String>
                  <x:String>cucd</x:String>
                  <x:String>tepy</x:String>
                  <x:String>pyme</x:String>
                  <x:String>Amount_DR</x:String>
                  <x:String>1</x:String>
                  <x:String>1</x:String>
                  <x:String>YMD8</x:String>
                  <x:String>false</x:String>
                  <x:String>true</x:String>
                  <x:String>PRETTY</x:String>
                  <x:String>false</x:String>
                  <x:String>authUser</x:String>
                  <x:String>bkid</x:String>
                  <x:String>correlationID</x:String>
                  <x:String>InvoiceNumber_DR</x:String>
                </scg:List>
              </scg:List>
            </iai:IONAPIRequestWizard.QueryParameters>
          </iai:IONAPIRequestWizard>
        </If.Then>
      </If>
      <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Message Box" sap2010:WorkflowViewState.IdRef="MessageBox_7" Selection="OK" Text="[StatusCode5.ToString]" Title="StatusCode5" />
    </Sequence>
    <Sequence DisplayName="Adding Lines wth -ve amount" sap2010:WorkflowViewState.IdRef="Sequence_80">
      <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" Title="{x:Null}" ContinueOnError="True" DisplayName="ListocrLineValues" sap2010:WorkflowViewState.IdRef="MessageBox_8" Selection="OK" Text="[ListocrLineValues.Count.tostring]" />
      <ForEach x:TypeArguments="scg:Dictionary(x:String, x:Object)" DisplayName="ForEach&lt;Dictionary&lt;String,Object&gt;&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_5" Values="[ListocrLineValues]">
        <ActivityAction x:TypeArguments="scg:Dictionary(x:String, x:Object)">
          <ActivityAction.Argument>
            <DelegateInArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)" Name="item" />
          </ActivityAction.Argument>
          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_79">
            <Sequence.Variables>
              <Variable x:TypeArguments="iru:ResponseObject" Name="respObj7" />
              <Variable x:TypeArguments="x:Int32" Name="StatusCode7" />
              <Variable x:TypeArguments="njl:JToken" Name="out7" />
            </Sequence.Variables>
            <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Message Box" sap2010:WorkflowViewState.IdRef="MessageBox_10" Selection="OK" Text="[item(&quot;DESCRIPTION&quot;).ToString]" Title="DESCRIPTION" />
            <If Condition="[item(&quot;DESCRIPTION&quot;).ToString.Contains(&quot;~&quot;)]" sap2010:WorkflowViewState.IdRef="If_54">
              <If.Then>
                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_78">
                  <Sequence.Variables>
                    <Variable x:TypeArguments="scg:Dictionary(x:String, x:Object)" Name="LinesDict" />
                    <Variable x:TypeArguments="x:String" Name="AIT1" />
                    <Variable x:TypeArguments="x:String" Name="AIT2" />
                    <Variable x:TypeArguments="x:String" Name="AIT3" />
                    <Variable x:TypeArguments="x:String" Name="AIT4" />
                    <Variable x:TypeArguments="x:String" Name="AIT5" />
                    <Variable x:TypeArguments="x:String" Name="AIT6" />
                    <Variable x:TypeArguments="x:String" Name="AIT7" />
                    <Variable x:TypeArguments="iru:ResponseObject" Name="respObj2" />
                    <Variable x:TypeArguments="x:Int32" Name="StatusCode2" />
                    <Variable x:TypeArguments="njl:JToken" Name="out1" />
                  </Sequence.Variables>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_101">
                    <Assign.To>
                      <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[New Dictionary (Of String, Object)]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_102">
                    <Assign.To>
                      <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[item]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_103">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[AIT1]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(0)]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_104">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[AIT2]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(1)]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_105">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[AIT3]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(2)]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_106">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[AIT4]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(3)]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_107">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[AIT5]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(4)]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_108">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[AIT6]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(5)]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_109">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[AIT7]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(6)]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_110">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[amt]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">["-"+LinesDict("LINE_AMOUNT").ToString]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" Title="{x:Null}" ContinueOnError="True" DisplayName="Message Box" sap2010:WorkflowViewState.IdRef="MessageBox_4" Selection="OK" Text="[amt]" />
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_111">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:Int32">[TotalAmount]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:Int32">[CInt(amt)+CInt(LinesDict("LINE_AMOUNT").ToString)]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding lines" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_15" Response="[respObj2]" StatusCode="[StatusCode2]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                    <iai:IONAPIRequestWizard.Headers>
                      <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                        <scg:List x:TypeArguments="x:String" Capacity="4">
                          <x:String>Accept</x:String>
                        </scg:List>
                        <scg:List x:TypeArguments="x:String" Capacity="4">
                          <x:String>application/json</x:String>
                        </scg:List>
                      </scg:List>
                    </iai:IONAPIRequestWizard.Headers>
                    <iai:IONAPIRequestWizard.QueryParameters>
                      <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                        <scg:List x:TypeArguments="x:String" Capacity="16">
                          <x:String>INBN</x:String>
                          <x:String>RDTP</x:String>
                          <x:String>DIVI</x:String>
                          <x:String>NLAM</x:String>
                          <x:String>AIT1</x:String>
                          <x:String>AIT2</x:String>
                          <x:String>AIT3</x:String>
                          <x:String>AIT4</x:String>
                          <x:String>AIT5</x:String>
                          <x:String>AIT6</x:String>
                          <x:String>AIT7</x:String>
                        </scg:List>
                        <scg:List x:TypeArguments="x:String" Capacity="16">
                          <x:String>inbnValue</x:String>
                          <x:String>8</x:String>
                          <x:String>division</x:String>
                          <x:String>amt</x:String>
                          <x:String>AIT1</x:String>
                          <x:String>AIT2</x:String>
                          <x:String>AIT3</x:String>
                          <x:String>AIT4</x:String>
                          <x:String>AIT5</x:String>
                          <x:String>AIT6</x:String>
                          <x:String>AIT7</x:String>
                        </scg:List>
                      </scg:List>
                    </iai:IONAPIRequestWizard.QueryParameters>
                  </iai:IONAPIRequestWizard>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_112">
                    <Assign.To>
                      <OutArgument x:TypeArguments="njl:JToken">[out1]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj2.ReadAsText)]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <If Condition="[out1(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_53">
                    <If.Then>
                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_76">
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_113">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String" xml:space="preserve">["Error occured while adding the line.  "  +  (out1("results")(0)("errorMessage")).ToString]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_114">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                          </Assign.Value>
                        </Assign>
                        <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_11" Line="[commentStatus]" Source="[logfile]" />
                      </Sequence>
                    </If.Then>
                    <If.Else>
                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_77">
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_115">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">["Invoice Line created"]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_116">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">["SUCCESS"]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_12" Line="[commentStatus]" Source="[logfile]" />
                      </Sequence>
                    </If.Else>
                  </If>
                </Sequence>
              </If.Then>
            </If>
          </Sequence>
        </ActivityAction>
      </ForEach>
    </Sequence>
    <sads:DebugSymbol.Symbol>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</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="MessageBox_1" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_97" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_16" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Append_Line_13" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Assign_117" sap:VirtualizedContainerService.HintSize="490,62" />
      <sap2010:ViewStateData Id="Assign_118" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_14" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_82" sap:VirtualizedContainerService.HintSize="264,248">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_119" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Assign_120" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Assign_121" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Assign_122" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Assign_123" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Assign_124" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_17" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Append_Line_15" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Assign_125" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_126" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_16" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_83" sap:VirtualizedContainerService.HintSize="264,248">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_127" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_128" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_129" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_130" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_131" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_132" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_133" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_18" sap:VirtualizedContainerService.HintSize="1256,22" />
      <sap2010:ViewStateData Id="Assign_134" sap:VirtualizedContainerService.HintSize="1108,62" />
      <sap2010:ViewStateData Id="Assign_135" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_17" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_84" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_18" sap:VirtualizedContainerService.HintSize="860,22" />
      <sap2010:ViewStateData Id="Assign_136" sap:VirtualizedContainerService.HintSize="860,62" />
      <sap2010:ViewStateData Id="Assign_137" sap:VirtualizedContainerService.HintSize="860,62" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_19" sap:VirtualizedContainerService.HintSize="860,22" />
      <sap2010:ViewStateData Id="Append_Line_19" sap:VirtualizedContainerService.HintSize="860,22" />
      <sap2010:ViewStateData Id="Assign_138" sap:VirtualizedContainerService.HintSize="712,62" />
      <sap2010:ViewStateData Id="Assign_139" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_20" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_85" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_140" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_141" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_142" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_143" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_144" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_145" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_146" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_147" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_148" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_149" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_150" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_55" sap:VirtualizedContainerService.HintSize="554,216" />
      <sap2010:ViewStateData Id="Assign_151" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_20" sap:VirtualizedContainerService.HintSize="554,22" />
      <sap2010:ViewStateData Id="Assign_152" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_153" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_154" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_21" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_86" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_155" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_156" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_22" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_87" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_56" sap:VirtualizedContainerService.HintSize="554,504" />
      <sap2010:ViewStateData Id="Sequence_88" sap:VirtualizedContainerService.HintSize="576,2170">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_6" sap:VirtualizedContainerService.HintSize="200,52.6666666666667" />
      <sap2010:ViewStateData Id="Append_Line_23" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="InvokeWorkflow_1" sap:VirtualizedContainerService.HintSize="926.666666666667,22" />
      <sap2010:ViewStateData Id="Assign_157" sap:VirtualizedContainerService.HintSize="630.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_158" sap:VirtualizedContainerService.HintSize="482.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_159" sap:VirtualizedContainerService.HintSize="482.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_160" sap:VirtualizedContainerService.HintSize="482.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_161" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_162" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_163" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_164" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_165" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="InvokeMethod_1" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="Sequence_89" sap:VirtualizedContainerService.HintSize="264,768">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_57" sap:VirtualizedContainerService.HintSize="464,922">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_166" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Catch`1_1" sap:VirtualizedContainerService.HintSize="468.666666666667,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_1" sap:VirtualizedContainerService.HintSize="482.666666666667,1160" />
      <sap2010:ViewStateData Id="Sequence_90" sap:VirtualizedContainerService.HintSize="504.666666666667,1590">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_58" sap:VirtualizedContainerService.HintSize="630.666666666667,1744">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_91" sap:VirtualizedContainerService.HintSize="652.666666666667,1970">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_59" sap:VirtualizedContainerService.HintSize="926.666666666667,2124" />
      <sap2010:ViewStateData Id="InvokeWorkflow_2" sap:VirtualizedContainerService.HintSize="778.666666666667,22" />
      <sap2010:ViewStateData Id="Assign_167" sap:VirtualizedContainerService.HintSize="630.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_168" sap:VirtualizedContainerService.HintSize="482.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_169" sap:VirtualizedContainerService.HintSize="482.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_170" sap:VirtualizedContainerService.HintSize="482.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_171" sap:VirtualizedContainerService.HintSize="482.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_172" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_173" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_174" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_175" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_176" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="InvokeMethod_2" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="Sequence_92" sap:VirtualizedContainerService.HintSize="264,768">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_60" sap:VirtualizedContainerService.HintSize="464,922">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_177" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Catch`1_2" sap:VirtualizedContainerService.HintSize="468.666666666667,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_2" sap:VirtualizedContainerService.HintSize="482.666666666667,1160">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_93" sap:VirtualizedContainerService.HintSize="504.666666666667,1692">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_61" sap:VirtualizedContainerService.HintSize="630.666666666667,1846" />
      <sap2010:ViewStateData Id="Sequence_94" sap:VirtualizedContainerService.HintSize="652.666666666667,2072">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_62" sap:VirtualizedContainerService.HintSize="778.666666666667,2226">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_95" sap:VirtualizedContainerService.HintSize="800.666666666667,2412">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_63" sap:VirtualizedContainerService.HintSize="926.666666666667,2566" />
      <sap2010:ViewStateData Id="Sequence_96" sap:VirtualizedContainerService.HintSize="948.666666666667,4916">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_64" sap:VirtualizedContainerService.HintSize="1074.66666666667,5070" />
      <sap2010:ViewStateData Id="If_65" sap:VirtualizedContainerService.HintSize="1200.66666666667,5224" />
      <sap2010:ViewStateData Id="InvokeWorkflow_3" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Assign_178" sap:VirtualizedContainerService.HintSize="630.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_179" sap:VirtualizedContainerService.HintSize="482.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_180" sap:VirtualizedContainerService.HintSize="482.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_181" sap:VirtualizedContainerService.HintSize="482.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_182" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_183" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_184" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_185" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_186" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="InvokeMethod_3" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="Sequence_97" sap:VirtualizedContainerService.HintSize="264,768">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_66" sap:VirtualizedContainerService.HintSize="464,922">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_187" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Catch`1_3" sap:VirtualizedContainerService.HintSize="468.666666666667,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_3" sap:VirtualizedContainerService.HintSize="482.666666666667,1160" />
      <sap2010:ViewStateData Id="Sequence_98" sap:VirtualizedContainerService.HintSize="504.666666666667,1590">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_67" sap:VirtualizedContainerService.HintSize="630.666666666667,1744">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_99" sap:VirtualizedContainerService.HintSize="652.666666666667,1970">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_68" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeWorkflow_4" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Assign_188" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_189" sap:VirtualizedContainerService.HintSize="482.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_190" sap:VirtualizedContainerService.HintSize="482.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_191" sap:VirtualizedContainerService.HintSize="482.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_192" sap:VirtualizedContainerService.HintSize="482.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_193" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_194" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_195" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_196" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_197" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="InvokeMethod_4" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="Sequence_100" sap:VirtualizedContainerService.HintSize="264,768">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_69" sap:VirtualizedContainerService.HintSize="464,922">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_198" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Catch`1_4" sap:VirtualizedContainerService.HintSize="468.666666666667,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_4" sap:VirtualizedContainerService.HintSize="482.666666666667,1160">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_101" sap:VirtualizedContainerService.HintSize="504.666666666667,1692">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_70" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_102" sap:VirtualizedContainerService.HintSize="264,278.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_71" sap:VirtualizedContainerService.HintSize="464,432.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_103" sap:VirtualizedContainerService.HintSize="486,618.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_72" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_104" sap:VirtualizedContainerService.HintSize="222,331.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_73" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_74" sap:VirtualizedContainerService.HintSize="2301.33333333333,5378">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_199" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_200" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_201" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_202" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_203" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_204" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_205" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_206" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_207" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_208" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_209" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_75" sap:VirtualizedContainerService.HintSize="554,216" />
      <sap2010:ViewStateData Id="Assign_210" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_21" sap:VirtualizedContainerService.HintSize="554,22" />
      <sap2010:ViewStateData Id="Assign_211" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_212" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_213" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_24" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_105" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_214" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_215" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_25" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_106" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_76" sap:VirtualizedContainerService.HintSize="554,504" />
      <sap2010:ViewStateData Id="Sequence_107" sap:VirtualizedContainerService.HintSize="576,2170">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_7" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_216" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_217" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_218" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_219" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_220" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_221" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_222" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_223" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_22" sap:VirtualizedContainerService.HintSize="554,22" />
      <sap2010:ViewStateData Id="Assign_224" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_225" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_226" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_26" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_108" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_227" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_228" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_27" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_109" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_77" sap:VirtualizedContainerService.HintSize="554,504" />
      <sap2010:ViewStateData Id="Sequence_110" sap:VirtualizedContainerService.HintSize="576,1608">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_78" sap:VirtualizedContainerService.HintSize="2301.33333333333,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_111" sap:VirtualizedContainerService.HintSize="2323.33333333333,5594.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_79" sap:VirtualizedContainerService.HintSize="2549.33333333333,5748.66666666667" />
      <sap2010:ViewStateData Id="Sequence_112" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_80" sap:VirtualizedContainerService.HintSize="464,214">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_229" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Assign_230" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_23" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_24" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_81" sap:VirtualizedContainerService.HintSize="464,214" />
      <sap2010:ViewStateData Id="Sequence_113" sap:VirtualizedContainerService.HintSize="486,542">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_82" sap:VirtualizedContainerService.HintSize="464,52.6666666666667" />
      <sap2010:ViewStateData Id="Sequence_114" sap:VirtualizedContainerService.HintSize="486,430.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_83" sap:VirtualizedContainerService.HintSize="712,584.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_115" sap:VirtualizedContainerService.HintSize="734,810.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_84" sap:VirtualizedContainerService.HintSize="860,964.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_116" sap:VirtualizedContainerService.HintSize="882,1478.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_85" sap:VirtualizedContainerService.HintSize="1108,1632.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_117" sap:VirtualizedContainerService.HintSize="1130,1858.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_86" sap:VirtualizedContainerService.HintSize="1256,2012.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_118" sap:VirtualizedContainerService.HintSize="1278,2198.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_119" sap:VirtualizedContainerService.HintSize="264,890.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_87" sap:VirtualizedContainerService.HintSize="554,1044.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_120" sap:VirtualizedContainerService.HintSize="576,1270.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_88" sap:VirtualizedContainerService.HintSize="464,214">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_121" sap:VirtualizedContainerService.HintSize="486,1074">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_89" sap:VirtualizedContainerService.HintSize="490,402">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_122" sap:VirtualizedContainerService.HintSize="512,628">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_90" sap:VirtualizedContainerService.HintSize="464,214">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_123" sap:VirtualizedContainerService.HintSize="486,462">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_81" sap:VirtualizedContainerService.HintSize="222,176.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="MessageBox_5" sap:VirtualizedContainerService.HintSize="754.666666666667,22" />
      <sap2010:ViewStateData Id="MessageBox_9" sap:VirtualizedContainerService.HintSize="702,22" />
      <sap2010:ViewStateData Id="Assign_52" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_53" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_54" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_55" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_56" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_57" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_58" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_59" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_60" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_98" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="MessageBox_3" sap:VirtualizedContainerService.HintSize="554,22" />
      <sap2010:ViewStateData Id="Assign_61" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_11" sap:VirtualizedContainerService.HintSize="554,22" />
      <sap2010:ViewStateData Id="Assign_62" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_63" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_64" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_6" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_60" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_65" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_66" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_7" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_61" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_45" sap:VirtualizedContainerService.HintSize="554,504">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_62" sap:VirtualizedContainerService.HintSize="576,1976">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_52" sap:VirtualizedContainerService.HintSize="702,2130">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_73" sap:VirtualizedContainerService.HintSize="724,2316">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_4" sap:VirtualizedContainerService.HintSize="754.666666666667,2468.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_74" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_99" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Assign_100" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_9" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="MessageBox_6" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_10" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_44" sap:VirtualizedContainerService.HintSize="464,214">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="MessageBox_7" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Sequence_59" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="MessageBox_8" sap:VirtualizedContainerService.HintSize="754,22" />
      <sap2010:ViewStateData Id="MessageBox_10" sap:VirtualizedContainerService.HintSize="701.333333333333,22" />
      <sap2010:ViewStateData Id="Assign_101" sap:VirtualizedContainerService.HintSize="553.333333333333,62" />
      <sap2010:ViewStateData Id="Assign_102" sap:VirtualizedContainerService.HintSize="553.333333333333,62" />
      <sap2010:ViewStateData Id="Assign_103" sap:VirtualizedContainerService.HintSize="553.333333333333,62" />
      <sap2010:ViewStateData Id="Assign_104" sap:VirtualizedContainerService.HintSize="553,60" />
      <sap2010:ViewStateData Id="Assign_105" sap:VirtualizedContainerService.HintSize="553,60" />
      <sap2010:ViewStateData Id="Assign_106" sap:VirtualizedContainerService.HintSize="553,60" />
      <sap2010:ViewStateData Id="Assign_107" sap:VirtualizedContainerService.HintSize="553,60" />
      <sap2010:ViewStateData Id="Assign_108" sap:VirtualizedContainerService.HintSize="553,60" />
      <sap2010:ViewStateData Id="Assign_109" sap:VirtualizedContainerService.HintSize="553,60" />
      <sap2010:ViewStateData Id="Assign_110" sap:VirtualizedContainerService.HintSize="553,60" />
      <sap2010:ViewStateData Id="MessageBox_4" sap:VirtualizedContainerService.HintSize="553,22" />
      <sap2010:ViewStateData Id="Assign_111" sap:VirtualizedContainerService.HintSize="553,60" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_15" sap:VirtualizedContainerService.HintSize="553,22" />
      <sap2010:ViewStateData Id="Assign_112" sap:VirtualizedContainerService.HintSize="553,60" />
      <sap2010:ViewStateData Id="Assign_113" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_114" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_11" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_76" sap:VirtualizedContainerService.HintSize="264,346">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_115" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_116" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_12" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_77" sap:VirtualizedContainerService.HintSize="264,346">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_53" sap:VirtualizedContainerService.HintSize="553,494">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_78" sap:VirtualizedContainerService.HintSize="575.333333333333,1948">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_54" sap:VirtualizedContainerService.HintSize="701.333333333333,2102">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_79" sap:VirtualizedContainerService.HintSize="723.333333333333,2288">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_5" sap:VirtualizedContainerService.HintSize="754,2440.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_80" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_75" sap:VirtualizedContainerService.HintSize="264,618.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="304,726">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="ShouldCollapseAll">True</x:Boolean>
            <x:Boolean x:Key="ShouldExpandAll">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>