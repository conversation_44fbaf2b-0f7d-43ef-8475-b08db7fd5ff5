﻿<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:iad="clr-namespace:Infor.Activities.Debug;assembly=Infor.Activities.Debug"
 xmlns:iae="clr-namespace:Infor.Activities.Email;assembly=Infor.Activities.Email"
 xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI"
 xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys"
 xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities"
 xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json"
 xmlns:s="clr-namespace:System;assembly=mscorlib"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib"
 xmlns:si="clr-namespace:System.IO;assembly=mscorlib"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="submails" Type="InArgument(scg:List(iae:Mail))" />
    <x:Property Name="emailsubjects" Type="OutArgument(scg:List(x:String))" />
    <x:Property Name="enableMessageBoxes" Type="InArgument(x:Boolean)" />
    <x:Property Name="logfile" Type="InArgument(x:String)" />
    <x:Property Name="invoiceSource" Type="InArgument(x:String)" />
    <x:Property Name="configurationFolder" Type="InArgument(x:String)" />
    <x:Property Name="seqnumber" Type="InArgument(x:String)" />
    <x:Property Name="DownloadTo" Type="InArgument(x:String)" />
    <x:Property Name="FileRenameFormat" Type="InArgument(x:String)" />
    <x:Property Name="miscValues" Type="InArgument(scg:Dictionary(x:String, x:Object))" />
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="projectPath" Type="InArgument(x:String)" />
    <x:Property Name="colemanAPI" Type="InArgument(x:String)" />
    <x:Property Name="chargeCode" Type="InArgument(x:String)" />
    <x:Property Name="authUser" Type="InArgument(x:String)" />
    <x:Property Name="userIdentifier" Type="InArgument(x:String)" />
    <x:Property Name="distributionType" Type="InArgument(x:String)" />
    <x:Property Name="poFilterValues" Type="InArgument(x:String)" />
    <x:Property Name="poFilterCondition" Type="InArgument(x:String)" />
    <x:Property Name="datalakeAPILogicalId" Type="InArgument(x:String)" />
    <x:Property Name="discountCode" Type="InArgument(x:String)" />
    <x:Property Name="imsAPIUrl" Type="InArgument(x:String)" />
    <x:Property Name="extractNumericFromPO" Type="InArgument(x:Boolean)" />
    <x:Property Name="vatCodeConfig" Type="InArgument(x:String)" />
    <x:Property Name="poDiscountsHandlingConfig" Type="InArgument(x:Boolean)" />
    <x:Property Name="matchVendorItemCode" Type="InArgument(x:Boolean)" />
    <x:Property Name="extractFromWidgetDatalake" Type="InArgument(x:Boolean)" />
    <x:Property Name="approvalRequired" Type="InArgument(x:Boolean)" />
    <x:Property Name="approvalWorkflow" Type="InArgument(x:String)" />
    <x:Property Name="processExpenseInvoice" Type="InArgument(x:Boolean)" />
    <x:Property Name="checkAmountBussinessRule" Type="InArgument(x:String)" />
    <x:Property Name="MasterDownloads" Type="InArgument(x:String)" />
    <x:Property Name="emailAccount" Type="InArgument(x:String)" />
    <x:Property Name="mainStoreStats" Type="OutArgument(scg:List(scg:List(x:String)))" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>System</x:String>
      <x:String>System.IO</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>System.Text.RegularExpressions</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <TryCatch DisplayName="TryCatch Preprocess Flow" sap2010:WorkflowViewState.IdRef="TryCatch_11">
    <TryCatch.Variables>
      <Variable x:TypeArguments="x:String" Name="SublogFile" />
      <Variable x:TypeArguments="scg:List(x:String)" Default="[new list( of string)]" Name="ListBulkNames" />
    </TryCatch.Variables>
    <TryCatch.Try>
      <Sequence DisplayName="Outlook Preprocess Sequence" sap2010:WorkflowViewState.IdRef="Sequence_105">
        <Sequence.Variables>
          <Variable x:TypeArguments="x:String" Name="inProgressFolder" />
          <Variable x:TypeArguments="x:Int32" Default="0" Name="pdfs_count" />
          <Variable x:TypeArguments="si:FileInfo" Name="fileDetails" />
          <Variable x:TypeArguments="x:Int32" Name="RenameCounter" />
          <Variable x:TypeArguments="scg:List(x:String)" Name="ListDownloadedFiles" />
          <Variable x:TypeArguments="x:Boolean" Name="attachmentsAvail" />
          <Variable x:TypeArguments="x:Int32" Name="processPoRespCode" />
          <Variable x:TypeArguments="x:String" Name="subDownloadFolder" />
          <Variable x:TypeArguments="x:String" Name="InvoiceFileName" />
          <Variable x:TypeArguments="scg:List(x:String)" Name="storeStats" />
        </Sequence.Variables>
        <ias:File_Create ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Create Sub Log File" sap2010:WorkflowViewState.IdRef="File_Create_4" Name="[&quot;SublogFile_&quot;+seqnumber+&quot;.txt&quot;]" OutputFile="[SublogFile]" Target="[configurationFolder+&quot;\OutlookDownloads\&quot;]" />
        <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="SubLog Preprocess Start - Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_45" Line="[&quot;Logs from Preprocess Sequence - #&quot;+seqnumber.ToString+ &quot; - Start&quot;+Environment.NewLine+&quot;------------------------------------------------&quot;]" Source="[SublogFile]" />
        <Assign sap2010:WorkflowViewState.IdRef="Assign_155">
          <Assign.To>
            <OutArgument x:TypeArguments="scg:List(scg:List(x:String))">[mainStoreStats]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="scg:List(scg:List(x:String))">[New List(of list(of string))]</InArgument>
          </Assign.Value>
        </Assign>
        <Assign sap2010:WorkflowViewState.IdRef="Assign_156">
          <Assign.To>
            <OutArgument x:TypeArguments="x:Int32">[RenameCounter]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:Int32">1</InArgument>
          </Assign.Value>
        </Assign>
        <Assign sap2010:WorkflowViewState.IdRef="Assign_157">
          <Assign.To>
            <OutArgument x:TypeArguments="x:String">[inProgressFolder]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:String">[configurationFolder+ "\InProgress"]</InArgument>
          </Assign.Value>
        </Assign>
        <Assign sap2010:WorkflowViewState.IdRef="Assign_158">
          <Assign.To>
            <OutArgument x:TypeArguments="scg:List(x:String)">[emailsubjects]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="scg:List(x:String)">[new list( of string)]</InArgument>
          </Assign.Value>
        </Assign>
        <ForEach x:TypeArguments="iae:Mail" sap2010:Annotation.AnnotationText="All the attachments from the sub mails list SM1, SM2, SM3, SM4, SM5 will be downlaoded as part of this block of code. This block also includes additional functionality of handling duplicate file names and also changing .PDF to _copy.pdf" DisplayName="ForEach submails" sap2010:WorkflowViewState.IdRef="ForEach`1_39" Values="[submails]">
          <ActivityAction x:TypeArguments="iae:Mail">
            <ActivityAction.Argument>
              <DelegateInArgument x:TypeArguments="iae:Mail" Name="email" />
            </ActivityAction.Argument>
            <TryCatch DisplayName="TryCatch - Process Each email (Inside for each sequence)" sap2010:WorkflowViewState.IdRef="TryCatch_10">
              <TryCatch.Try>
                <Sequence DisplayName="For each Mail Sequence" sap2010:WorkflowViewState.IdRef="Sequence_102">
                  <Sequence.Variables>
                    <Variable x:TypeArguments="scg:List(x:String)" Name="MasterFiles" />
                    <Variable x:TypeArguments="scg:List(x:String)" Name="attachments" />
                    <Variable x:TypeArguments="x:Boolean" Name="pdfFileAvailable" />
                    <Variable x:TypeArguments="x:Double" Name="filelength" />
                    <Variable x:TypeArguments="scg:List(x:String)" Default="[New List(Of String)]" Name="ListMasterNames" />
                  </Sequence.Variables>
                  <If Condition="[enableMessageBoxes]" DisplayName="Check If enableMessageBoxes" sap2010:WorkflowViewState.IdRef="If_63">
                    <If.Then>
                      <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" Title="{x:Null}" ContinueOnError="True" DisplayName="Message Box" sap2010:WorkflowViewState.IdRef="MessageBox_24" Selection="OK" Text="[&quot;Email Subject :&quot;+email.subject]" />
                    </If.Then>
                  </If>
                  <ias:Directory_GetFiles ErrorCode="{x:Null}" ContinueOnError="False" DirPath="[MasterDownloads]" DisplayName="Get Files in Directory - Master Downloads folder" FileType="All" Files="[MasterFiles]" sap2010:WorkflowViewState.IdRef="Directory_GetFiles_17" IncludeSubDir="True" />
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_159">
                    <Assign.To>
                      <OutArgument x:TypeArguments="scg:List(x:String)">[ListMasterNames]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="scg:List(x:String)">[new list( of string)]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <ForEach x:TypeArguments="x:String" DisplayName="Get FileNames from Master Downloads folder" sap2010:WorkflowViewState.IdRef="ForEach`1_36" Values="[MasterFiles]">
                    <ActivityAction x:TypeArguments="x:String">
                      <ActivityAction.Argument>
                        <DelegateInArgument x:TypeArguments="x:String" Name="item" />
                      </ActivityAction.Argument>
                      <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_55" MethodName="Add">
                        <InvokeMethod.TargetObject>
                          <InArgument x:TypeArguments="scg:List(x:String)">[ListMasterNames]</InArgument>
                        </InvokeMethod.TargetObject>
                        <InArgument x:TypeArguments="x:String">[Path.GetFileName(item).Tostring]</InArgument>
                      </InvokeMethod>
                    </ActivityAction>
                  </ForEach>
                  <Switch x:TypeArguments="x:String" DisplayName="Downlaod Attachments based on Email Source" Expression="[invoiceSource]" sap2010:WorkflowViewState.IdRef="Switch`1_24">
                    <x:Null x:Key="OutlookGraphEmail" />
                    <Sequence x:Key="OutlookClientEmail" sap2010:WorkflowViewState.IdRef="Sequence_91">
                      <iae:DownloadOutlookAttachment ResponseCode="{x:Null}" ContinueOnError="False" DisplayName="Download Outlook Attachment" Email="[email]" sap2010:WorkflowViewState.IdRef="DownloadOutlookAttachment_8" OutputFilePaths="[attachments]" Path="[configurationFolder+&quot;\OutlookDownloads\&quot;+DownloadTo+seqnumber]" />
                    </Sequence>
                  </Switch>
                  <Assign DisplayName="Assign pdfFileAvailable as false" sap2010:WorkflowViewState.IdRef="Assign_160">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:Boolean">[pdfFileAvailable]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_161">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:Boolean">[attachmentsAvail]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                    </Assign.Value>
                  </Assign>
                  <ForEach x:TypeArguments="x:String" DisplayName="Extract ZIP files in attachments" sap2010:WorkflowViewState.IdRef="ForEach`1_37" Values="[attachments]">
                    <ActivityAction x:TypeArguments="x:String">
                      <ActivityAction.Argument>
                        <DelegateInArgument x:TypeArguments="x:String" Name="attachment" />
                      </ActivityAction.Argument>
                      <If Condition="[attachment.contains(&quot;.zip&quot;) or attachment.contains(&quot;.ZIP&quot;)]" DisplayName="If .ZIP file Attachment" sap2010:WorkflowViewState.IdRef="If_64">
                        <If.Then>
                          <Sequence DisplayName="Extract Zip" sap2010:WorkflowViewState.IdRef="Sequence_92">
                            <Sequence.Variables>
                              <Variable x:TypeArguments="x:String" Name="extractedFolderPath" />
                              <Variable x:TypeArguments="scg:List(x:String)" Name="extractedFiles" />
                              <Variable x:TypeArguments="x:String" Name="extractedFilePath" />
                            </Sequence.Variables>
                            <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_46" Line="[&quot;ZIP File Extracted started for attachments in email - &quot;+email.subject]" Source="[SublogFile]" />
                            <ias:Directory_Extract ErrorCode="{x:Null}" Password="{x:Null}" ContinueOnError="False" DisplayName="Extract Directory" sap2010:WorkflowViewState.IdRef="Directory_Extract_5" OutputFile="[extractedFolderPath]" Source="[attachment]" Target="[configurationFolder+&quot;\OutlookDownloads\&quot;+DownloadTo+seqnumber]" />
                            <ias:File_Delete ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Delete ZIP File" sap2010:WorkflowViewState.IdRef="File_Delete_17" Source="[attachment]" />
                          </Sequence>
                        </If.Then>
                      </If>
                    </ActivityAction>
                  </ForEach>
                  <ias:Directory_GetFiles ErrorCode="{x:Null}" ContinueOnError="False" DirPath="[configurationFolder+&quot;\OutlookDownloads\&quot;+DownloadTo+seqnumber]" DisplayName="Get Files in OutlookDownloads" FileType="All" Files="[ListDownloadedFiles]" sap2010:WorkflowViewState.IdRef="Directory_GetFiles_18" IncludeSubDir="True" />
                  <ForEach x:TypeArguments="x:String" DisplayName="ForEach File in OutlookDownloads" sap2010:WorkflowViewState.IdRef="ForEach`1_38" Values="[ListDownloadedFiles]">
                    <ActivityAction x:TypeArguments="x:String">
                      <ActivityAction.Argument>
                        <DelegateInArgument x:TypeArguments="x:String" Name="item" />
                      </ActivityAction.Argument>
                      <Sequence DisplayName="Inside For each file in OutlookDownloads" sap2010:WorkflowViewState.IdRef="Sequence_100">
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_162">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:Boolean">[attachmentsAvail]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Switch x:TypeArguments="x:Boolean" DisplayName="Rename .PDF files if any" Expression="[item.contains(&quot;.PDF&quot;)]" sap2010:WorkflowViewState.IdRef="Switch`1_25">
                          <Sequence x:Key="True" DisplayName="PDF Sequence" sap2010:WorkflowViewState.IdRef="Sequence_93">
                            <Sequence.Variables>
                              <Variable x:TypeArguments="x:String" Name="pdfFileName" />
                              <Variable x:TypeArguments="x:String" Name="copiedFilePath" />
                            </Sequence.Variables>
                            <Assign DisplayName="Assign pdfFileName" sap2010:WorkflowViewState.IdRef="Assign_163">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[pdfFileName]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[Path.GetFileName(item).replace(".PDF","_copy.pdf")]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <ias:File_Copy ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Copy File" sap2010:WorkflowViewState.IdRef="File_Copy_7" OutputFile="[copiedFilePath]" OverwriteFile="False" Source="[item]" Target="[Path.GetDirectoryName(item)]" TargetFilename="[pdfFileName]" />
                            <ias:File_Delete ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Delete File" sap2010:WorkflowViewState.IdRef="File_Delete_18" Source="[item]" />
                            <Assign DisplayName="Assign attachment+_Copy" sap2010:WorkflowViewState.IdRef="Assign_164">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[item]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[item.replace(".PDF","_copy.pdf")]</InArgument>
                              </Assign.Value>
                            </Assign>
                          </Sequence>
                        </Switch>
                        <Switch x:TypeArguments="x:Boolean" DisplayName=".pdf Check If attachment.contains(&quot;.pdf&quot;)" Expression="[item.contains(&quot;.pdf&quot;)]" sap2010:WorkflowViewState.IdRef="Switch`1_27">
                          <Sequence x:Key="True" DisplayName="If .pdf extension" sap2010:WorkflowViewState.IdRef="Sequence_98">
                            <Sequence.Variables>
                              <Variable x:TypeArguments="x:String" Name="MovedFilePath" />
                            </Sequence.Variables>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_165">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:Boolean">[pdfFileAvailable]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_166">
                              <Assign.To>
                                <OutArgument x:TypeArguments="si:FileInfo">[fileDetails]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="si:FileInfo">[New FileInfo(item)]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign DisplayName="fileLength Assign" sap2010:WorkflowViewState.IdRef="Assign_167">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:Double">[filelength]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:Double">[Math.Round(filedetails.Length / 1024.0 / 1024.0, 2)]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <If Condition="[filelength&lt;=5]" DisplayName="Check file Length" sap2010:WorkflowViewState.IdRef="If_65">
                              <If.Then>
                                <Sequence DisplayName="ProcessEmailAttachment" sap2010:WorkflowViewState.IdRef="Sequence_94">
                                  <Sequence.Variables>
                                    <Variable x:TypeArguments="x:String" Name="variable1" />
                                  </Sequence.Variables>
                                  <Assign DisplayName="Assign pdfFileAvailable to True" sap2010:WorkflowViewState.IdRef="Assign_168">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:Boolean">[pdfFileAvailable]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_56" MethodName="Add">
                                    <InvokeMethod.TargetObject>
                                      <InArgument x:TypeArguments="scg:List(x:String)">[emailsubjects]</InArgument>
                                    </InvokeMethod.TargetObject>
                                    <InArgument x:TypeArguments="x:String">[item.Substring(item.LastIndexOf("\"c)+1,(item.Length()-item.LastIndexOf("\"c))-1)]</InArgument>
                                  </InvokeMethod>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_169">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:Int32">[pdfs_count]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:Int32">[pdfs_count+1]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_170">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[InvoiceFileName]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">[Path.GetFilename(item)]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <Switch x:TypeArguments="x:Boolean" DisplayName="Move Files to InProgress Folder" Expression="[ListMasterNames.Contains(Path.GetFileName(item))]" sap2010:WorkflowViewState.IdRef="Switch`1_26">
                                    <Sequence x:Key="True" DisplayName="File Name Already Present in Master Downloads" sap2010:WorkflowViewState.IdRef="Sequence_95">
                                      <iad:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_17">
                                        <iad:CommentOut.Activities>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_192">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[InvoiceFileName]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">[If(System.Text.RegularExpressions.Regex.IsMatch(InvoiceFileName, "[:#&amp;@`'*/\?&lt;&gt;|""]"),System.Text.RegularExpressions.Regex.Replace(InvoiceFileName, "[:#&amp;@`'*/\?&lt;&gt;|""]", "_"),InvoiceFileName)]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                        </iad:CommentOut.Activities>
                                      </iad:CommentOut>
                                      <If Condition="[System.Text.RegularExpressions.Regex.IsMatch(InvoiceFileName, &quot;[:#&amp;@`'*/\?&lt;&gt;|&quot;&quot;]&quot;)]" DisplayName="Check if the FileName Contains the special Characters" sap2010:WorkflowViewState.IdRef="If_66">
                                        <If.Then>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_171">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[InvoiceFileName]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">[System.Text.RegularExpressions.Regex.Replace(InvoiceFileName, "[:#&amp;@`'*/\?&lt;&gt;|""]", "_")]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                        </If.Then>
                                      </If>
                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_172">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:String">[InvoiceFileName]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:String">[seqnumber+"_"+InvoiceFileName]</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_190">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:String">[InvoiceFileName]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:String">[InvoiceFileName.Replace("~","")]</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                      <ias:File_Move ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Move File" sap2010:WorkflowViewState.IdRef="File_Move_12" OutputFile="[MovedFilePath]" OverwriteFile="False" Source="[item]" Target="[MasterDownloads]" targetName="[InvoiceFileName.Replace(&quot;.pdf&quot;,&quot;_&quot;+FileRenameFormat+&quot;_&quot;+RenameCounter.Tostring+&quot;.pdf&quot;)]" />
                                    </Sequence>
                                    <Sequence x:Key="False" DisplayName="File Name not Already Present in Master Downloads Folder" sap2010:WorkflowViewState.IdRef="Sequence_96">
                                      <If Condition="[System.Text.RegularExpressions.Regex.IsMatch(InvoiceFileName, &quot;[:#&amp;@`'*/\?&lt;&gt;|&quot;&quot;]&quot;)]" DisplayName="Check if the FileName Contains the special Characters" sap2010:WorkflowViewState.IdRef="If_67">
                                        <If.Then>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_173">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[InvoiceFileName]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">[System.Text.RegularExpressions.Regex.Replace(InvoiceFileName, "[:#&amp;@~`'*/\?&lt;&gt;|""]", "_")]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                        </If.Then>
                                      </If>
                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_174">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:String">[InvoiceFileName]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:String">[seqnumber+"_"+InvoiceFileName]</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_191">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:String">[InvoiceFileName]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:String">[InvoiceFileName.Replace("~","")]</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                      <ias:File_Move ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Move File" sap2010:WorkflowViewState.IdRef="File_Move_13" OutputFile="[MovedFilePath]" OverwriteFile="False" Source="[item]" Target="[MasterDownloads]" targetName="[InvoiceFileName]" />
                                    </Sequence>
                                  </Switch>
                                </Sequence>
                              </If.Then>
                              <If.Else>
                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_106">
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_200">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[InvoiceFileName]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">[Path.GetFilename(item)]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_71" MethodName="Add">
                                    <InvokeMethod.TargetObject>
                                      <InArgument x:TypeArguments="scg:List(x:String)">[ListBulkNames]</InArgument>
                                    </InvokeMethod.TargetObject>
                                    <InArgument x:TypeArguments="x:String">[InvoiceFileName]</InArgument>
                                  </InvokeMethod>
                                  <Switch x:TypeArguments="x:Boolean" DisplayName="Move Files to BulkFiles Folder" Expression="[ListBulkNames.Contains(InvoiceFileName)]" sap2010:WorkflowViewState.IdRef="Switch`1_30">
                                    <Sequence x:Key="True" DisplayName="File Name Already Present in Bulk Files" sap2010:WorkflowViewState.IdRef="Sequence_107">
                                      <If Condition="[System.Text.RegularExpressions.Regex.IsMatch(InvoiceFileName, &quot;[:#&amp;@`'*/\?&lt;&gt;|&quot;&quot;]&quot;)]" DisplayName="Check if the FileName Contains the special Characters" sap2010:WorkflowViewState.IdRef="If_72">
                                        <If.Then>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_194">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[InvoiceFileName]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">[System.Text.RegularExpressions.Regex.Replace(InvoiceFileName, "[:#&amp;@`'*/\?&lt;&gt;|""]", "_")]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                        </If.Then>
                                      </If>
                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_195">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:String">[InvoiceFileName]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:String">[seqnumber+"_"+InvoiceFileName]</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_196">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:String">[InvoiceFileName]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:String">[InvoiceFileName.Replace("~","")]</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                      <ias:File_Move ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Move File" sap2010:WorkflowViewState.IdRef="File_Move_15" OutputFile="[MovedFilePath]" OverwriteFile="False" Source="[item]" Target="[configurationFolder+&quot;\OutlookDownloads\BulkFiles&quot;]" targetName="[InvoiceFileName.Replace(&quot;.pdf&quot;,&quot;_&quot;+FileRenameFormat+&quot;_&quot;+RenameCounter.Tostring+&quot;.pdf&quot;)]" />
                                    </Sequence>
                                    <Sequence x:Key="False" DisplayName="File Name not Already Present in Master Downloads Folder" sap2010:WorkflowViewState.IdRef="Sequence_108">
                                      <If Condition="[System.Text.RegularExpressions.Regex.IsMatch(InvoiceFileName, &quot;[:#&amp;@`'*/\?&lt;&gt;|&quot;&quot;]&quot;)]" DisplayName="Check if the FileName Contains the special Characters" sap2010:WorkflowViewState.IdRef="If_73">
                                        <If.Then>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_197">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[InvoiceFileName]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">[System.Text.RegularExpressions.Regex.Replace(InvoiceFileName, "[:#&amp;@~`'*/\?&lt;&gt;|""]", "_")]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                        </If.Then>
                                      </If>
                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_198">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:String">[InvoiceFileName]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:String">[seqnumber+"_"+InvoiceFileName]</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_199">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:String">[InvoiceFileName]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:String">[InvoiceFileName.Replace("~","")]</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                      <ias:File_Move ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Move File" sap2010:WorkflowViewState.IdRef="File_Move_16" OutputFile="[MovedFilePath]" OverwriteFile="False" Source="[item]" Target="[configurationFolder+&quot;\OutlookDownloads\BulkFiles&quot;]" targetName="[InvoiceFileName]" />
                                    </Sequence>
                                  </Switch>
                                  <iad:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_19">
                                    <iad:CommentOut.Activities>
                                      <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="File length &gt;5 MB Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_47" Line="[&quot;File length is greater than 5 MB. File name is - &quot;+Path.getfilename(item)]" Source="[SublogFile]" />
                                    </iad:CommentOut.Activities>
                                  </iad:CommentOut>
                                </Sequence>
                              </If.Else>
                            </If>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_175">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:Int32">[RenameCounter]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:Int32">[RenameCounter+1]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Sequence DisplayName="Storing Stats Sequence" sap2010:WorkflowViewState.IdRef="Sequence_97">
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_176">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="scg:List(x:String)">[storeStats]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="scg:List(x:String)">[New List(of String)]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <InvokeMethod DisplayName="email Account InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_57" MethodName="Add">
                                <InvokeMethod.TargetObject>
                                  <InArgument x:TypeArguments="scg:List(x:String)">[storeStats]</InArgument>
                                </InvokeMethod.TargetObject>
                                <InArgument x:TypeArguments="x:String">[emailAccount]</InArgument>
                              </InvokeMethod>
                              <InvokeMethod DisplayName="email Sender InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_58" MethodName="Add">
                                <InvokeMethod.TargetObject>
                                  <InArgument x:TypeArguments="scg:List(x:String)">[storeStats]</InArgument>
                                </InvokeMethod.TargetObject>
                                <InArgument x:TypeArguments="x:String">[email.sender]</InArgument>
                              </InvokeMethod>
                              <InvokeMethod DisplayName="email Receiver InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_59" MethodName="Add">
                                <InvokeMethod.TargetObject>
                                  <InArgument x:TypeArguments="scg:List(x:String)">[storeStats]</InArgument>
                                </InvokeMethod.TargetObject>
                                <InArgument x:TypeArguments="x:String">[emailAccount]</InArgument>
                              </InvokeMethod>
                              <InvokeMethod DisplayName="email ReceivedTime InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_60" MethodName="Add">
                                <InvokeMethod.TargetObject>
                                  <InArgument x:TypeArguments="scg:List(x:String)">[storeStats]</InArgument>
                                </InvokeMethod.TargetObject>
                                <InArgument x:TypeArguments="x:String">[email.receivedTime]</InArgument>
                              </InvokeMethod>
                              <InvokeMethod DisplayName="email Subject InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_61" MethodName="Add">
                                <InvokeMethod.TargetObject>
                                  <InArgument x:TypeArguments="scg:List(x:String)">[storeStats]</InArgument>
                                </InvokeMethod.TargetObject>
                                <InArgument x:TypeArguments="x:String">[email.subject]</InArgument>
                              </InvokeMethod>
                              <InvokeMethod DisplayName="FileName InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_62" MethodName="Add">
                                <InvokeMethod.TargetObject>
                                  <InArgument x:TypeArguments="scg:List(x:String)">[storeStats]</InArgument>
                                </InvokeMethod.TargetObject>
                                <InArgument x:TypeArguments="x:String">[item.Substring(item.LastIndexOf("\"c)+1,(item.Length()-item.LastIndexOf("\"c))-1)]</InArgument>
                              </InvokeMethod>
                              <InvokeMethod DisplayName="Execution start time InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_63" MethodName="Add">
                                <InvokeMethod.TargetObject>
                                  <InArgument x:TypeArguments="scg:List(x:String)">[storeStats]</InArgument>
                                </InvokeMethod.TargetObject>
                                <InArgument x:TypeArguments="x:String">[System.DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ")]</InArgument>
                              </InvokeMethod>
                              <InvokeMethod DisplayName="Execution EndTime InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_64" MethodName="Add">
                                <InvokeMethod.TargetObject>
                                  <InArgument x:TypeArguments="scg:List(x:String)">[storeStats]</InArgument>
                                </InvokeMethod.TargetObject>
                                <InArgument x:TypeArguments="x:String">
                                  <Literal x:TypeArguments="x:String" Value="" />
                                </InArgument>
                              </InvokeMethod>
                              <InvokeMethod DisplayName="Status InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_65" MethodName="Add">
                                <InvokeMethod.TargetObject>
                                  <InArgument x:TypeArguments="scg:List(x:String)">[storeStats]</InArgument>
                                </InvokeMethod.TargetObject>
                                <InArgument x:TypeArguments="x:String">
                                  <Literal x:TypeArguments="x:String" Value="" />
                                </InArgument>
                              </InvokeMethod>
                              <InvokeMethod DisplayName="Comments InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_66" MethodName="Add">
                                <InvokeMethod.TargetObject>
                                  <InArgument x:TypeArguments="scg:List(x:String)">[storeStats]</InArgument>
                                </InvokeMethod.TargetObject>
                                <InArgument x:TypeArguments="x:String">
                                  <Literal x:TypeArguments="x:String" Value="" />
                                </InArgument>
                              </InvokeMethod>
                              <InvokeMethod DisplayName="ProcessID InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_67" MethodName="Add">
                                <InvokeMethod.TargetObject>
                                  <InArgument x:TypeArguments="scg:List(x:String)">[storeStats]</InArgument>
                                </InvokeMethod.TargetObject>
                                <InArgument x:TypeArguments="x:String">
                                  <Literal x:TypeArguments="x:String" Value="" />
                                </InArgument>
                              </InvokeMethod>
                              <InvokeMethod DisplayName="processfilename InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_68" MethodName="Add">
                                <InvokeMethod.TargetObject>
                                  <InArgument x:TypeArguments="scg:List(x:String)">[storeStats]</InArgument>
                                </InvokeMethod.TargetObject>
                                <InArgument x:TypeArguments="x:String">
                                  <Literal x:TypeArguments="x:String" Value="" />
                                </InArgument>
                              </InvokeMethod>
                              <InvokeMethod DisplayName="email Account InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_69" MethodName="Add">
                                <InvokeMethod.TargetObject>
                                  <InArgument x:TypeArguments="scg:List(scg:List(x:String))">[mainStoreStats]</InArgument>
                                </InvokeMethod.TargetObject>
                                <InArgument x:TypeArguments="scg:List(x:String)">[storeStats]</InArgument>
                              </InvokeMethod>
                            </Sequence>
                          </Sequence>
                          <ias:File_Delete ErrorCode="{x:Null}" x:Key="False" ContinueOnError="True" DisplayName="Delete File" sap2010:WorkflowViewState.IdRef="File_Delete_19" Source="[item]" />
                        </Switch>
                        <If Condition="[item.EndsWith(&quot;.pdf&quot;) OR item.EndsWith(&quot;.PDF&quot;) OR item.EndsWith(&quot;.txt&quot;) OR item.EndsWith(&quot;.jpg&quot;) OR item.EndsWith(&quot;.jpeg&quot;) OR item.EndsWith(&quot;.png&quot;) OR item.EndsWith(&quot;.zip&quot;) OR item.EndsWith(&quot;.ZIP&quot;)]" DisplayName="Check other file extensions" sap2010:WorkflowViewState.IdRef="If_69">
                          <If.Else>
                            <Sequence DisplayName="Other file extensions Sequence" sap2010:WorkflowViewState.IdRef="Sequence_99">
                              <Sequence.Variables>
                                <Variable x:TypeArguments="x:String" Name="status" />
                                <Variable x:TypeArguments="x:String" Name="statusComments" />
                                <Variable x:TypeArguments="x:String" Name="fileName" />
                                <Variable x:TypeArguments="x:String" Name="message" />
                                <Variable x:TypeArguments="x:String" Name="emailSubject" />
                                <Variable x:TypeArguments="x:String" Name="emailReceivedTime" />
                                <Variable x:TypeArguments="x:String" Name="notificationRequestStr" />
                                <Variable x:TypeArguments="njl:JToken" Name="notificationToken" />
                                <Variable x:TypeArguments="iru:ResponseObject" Name="notificationResponse" />
                                <Variable x:TypeArguments="x:Int32" Name="notificationResponseCode" />
                              </Sequence.Variables>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_177">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[status]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_178">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[statusComments]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">Wrong Format of the document</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_179">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[fileName]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[Path.GetFileName(item)]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_180">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[message]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">["RPA M3 IP" +" - " + status + "( "+statusComments+")"]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_181">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[emailSubject]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[email.Subject]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_182">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[emailReceivedTime]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[email.ReceivedTime]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <If Condition="[emailSubject.contains(&quot;'&quot;)]" sap2010:WorkflowViewState.IdRef="If_68">
                                <If.Then>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_183">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[emailSubject]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">[emailSubject.Replace("'","")]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                </If.Then>
                              </If>
                              <ias:Template_Apply ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Templating Activity" sap2010:WorkflowViewState.IdRef="Template_Apply_8" Template="{}{&#xA;    'message': '{{%msg%}}',&#xA;    'parameters': [&#xA;        {&#xA;            'dataType': 'STRING',&#xA;            'name': 'RPA_INVOICEPROCESSING_STATUS',&#xA;            'value': '{{%status%}}',&#xA;            'label': 'Status',&#xA;            'readOnly': true&#xA;        },&#xA;        {&#xA;            'dataType': 'STRING',&#xA;            'name': 'RPA_INVOICEPROCESSING_Subject',&#xA;            'value': '{{%Subject%}}',&#xA;            'label': 'Subject',&#xA;            'readOnly': true&#xA;        },&#xA;        {&#xA;            'dataType': 'STRING',&#xA;            'name': 'RPA_INVOICEPROCESSING_RcvdTime',&#xA;            'value': '{{%RcvdTime%}}',&#xA;            'label': 'Recieved Time',&#xA;            'readOnly': true&#xA;        },&#xA;        {&#xA;            'dataType': 'STRING',&#xA;            'name': 'RPA_INVOICEPROCESSING_FileName',&#xA;            'value': '{{%fileName%}}',&#xA;            'label': 'File Name',&#xA;            'readOnly': true&#xA;        },&#xA;        {&#xA;            'dataType': 'STRING',&#xA;            'name': 'RPA_INVOICEPROCESSING_Remarks',&#xA;            'value': '{{%Remarks%}}',&#xA;            'label': 'Remarks',&#xA;            'readOnly': true&#xA;        }&#xA;    ],&#xA;    'category': 'RPA',&#xA;    'distribution': [&#xA;        {&#xA;            'identifier': '{{%userIdentifier%}}',&#xA;            'type': '{{%distributionType%}}',&#xA;            'sendMail': false&#xA;        }&#xA;    ]&#xA;}&#xA;" Text="[notificationRequestStr]">
                                <ias:Template_Apply.Values>
                                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                    <scg:List x:TypeArguments="x:String" Capacity="8">
                                      <x:String>status</x:String>
                                      <x:String>userIdentifier</x:String>
                                      <x:String>distributionType</x:String>
                                      <x:String>Remarks</x:String>
                                      <x:String>RcvdTime</x:String>
                                      <x:String>Subject</x:String>
                                      <x:String>msg</x:String>
                                      <x:String>fileName</x:String>
                                    </scg:List>
                                    <scg:List x:TypeArguments="x:String" Capacity="8">
                                      <x:String>status</x:String>
                                      <x:String>userIdentifier</x:String>
                                      <x:String>distributionType</x:String>
                                      <x:String>statusComments</x:String>
                                      <x:String>emailReceivedTime</x:String>
                                      <x:String>emailSubject</x:String>
                                      <x:String>message</x:String>
                                      <x:String>fileName</x:String>
                                    </scg:List>
                                  </scg:List>
                                </ias:Template_Apply.Values>
                              </ias:Template_Apply>
                              <ias:DeserializeJSON ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Deserialize JSON" sap2010:WorkflowViewState.IdRef="DeserializeJSON_8" JTokenObject="[notificationToken]" JTokenString="[notificationRequestStr]" />
                              <iai:IONAPIRequestWizard FileAttachments="{x:Null}" Headers="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="False" DisplayName="IONAPI Request" HttpMethod="POST" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_8" PostData="[notificationToken.tostring]" Response="[notificationResponse]" ResponseCode="[notificationResponseCode]" Url="[tenantID+ &quot;IONSERVICES/process/application/v1/pulse/notification/create&quot;]">
                                <iai:IONAPIRequestWizard.QueryParameters>
                                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                      <x:String>logicalId</x:String>
                                    </scg:List>
                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                      <x:String>lid://infor.rpa.1</x:String>
                                    </scg:List>
                                  </scg:List>
                                </iai:IONAPIRequestWizard.QueryParameters>
                              </iai:IONAPIRequestWizard>
                            </Sequence>
                          </If.Else>
                        </If>
                        <iad:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_16">
                          <iad:CommentOut.Activities>
                            <If Condition="[pdfFileAvailable]" sap2010:WorkflowViewState.IdRef="If_70">
                              <If.Then>
                                <InvokeMethod DisplayName="email Account InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_70" MethodName="Add">
                                  <InvokeMethod.TargetObject>
                                    <InArgument x:TypeArguments="scg:List(scg:List(x:String))">[mainStoreStats]</InArgument>
                                  </InvokeMethod.TargetObject>
                                  <InArgument x:TypeArguments="scg:List(x:String)">[storeStats]</InArgument>
                                </InvokeMethod>
                              </If.Then>
                            </If>
                          </iad:CommentOut.Activities>
                        </iad:CommentOut>
                      </Sequence>
                    </ActivityAction>
                  </ForEach>
                  <Switch x:TypeArguments="x:Boolean" DisplayName="If attachments Avail False" Expression="[attachmentsAvail]" sap2010:WorkflowViewState.IdRef="Switch`1_28">
                    <Sequence x:Key="False" DisplayName="No Attachment Available" sap2010:WorkflowViewState.IdRef="Sequence_101">
                      <Sequence.Variables>
                        <Variable x:TypeArguments="x:String" Name="status" />
                        <Variable x:TypeArguments="x:String" Name="statusComments" />
                        <Variable x:TypeArguments="x:String" Name="fileName" />
                        <Variable x:TypeArguments="x:String" Name="message" />
                        <Variable x:TypeArguments="x:String" Name="emailSubject" />
                        <Variable x:TypeArguments="x:String" Name="emailReceivedTime" />
                        <Variable x:TypeArguments="x:String" Name="notificationRequestStr" />
                        <Variable x:TypeArguments="njl:JToken" Name="notificationToken" />
                        <Variable x:TypeArguments="iru:ResponseObject" Name="notificationResponse" />
                        <Variable x:TypeArguments="x:Int32" Name="notificationResponseCode" />
                      </Sequence.Variables>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_184">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[status]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_185">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[statusComments]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">["No attachments available."]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_186">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[message]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">["RPA M3 IP" +" - " + status + "( "+statusComments+")"]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_187">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[emailSubject]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[email.Subject]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_188">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[emailReceivedTime]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[email.ReceivedTime]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_48" Line="No pdf files available to process" Source="[logFIle]" />
                      <If Condition="[emailSubject.contains(&quot;'&quot;)]" sap2010:WorkflowViewState.IdRef="If_71">
                        <If.Then>
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_189">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:String">[emailSubject]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String">[emailSubject.Replace("'","")]</InArgument>
                            </Assign.Value>
                          </Assign>
                        </If.Then>
                      </If>
                      <ias:Template_Apply ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Templating Activity" sap2010:WorkflowViewState.IdRef="Template_Apply_9" Template="{}{&#xA;    'message': '{{%msg%}}',&#xA;    'parameters': [&#xA;        {&#xA;            'dataType': 'STRING',&#xA;            'name': 'RPA_INVOICEPROCESSING_STATUS',&#xA;            'value': '{{%status%}}',&#xA;            'label': 'Status',&#xA;            'readOnly': true&#xA;        },&#xA;        {&#xA;            'dataType': 'STRING',&#xA;            'name': 'RPA_INVOICEPROCESSING_Subject',&#xA;            'value': '{{%Subject%}}',&#xA;            'label': 'Subject',&#xA;            'readOnly': true&#xA;        },&#xA;        {&#xA;            'dataType': 'STRING',&#xA;            'name': 'RPA_INVOICEPROCESSING_RcvdTime',&#xA;            'value': '{{%RcvdTime%}}',&#xA;            'label': 'Recieved Time',&#xA;            'readOnly': true&#xA;        },&#xA;        {&#xA;            'dataType': 'STRING',&#xA;            'name': 'RPA_INVOICEPROCESSING_Remarks',&#xA;            'value': '{{%Remarks%}}',&#xA;            'label': 'Remarks',&#xA;            'readOnly': true&#xA;        }&#xA;    ],&#xA;    'category': 'RPA',&#xA;    'distribution': [&#xA;        {&#xA;            'identifier': '{{%userIdentifier%}}',&#xA;            'type': '{{%distributionType%}}',&#xA;            'sendMail': false&#xA;        }&#xA;    ]&#xA;}&#xA;" Text="[notificationRequestStr]">
                        <ias:Template_Apply.Values>
                          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                            <scg:List x:TypeArguments="x:String" Capacity="8">
                              <x:String>status</x:String>
                              <x:String>userIdentifier</x:String>
                              <x:String>distributionType</x:String>
                              <x:String>Remarks</x:String>
                              <x:String>RcvdTime</x:String>
                              <x:String>Subject</x:String>
                              <x:String>msg</x:String>
                              <x:String>fileName</x:String>
                            </scg:List>
                            <scg:List x:TypeArguments="x:String" Capacity="8">
                              <x:String>status</x:String>
                              <x:String>userIdentifier</x:String>
                              <x:String>distributionType</x:String>
                              <x:String>statusComments</x:String>
                              <x:String>emailReceivedTime</x:String>
                              <x:String>emailSubject</x:String>
                              <x:String>message</x:String>
                              <x:String>fileName</x:String>
                            </scg:List>
                          </scg:List>
                        </ias:Template_Apply.Values>
                      </ias:Template_Apply>
                      <ias:DeserializeJSON ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Deserialize JSON" sap2010:WorkflowViewState.IdRef="DeserializeJSON_9" JTokenObject="[notificationToken]" JTokenString="[notificationRequestStr]" />
                      <iai:IONAPIRequestWizard FileAttachments="{x:Null}" Headers="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request" HttpMethod="POST" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_9" PostData="[notificationToken.tostring]" Response="[notificationResponse]" ResponseCode="[notificationResponseCode]" Url="[tenantID+ &quot;IONSERVICES/process/application/v1/pulse/notification/create&quot;]">
                        <iai:IONAPIRequestWizard.QueryParameters>
                          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                            <scg:List x:TypeArguments="x:String" Capacity="4">
                              <x:String>logicalId</x:String>
                            </scg:List>
                            <scg:List x:TypeArguments="x:String" Capacity="4">
                              <x:String>lid://infor.rpa.1</x:String>
                            </scg:List>
                          </scg:List>
                        </iai:IONAPIRequestWizard.QueryParameters>
                      </iai:IONAPIRequestWizard>
                    </Sequence>
                  </Switch>
                  <Switch x:TypeArguments="x:Boolean" DisplayName="Is any PDF file available" Expression="[pdfFileAvailable]" sap2010:WorkflowViewState.IdRef="Switch`1_29">
                    <ias:Append_Line ErrorCode="{x:Null}" x:Key="False" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_49" Line="[&quot;pdfFileAvailable value is &quot;+pdfFileAvailable.tostring+&quot; for email with subject - &quot;+email.subject]" Source="[SublogFile]" />
                  </Switch>
                </Sequence>
              </TryCatch.Try>
              <TryCatch.Catches>
                <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_10">
                  <ActivityAction x:TypeArguments="s:Exception">
                    <ActivityAction.Argument>
                      <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                    </ActivityAction.Argument>
                    <Sequence DisplayName="Catch Block For Eahc Mail - Sequence" sap2010:WorkflowViewState.IdRef="Sequence_103">
                      <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="For each mail catch block - Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_50" Line="[&quot;Exception occurred in OutlookPreprocess XAML for Email below &quot;+Environment.newline+&quot;Email Subject is -&quot;+email.subject+Environment.newline+&quot;Email Received time is -&quot;+email.ReceivedTime.ToString+Environment.newline+&quot;Exception type is - &quot;+exception.GetType().Name+&quot;.&quot;]" Source="[SublogFile]" />
                    </Sequence>
                  </ActivityAction>
                </Catch>
              </TryCatch.Catches>
            </TryCatch>
          </ActivityAction>
        </ForEach>
        <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="SubLog Preprocess End - Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_51" Line="[&quot;Logs from Preprocess Sequence - #&quot;+seqnumber.ToString+ &quot;End&quot;+Environment.NewLine+Environment.NewLine+Environment.NewLine]" Source="[SublogFile]" />
      </Sequence>
    </TryCatch.Try>
    <TryCatch.Catches>
      <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_11">
        <ActivityAction x:TypeArguments="s:Exception">
          <ActivityAction.Argument>
            <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
          </ActivityAction.Argument>
          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="SubLog Preprocess Start - Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_52" Line="[&quot;Exception Occurred in Preprocess flow Sequence - #&quot;+seqnumber.ToString+&quot; with exception type &quot;+exception.GetType().Name+&quot;.&quot;]" Source="[SublogFile]" />
        </ActivityAction>
      </Catch>
    </TryCatch.Catches>
    <sads:DebugSymbol.Symbol>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</sads:DebugSymbol.Symbol>
  </TryCatch>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="File_Create_4" sap:VirtualizedContainerService.HintSize="678,22" />
      <sap2010:ViewStateData Id="Append_Line_45" sap:VirtualizedContainerService.HintSize="678,22" />
      <sap2010:ViewStateData Id="Assign_155" sap:VirtualizedContainerService.HintSize="678,62" />
      <sap2010:ViewStateData Id="Assign_156" sap:VirtualizedContainerService.HintSize="678,62" />
      <sap2010:ViewStateData Id="Assign_157" sap:VirtualizedContainerService.HintSize="678,62" />
      <sap2010:ViewStateData Id="Assign_158" sap:VirtualizedContainerService.HintSize="678,62" />
      <sap2010:ViewStateData Id="MessageBox_24" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_63" sap:VirtualizedContainerService.HintSize="648.666666666667,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Directory_GetFiles_17" sap:VirtualizedContainerService.HintSize="648.666666666667,22" />
      <sap2010:ViewStateData Id="Assign_159" sap:VirtualizedContainerService.HintSize="648.666666666667,62" />
      <sap2010:ViewStateData Id="InvokeMethod_55" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="ForEach`1_36" sap:VirtualizedContainerService.HintSize="648.666666666667,286.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="DownloadOutlookAttachment_8" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Sequence_91" sap:VirtualizedContainerService.HintSize="222,146">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_24" sap:VirtualizedContainerService.HintSize="648.666666666667,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_160" sap:VirtualizedContainerService.HintSize="648.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_161" sap:VirtualizedContainerService.HintSize="648.666666666667,62" />
      <sap2010:ViewStateData Id="Append_Line_46" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Directory_Extract_5" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="File_Delete_17" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Sequence_92" sap:VirtualizedContainerService.HintSize="222,270">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_64" sap:VirtualizedContainerService.HintSize="200,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_37" sap:VirtualizedContainerService.HintSize="648.666666666667,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Directory_GetFiles_18" sap:VirtualizedContainerService.HintSize="648.666666666667,22" />
      <sap2010:ViewStateData Id="Assign_162" sap:VirtualizedContainerService.HintSize="596,62" />
      <sap2010:ViewStateData Id="Assign_163" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="File_Copy_7" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="File_Delete_18" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_164" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_93" sap:VirtualizedContainerService.HintSize="264,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_25" sap:VirtualizedContainerService.HintSize="596,145.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_165" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_166" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_167" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_168" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="InvokeMethod_56" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="Assign_169" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_170" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_192" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="CommentOut_17" sap:VirtualizedContainerService.HintSize="242,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_171" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_66" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_172" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_190" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="File_Move_12" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_95" sap:VirtualizedContainerService.HintSize="264,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_173" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_67" sap:VirtualizedContainerService.HintSize="464,216">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_174" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Assign_191" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="File_Move_13" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Sequence_96" sap:VirtualizedContainerService.HintSize="486,601.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_26" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_94" sap:VirtualizedContainerService.HintSize="264,656.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_200" sap:VirtualizedContainerService.HintSize="476.666666666667,62" />
      <sap2010:ViewStateData Id="InvokeMethod_71" sap:VirtualizedContainerService.HintSize="476.666666666667,134" />
      <sap2010:ViewStateData Id="Assign_194" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_72" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_195" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_196" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="File_Move_15" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_107" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_197" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_73" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_198" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_199" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="File_Move_16" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_108" sap:VirtualizedContainerService.HintSize="264,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_30" sap:VirtualizedContainerService.HintSize="476.666666666667,274.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_47" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="CommentOut_19" sap:VirtualizedContainerService.HintSize="476.666666666667,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_106" sap:VirtualizedContainerService.HintSize="498.666666666667,772.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_65" sap:VirtualizedContainerService.HintSize="788.666666666667,926.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_175" sap:VirtualizedContainerService.HintSize="788.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_176" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="InvokeMethod_57" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="InvokeMethod_58" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="InvokeMethod_59" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="InvokeMethod_60" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="InvokeMethod_61" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="InvokeMethod_62" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="InvokeMethod_63" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="InvokeMethod_64" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="InvokeMethod_65" sap:VirtualizedContainerService.HintSize="242,128" />
      <sap2010:ViewStateData Id="InvokeMethod_66" sap:VirtualizedContainerService.HintSize="242,128" />
      <sap2010:ViewStateData Id="InvokeMethod_67" sap:VirtualizedContainerService.HintSize="242,128" />
      <sap2010:ViewStateData Id="InvokeMethod_68" sap:VirtualizedContainerService.HintSize="242,128" />
      <sap2010:ViewStateData Id="InvokeMethod_69" sap:VirtualizedContainerService.HintSize="242,128" />
      <sap2010:ViewStateData Id="Sequence_97" sap:VirtualizedContainerService.HintSize="554,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_98" sap:VirtualizedContainerService.HintSize="810.666666666667,1551.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="File_Delete_19" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Switch`1_27" sap:VirtualizedContainerService.HintSize="830.666666666667,1766">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_177" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_178" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_179" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_180" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_181" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_182" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_183" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_68" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Template_Apply_8" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="DeserializeJSON_8" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_8" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_99" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_69" sap:VirtualizedContainerService.HintSize="596,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeMethod_70" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="If_70" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="CommentOut_16" sap:VirtualizedContainerService.HintSize="596,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_100" sap:VirtualizedContainerService.HintSize="852.666666666667,2368">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_38" sap:VirtualizedContainerService.HintSize="883.333333333333,2520.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_184" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_185" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_186" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_187" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_188" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_48" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_189" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_71" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Template_Apply_9" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="DeserializeJSON_9" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_9" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_101" sap:VirtualizedContainerService.HintSize="264,934.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_28" sap:VirtualizedContainerService.HintSize="648.666666666667,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_49" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Switch`1_29" sap:VirtualizedContainerService.HintSize="648.666666666667,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_102" sap:VirtualizedContainerService.HintSize="905.333333333333,3864.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_50" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Sequence_103" sap:VirtualizedContainerService.HintSize="222,146">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Catch`1_10" sap:VirtualizedContainerService.HintSize="675.333333333333,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_10" sap:VirtualizedContainerService.HintSize="924,4102.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_39" sap:VirtualizedContainerService.HintSize="954.666666666667,4296.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
            <x:Boolean x:Key="IsAnnotationDocked">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_51" sap:VirtualizedContainerService.HintSize="678,22" />
      <sap2010:ViewStateData Id="Sequence_105" sap:VirtualizedContainerService.HintSize="976.666666666667,5014.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_52" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Catch`1_11" sap:VirtualizedContainerService.HintSize="704.666666666667,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_11" sap:VirtualizedContainerService.HintSize="995.333333333333,5532.66666666667" />
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="1035.33333333333,5892.66666666667" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>