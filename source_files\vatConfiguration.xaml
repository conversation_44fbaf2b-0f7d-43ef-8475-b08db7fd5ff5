﻿<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI"
 xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys"
 xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities"
 xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json"
 xmlns:s="clr-namespace:System;assembly=mscorlib"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="vat" Type="InArgument(x:String)" />
    <x:Property Name="division" Type="InArgument(x:String)" />
    <x:Property Name="inbnValue" Type="InArgument(x:String)" />
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="logfile" Type="InArgument(x:String)" />
    <x:Property Name="CountryCode" Type="InArgument(x:String)" />
    <x:Property Name="VatPercentage" Type="InArgument(x:String)" />
    <x:Property Name="invoiceType" Type="InArgument(x:String)" />
    <x:Property Name="AccountStartsWith" Type="InArgument(x:String)" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Infor.Activities.IONAPI</x:String>
      <x:String>Infor.Activities.Sys</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>System</x:String>
      <x:String>Infor.RPA.Utilities</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>System.Text.RegularExpressions</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>Microsoft.VisualBasic.CompilerServices</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>Infor.Activities.IONAPI</AssemblyReference>
      <AssemblyReference>Infor.Activities.Sys</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>Infor.RPA.Utilities</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_2">
    <Sequence.Variables>
      <Variable x:TypeArguments="x:String" Name="vatCode" />
      <Variable x:TypeArguments="njl:JToken" Name="brDesOpt" />
      <Variable x:TypeArguments="x:String" Name="brFomat" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="respOutput" />
      <Variable x:TypeArguments="x:Int32" Name="brRespStatus" />
      <Variable x:TypeArguments="x:Int32" Name="respout" />
      <Variable x:TypeArguments="njl:JToken" Name="out5" />
      <Variable x:TypeArguments="x:String" Name="vatAmt" />
      <Variable x:TypeArguments="x:Int32" Name="totalVats" />
      <Variable x:TypeArguments="x:Int32" Default="0" Name="Itr" />
      <Variable x:TypeArguments="x:String" Name="percentage" />
      <Variable x:TypeArguments="x:String" Name="per" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="respout1" />
    </Sequence.Variables>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_24">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[AccountStartsWith]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">[If(invoiceType = "po","",AccountStartsWith)]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_18">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[percentage]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">[VatPercentage]</InArgument>
      </Assign.Value>
    </Assign>
    <TryCatch DisplayName="TryCatch - totalVats" sap2010:WorkflowViewState.IdRef="TryCatch_5">
      <TryCatch.Try>
        <Assign DisplayName="Assign check" sap2010:WorkflowViewState.IdRef="Assign_15">
          <Assign.To>
            <OutArgument x:TypeArguments="x:Int32">[totalVats]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:Int32">[vat.split(","c).length]</InArgument>
          </Assign.Value>
        </Assign>
      </TryCatch.Try>
      <TryCatch.Catches>
        <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_5">
          <ActivityAction x:TypeArguments="s:Exception">
            <ActivityAction.Argument>
              <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
            </ActivityAction.Argument>
            <Assign DisplayName="Assign check" sap2010:WorkflowViewState.IdRef="Assign_32">
              <Assign.To>
                <OutArgument x:TypeArguments="x:Int32">[totalVats]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:Int32">[1]</InArgument>
              </Assign.Value>
            </Assign>
          </ActivityAction>
        </Catch>
      </TryCatch.Catches>
    </TryCatch>
    <If Condition="[totalVats&gt;1]" sap2010:WorkflowViewState.IdRef="If_13">
      <If.Then>
        <While sap2010:WorkflowViewState.IdRef="While_1" Condition="[Itr &lt; totalVats]">
          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_8">
            <Assign sap2010:WorkflowViewState.IdRef="Assign_16">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[vatAmt]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">[vat.split(","c)(Itr)]</InArgument>
              </Assign.Value>
            </Assign>
            <Assign DisplayName="vat%" sap2010:WorkflowViewState.IdRef="Assign_19">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[per]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">[percentage.split(","c)(Itr)]</InArgument>
              </Assign.Value>
            </Assign>
            <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_2">
              <TryCatch.Try>
                <Assign DisplayName="vat%" sap2010:WorkflowViewState.IdRef="Assign_5">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[VatPercentage]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[Convert.toDouble(Regex.Replace(per, "%", "").Trim()).tostring]</InArgument>
                  </Assign.Value>
                </Assign>
              </TryCatch.Try>
              <TryCatch.Catches>
                <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_2">
                  <ActivityAction x:TypeArguments="s:Exception">
                    <ActivityAction.Argument>
                      <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                    </ActivityAction.Argument>
                    <Assign DisplayName="vat%" sap2010:WorkflowViewState.IdRef="Assign_25">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[VatPercentage]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">0</InArgument>
                      </Assign.Value>
                    </Assign>
                  </ActivityAction>
                </Catch>
              </TryCatch.Catches>
            </TryCatch>
            <ias:Template_Apply ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Templating Activity" sap2010:WorkflowViewState.IdRef="Template_Apply_5" Template="{}{ 'parameters': {  'AccountStartsWith':'{{%AccountStartsWith%}}','VatPercentage': '{{%VatPercentage%}}' ,'CountryCode': '{{%CountryCode%}}','invoiceType': '{{%invoiceType%}}' } }" Text="[brfomat]">
              <ias:Template_Apply.Values>
                <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                  <scg:List x:TypeArguments="x:String" Capacity="4">
                    <x:String>VatPercentage</x:String>
                    <x:String>CountryCode</x:String>
                    <x:String>invoiceType</x:String>
                    <x:String>AccountStartsWith</x:String>
                  </scg:List>
                  <scg:List x:TypeArguments="x:String" Capacity="4">
                    <x:String>VatPercentage</x:String>
                    <x:String>CountryCode</x:String>
                    <x:String>invoiceType</x:String>
                    <x:String>AccountStartsWith</x:String>
                  </scg:List>
                </scg:List>
              </ias:Template_Apply.Values>
            </ias:Template_Apply>
            <ias:DeserializeJSON ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Deserialize JSON" sap2010:WorkflowViewState.IdRef="DeserializeJSON_4" JTokenObject="[brDesOpt]" JTokenString="[brFomat]" />
            <iai:IONAPIRequestWizard FileAttachments="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request" HttpMethod="POST" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_5" PostData="[brDesOpt.toString()]" Response="[respOutput]" StatusCode="[brRespStatus]" Url="[tenantID+&quot;IONSERVICES/businessrules/decision/execute/&quot;+&quot;VatCodeConfiguration&quot;]">
              <iai:IONAPIRequestWizard.Headers>
                <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                  <scg:List x:TypeArguments="x:String" Capacity="0" />
                  <scg:List x:TypeArguments="x:String" Capacity="0" />
                </scg:List>
              </iai:IONAPIRequestWizard.Headers>
              <iai:IONAPIRequestWizard.QueryParameters>
                <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                  <scg:List x:TypeArguments="x:String" Capacity="4">
                    <x:String>matrixName</x:String>
                  </scg:List>
                  <scg:List x:TypeArguments="x:String" Capacity="4">
                    <x:String>VatCodeConfiguration</x:String>
                  </scg:List>
                </scg:List>
              </iai:IONAPIRequestWizard.QueryParameters>
            </iai:IONAPIRequestWizard>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_21">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[vatCode]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">[JToken.Parse(respOutput.ReadAsText)("parameters")("VatCode").ToString]</InArgument>
              </Assign.Value>
            </Assign>
            <If Condition="[vatCode = &quot;NA&quot;]" sap2010:WorkflowViewState.IdRef="If_12">
              <If.Then>
                <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding VAT" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_7" Response="[respout1]" ResponseCode="[respout]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                  <iai:IONAPIRequestWizard.Headers>
                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>Accept</x:String>
                      </scg:List>
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>application/json</x:String>
                      </scg:List>
                    </scg:List>
                  </iai:IONAPIRequestWizard.Headers>
                  <iai:IONAPIRequestWizard.QueryParameters>
                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>INBN</x:String>
                        <x:String>RDTP</x:String>
                        <x:String>DIVI</x:String>
                        <x:String>GLAM</x:String>
                      </scg:List>
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>inbnValue</x:String>
                        <x:String>3</x:String>
                        <x:String>division</x:String>
                        <x:String>vatAmt</x:String>
                      </scg:List>
                    </scg:List>
                  </iai:IONAPIRequestWizard.QueryParameters>
                </iai:IONAPIRequestWizard>
              </If.Then>
              <If.Else>
                <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding VAT" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_8" Response="[respout1]" ResponseCode="[respout]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                  <iai:IONAPIRequestWizard.Headers>
                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>Accept</x:String>
                      </scg:List>
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>application/json</x:String>
                      </scg:List>
                    </scg:List>
                  </iai:IONAPIRequestWizard.Headers>
                  <iai:IONAPIRequestWizard.QueryParameters>
                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                      <scg:List x:TypeArguments="x:String" Capacity="8">
                        <x:String>INBN</x:String>
                        <x:String>RDTP</x:String>
                        <x:String>DIVI</x:String>
                        <x:String>VTA1</x:String>
                        <x:String>VTCD</x:String>
                      </scg:List>
                      <scg:List x:TypeArguments="x:String" Capacity="8">
                        <x:String>inbnValue</x:String>
                        <x:String>3</x:String>
                        <x:String>division</x:String>
                        <x:String>vatAmt</x:String>
                        <x:String>vatCode</x:String>
                      </scg:List>
                    </scg:List>
                  </iai:IONAPIRequestWizard.QueryParameters>
                </iai:IONAPIRequestWizard>
              </If.Else>
            </If>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_12">
              <Assign.To>
                <OutArgument x:TypeArguments="njl:JToken">[out5]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respout1.ReadAsText)]</InArgument>
              </Assign.Value>
            </Assign>
            <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_1">
              <TryCatch.Try>
                <If Condition="[respout = 200]" sap2010:WorkflowViewState.IdRef="If_7">
                  <If.Then>
                    <If Condition="[out5(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_6">
                      <If.Then>
                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_6">
                          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_5" Line="[(out5(&quot;results&quot;)(0)(&quot;errorMessage&quot;)).ToString]" Source="[logfile]" />
                        </Sequence>
                      </If.Then>
                      <If.Else>
                        <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_6" Line="Vat line added" Source="[logfile]" />
                      </If.Else>
                    </If>
                  </If.Then>
                  <If.Else>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_7">
                      <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_7" Line="Error while adding the Vat line" Source="[logfile]" />
                    </Sequence>
                  </If.Else>
                </If>
              </TryCatch.Try>
              <TryCatch.Catches>
                <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_1">
                  <ActivityAction x:TypeArguments="s:Exception">
                    <ActivityAction.Argument>
                      <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                    </ActivityAction.Argument>
                    <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_4" Line="Exception while adding Vat line" Source="[logfile]" />
                  </ActivityAction>
                </Catch>
              </TryCatch.Catches>
            </TryCatch>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_17">
              <Assign.To>
                <OutArgument x:TypeArguments="x:Int32">[Itr]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:Int32">[Itr+1]</InArgument>
              </Assign.Value>
            </Assign>
          </Sequence>
        </While>
      </If.Then>
      <If.Else>
        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_11">
          <Assign sap2010:WorkflowViewState.IdRef="Assign_26">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[vatAmt]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[vat]</InArgument>
            </Assign.Value>
          </Assign>
          <ias:Template_Apply ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Templating Activity" sap2010:WorkflowViewState.IdRef="Template_Apply_6" Template="{}{ 'parameters': {  'AccountStartsWith':'{{%AccountStartsWith%}}','VatPercentage': '{{%VatPercentage%}}' ,'CountryCode': '{{%CountryCode%}}','invoiceType': '{{%invoiceType%}}' } }" Text="[brfomat]">
            <ias:Template_Apply.Values>
              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>VatPercentage</x:String>
                  <x:String>CountryCode</x:String>
                  <x:String>invoiceType</x:String>
                  <x:String>AccountStartsWith</x:String>
                </scg:List>
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>VatPercentage</x:String>
                  <x:String>CountryCode</x:String>
                  <x:String>invoiceType</x:String>
                  <x:String>AccountStartsWith</x:String>
                </scg:List>
              </scg:List>
            </ias:Template_Apply.Values>
          </ias:Template_Apply>
          <ias:DeserializeJSON ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Deserialize JSON" sap2010:WorkflowViewState.IdRef="DeserializeJSON_5" JTokenObject="[brDesOpt]" JTokenString="[brFomat]" />
          <iai:IONAPIRequestWizard FileAttachments="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request" HttpMethod="POST" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_9" PostData="[brDesOpt.toString()]" Response="[respOutput]" StatusCode="[brRespStatus]" Url="[tenantID+&quot;IONSERVICES/businessrules/decision/execute/&quot;+&quot;VatCodeConfiguration&quot;]">
            <iai:IONAPIRequestWizard.Headers>
              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                <scg:List x:TypeArguments="x:String" Capacity="0" />
                <scg:List x:TypeArguments="x:String" Capacity="0" />
              </scg:List>
            </iai:IONAPIRequestWizard.Headers>
            <iai:IONAPIRequestWizard.QueryParameters>
              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>matrixName</x:String>
                </scg:List>
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>VatCodeConfiguration</x:String>
                </scg:List>
              </scg:List>
            </iai:IONAPIRequestWizard.QueryParameters>
          </iai:IONAPIRequestWizard>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_30">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[vatCode]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[JToken.Parse(respOutput.ReadAsText)("parameters")("VatCode").ToString]</InArgument>
            </Assign.Value>
          </Assign>
          <If Condition="[vatCode = &quot;NA&quot;]" sap2010:WorkflowViewState.IdRef="If_14">
            <If.Then>
              <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding VAT" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_10" Response="[respout1]" ResponseCode="[respout]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                <iai:IONAPIRequestWizard.Headers>
                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                    <scg:List x:TypeArguments="x:String" Capacity="4">
                      <x:String>Accept</x:String>
                    </scg:List>
                    <scg:List x:TypeArguments="x:String" Capacity="4">
                      <x:String>application/json</x:String>
                    </scg:List>
                  </scg:List>
                </iai:IONAPIRequestWizard.Headers>
                <iai:IONAPIRequestWizard.QueryParameters>
                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                    <scg:List x:TypeArguments="x:String" Capacity="4">
                      <x:String>INBN</x:String>
                      <x:String>RDTP</x:String>
                      <x:String>DIVI</x:String>
                      <x:String>GLAM</x:String>
                    </scg:List>
                    <scg:List x:TypeArguments="x:String" Capacity="4">
                      <x:String>inbnValue</x:String>
                      <x:String>3</x:String>
                      <x:String>division</x:String>
                      <x:String>vatAmt</x:String>
                    </scg:List>
                  </scg:List>
                </iai:IONAPIRequestWizard.QueryParameters>
              </iai:IONAPIRequestWizard>
            </If.Then>
            <If.Else>
              <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding VAT" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_11" Response="[respout1]" ResponseCode="[respout]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                <iai:IONAPIRequestWizard.Headers>
                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                    <scg:List x:TypeArguments="x:String" Capacity="4">
                      <x:String>Accept</x:String>
                    </scg:List>
                    <scg:List x:TypeArguments="x:String" Capacity="4">
                      <x:String>application/json</x:String>
                    </scg:List>
                  </scg:List>
                </iai:IONAPIRequestWizard.Headers>
                <iai:IONAPIRequestWizard.QueryParameters>
                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                    <scg:List x:TypeArguments="x:String" Capacity="8">
                      <x:String>INBN</x:String>
                      <x:String>RDTP</x:String>
                      <x:String>DIVI</x:String>
                      <x:String>VTA1</x:String>
                      <x:String>VTCD</x:String>
                    </scg:List>
                    <scg:List x:TypeArguments="x:String" Capacity="8">
                      <x:String>inbnValue</x:String>
                      <x:String>3</x:String>
                      <x:String>division</x:String>
                      <x:String>vatAmt</x:String>
                      <x:String>vatCode</x:String>
                    </scg:List>
                  </scg:List>
                </iai:IONAPIRequestWizard.QueryParameters>
              </iai:IONAPIRequestWizard>
            </If.Else>
          </If>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_31">
            <Assign.To>
              <OutArgument x:TypeArguments="njl:JToken">[out5]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respout1.ReadAsText)]</InArgument>
            </Assign.Value>
          </Assign>
          <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_4">
            <TryCatch.Try>
              <If Condition="[respout = 200]" sap2010:WorkflowViewState.IdRef="If_16">
                <If.Then>
                  <If Condition="[out5(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_15">
                    <If.Then>
                      <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_12" Line="[(out5(&quot;results&quot;)(0)(&quot;errorMessage&quot;)).ToString]" Source="[logfile]" />
                    </If.Then>
                    <If.Else>
                      <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_9" Line="Vat line added" Source="[logfile]" />
                    </If.Else>
                  </If>
                </If.Then>
                <If.Else>
                  <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_13" Line="Error while adding the Vat line" Source="[logfile]" />
                </If.Else>
              </If>
            </TryCatch.Try>
            <TryCatch.Catches>
              <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_4">
                <ActivityAction x:TypeArguments="s:Exception">
                  <ActivityAction.Argument>
                    <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                  </ActivityAction.Argument>
                  <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_11" Line="Exception while adding Vat line" Source="[logfile]" />
                </ActivityAction>
              </Catch>
            </TryCatch.Catches>
          </TryCatch>
        </Sequence>
      </If.Else>
    </If>
    <sads:DebugSymbol.Symbol>d1xDOlxVc2Vyc1xza2F2aXJheWFuaVxBcHBEYXRhXExvY2FsXEluZm9yUlBBXE0zSW52b2ljZVByb2Nlc3NpbmdHZW5BSVYzXHZhdENvbmZpZ3VyYXRpb24ueGFtbIIBTAP7Aw4CAQFXM1c2AgECXAVjDgMBvAFkBWsOAwG3AWwFiAEQAwGtAYkBBfkDCgIBA2EwYV0DAb8BXjFeRAMBvQFpMGk/AwG6AWYxZj0DAbgBbgl1EgMBsgF9DYQBFgMBrgGJAROJASUCAQSLAQnZAhECAUzcAgn3AxQCAQZzM3NLAwG1AXA0cD8DAbMBggE3ggE6AwGxAX84f0MDAa8BjAEL2AIWAgFQiwFEiwFaAgFN3QIL5AIUAgFH5QIL9gIgAgFD9wIL9wLUAQIBPvgCC4kDJQIBNooDC5EDFAIBMpIDC9EDEAIBIdIDC9kDFAIBHdoDC/YDFgIBB40BDZQBFgMBpwGVAQ2cARYDAaEBnQENuQEYAwGYAboBDcsBIgMBlAHMAQ3MAdYBAwGPAc0BDd4BJwMBhwHfAQ3mARYDAYMB5wENpgISAgFypwINrgIWAgFurwINzwIYAgFW0AIN1wIWAgFR4gI24gI7AgFK3wI33wI/AgFI5QLlAuUC8AICAUXlAqkB5QLfAgIBRPcCxgH3AtEBAgFB9wKsAfcCuAECAT/4Av0B+AKUAgIBPfgCngL4AqwCAgE7+ALNAvgCsgMCATn4ArgC+ALIAgIBN48DNo8DfQIBNYwDN4wDQAIBM5IDGZIDNQIBIpQDD68DKQIBK7IDD88DKQIBJNcDONcDWwIBINQDOdQDPwIBHtwDD+oDFAIBDPIDE/ID1wECAQiSATiSAU4DAaoBjwE5jwFBAwGoAZoBOJoBVQMBpAGXATmXAT4DAaIBnwERpgEaAwGdAa4BFbUBHgMBmQG6AecCugHyAgMBlgG6AasBugHhAgMBlQHMAcgBzAHTAQMBkgHMAa4BzAG6AQMBkAHNAf8BzQGWAgMBjgHNAaACzQGuAgMBjAHNAc8CzQG0AwMBigHNAboCzQHKAgMBiAHkATjkAX8DAYYB4QE54QFCAwGEAecBG+cBNwIBc+kBEYQCKwIBfIcCEaQCKwIBdawCOqwCXQIBcakCO6kCQQIBb7ECEcMCFgIBW8sCFcsC2AECAVfVAjfVAj4CAVTSAjjSAj0CAVKUA7wClAPHAgIBMJQDogKUA64CAgEulAPMApQDjwMCASyyA7wCsgPHAgIBKbIDogKyA64CAgEnsgPMArIDjwMCASXcAx3cAy4CAQ3eAxPlAxgCARPoAxPoA9cBAgEP8gOgAfIDwQECAQvyA8kB8gPUAQIBCaQBPKQBewMBoAGhAT2hAUwDAZ4BswFAswFBAwGcAbABQbABUAMBmgHpAb0C6QHIAgMBgQHpAaMC6QGvAgIBf+kBzQLpAZADAgF9hwK9AocCyAICAXqHAqMChwKvAgIBeIcCzQKHApADAgF2sQIfsQIwAgFcswIVvAIaAgFjvwIVwQIgAgFeywKhAcsCwgECAVrLAsoBywLVAQIBWN4DId4DcAIBFOADF+AD/wECARnjAxfjA8kBAgEV6AOgAegDwQECARLoA8kB6APUAQIBELMCI7MCcgIBZLUCGbcCJAIBaboCGboCywECAWXAAhfAAtoBAgFf4AOkAeAD6QECARzgA/EB4AP8AQIBGuMDowHjA7MBAgEY4wO7AeMDxgECARa2Ahu2AoICAgFqugKlAboCtQECAWi6Ar0BugLIAQIBZsACowHAAsQBAgFiwALMAcAC1wECAWC2AqcBtgLsAQIBbbYC9AG2Av8BAgFr</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="Assign_24" sap:VirtualizedContainerService.HintSize="1023,60" />
      <sap2010:ViewStateData Id="Assign_18" sap:VirtualizedContainerService.HintSize="1023,60" />
      <sap2010:ViewStateData Id="Assign_15" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_32" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Catch`1_5" sap:VirtualizedContainerService.HintSize="404,21">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_5" sap:VirtualizedContainerService.HintSize="1023,287">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_16" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="Assign_19" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="Assign_5" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_25" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Catch`1_2" sap:VirtualizedContainerService.HintSize="404,21">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_2" sap:VirtualizedContainerService.HintSize="464,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Template_Apply_5" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="DeserializeJSON_4" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_5" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Assign_21" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_7" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_8" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_12" sap:VirtualizedContainerService.HintSize="464,208">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_12" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="Append_Line_5" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Sequence_6" sap:VirtualizedContainerService.HintSize="222,146">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_6" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_6" sap:VirtualizedContainerService.HintSize="464,294" />
      <sap2010:ViewStateData Id="Append_Line_7" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Sequence_7" sap:VirtualizedContainerService.HintSize="222,146">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_7" sap:VirtualizedContainerService.HintSize="711,442">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_4" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Catch`1_1" sap:VirtualizedContainerService.HintSize="716.666666666667,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_1" sap:VirtualizedContainerService.HintSize="464,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_17" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="Sequence_8" sap:VirtualizedContainerService.HintSize="486,1200">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="While_1" sap:VirtualizedContainerService.HintSize="512,1358">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_26" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="Template_Apply_6" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="DeserializeJSON_5" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_9" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Assign_30" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_10" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_11" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_14" sap:VirtualizedContainerService.HintSize="464,208">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_31" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="Append_Line_12" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Append_Line_9" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_15" sap:VirtualizedContainerService.HintSize="464,208" />
      <sap2010:ViewStateData Id="Append_Line_13" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_16" sap:VirtualizedContainerService.HintSize="689,356">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_11" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Catch`1_4" sap:VirtualizedContainerService.HintSize="404,21">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_4" sap:VirtualizedContainerService.HintSize="464,287">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_11" sap:VirtualizedContainerService.HintSize="486,1145">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_13" sap:VirtualizedContainerService.HintSize="1023,1506" />
      <sap2010:ViewStateData Id="Sequence_2" sap:VirtualizedContainerService.HintSize="1045,2157">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="1085,2237" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>