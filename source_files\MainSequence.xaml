<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow" this:Workflow.configurationFolder="C:\M3VendorInvoiceProcessing" this:Workflow.emailAccount="<EMAIL>" this:Workflow.emailFolder="M3" this:Workflow.numberOfEmails="10" this:Workflow.userIdentifier="29f1d737-8b29-4961-a9ef-1d2bd9210972" this:Workflow.distributionType="USER" this:Workflow.enableMessageBoxes="True" this:Workflow.division="FROM_BILL_TO" this:Workflow.tenantID="https://mingle-ionapi.inforcloudsuite.com/MANDALA_DEM/" this:Workflow.projectPath="[Environment.GetFolderPath(Environment.SpecialFolder.UserProfile) + &quot;\AppData\Local\InforRPA\M3InvoiceProcessingGenAIV3&quot;]" this:Workflow.datalakeAPILogicalId="infor.ims.imsfromrpastudio" this:Workflow.handleCashDiscount="True" this:Workflow.Match3Way="True"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:iad="clr-namespace:Infor.Activities.Debug;assembly=Infor.Activities.Debug"
 xmlns:iae="clr-namespace:Infor.Activities.Excel;assembly=Infor.Activities.Excel"
 xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI"
 xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys"
 xmlns:iaw="clr-namespace:Infor.Activities.Workflow;assembly=Infor.Activities.Workflow"
 xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities"
 xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json"
 xmlns:s="clr-namespace:System;assembly=mscorlib"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib"
 xmlns:sd="clr-namespace:System.Data;assembly=System.Data"
 xmlns:this="clr-namespace:RehostedWorkflowDesigner"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="configurationFolder" Type="InArgument(x:String)" />
    <x:Property Name="emailAccount" Type="InArgument(x:String)" />
    <x:Property Name="emailFolder" Type="InArgument(x:String)" />
    <x:Property Name="numberOfEmails" Type="InArgument(x:Int32)" />
    <x:Property Name="userIdentifier" Type="InArgument(x:String)" />
    <x:Property Name="distributionType" Type="InArgument(x:String)" />
    <x:Property Name="enableMessageBoxes" Type="InArgument(x:Boolean)" />
    <x:Property Name="division" Type="InArgument(x:String)" />
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="projectPath" Type="InArgument(x:String)" />
    <x:Property Name="datalakeAPILogicalId" Type="InArgument(x:String)" />
    <x:Property Name="handleCashDiscount" Type="InArgument(x:Boolean)" />
    <x:Property Name="Match3Way" Type="InArgument(x:Boolean)" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Infor.Activities.Workflow</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>System</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>Microsoft.VisualBasic.CompilerServices</x:String>
      <x:String>System.Linq</x:String>
      <x:String>System.Globalization</x:String>
      <x:String>Infor.RPA.Utilities</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>System.Text.RegularExpressions</x:String>
      <x:String>System.Data</x:String>
      <x:String>System.Xml.Serialization</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>Infor.Activities.Workflow</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
      <AssemblyReference>Infor.RPA.Utilities</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
      <AssemblyReference>System.Collections.Immutable</AssemblyReference>
      <AssemblyReference>System.Data</AssemblyReference>
      <AssemblyReference>System.Xml</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <TryCatch DisplayName="TryCatch - MainSequence" sap2010:WorkflowViewState.IdRef="TryCatch_3">
    <TryCatch.Variables>
      <Variable x:TypeArguments="x:String" Name="logFile" />
      <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="CDOutput" />
      <Variable x:TypeArguments="x:String" Name="model" />
      <Variable x:TypeArguments="x:String" Name="GenAIModel" />
      <Variable x:TypeArguments="x:String" Name="GenAIModelVersion" />
      <Variable x:TypeArguments="scg:Dictionary(x:String, x:String)" Default="[New Dictionary (Of String, String)]" Name="PromptsDicitonary" />
      <Variable x:TypeArguments="sd:DataTable" Name="dtConfig" />
      <Variable x:TypeArguments="x:String" Name="version" />
    </TryCatch.Variables>
    <TryCatch.Try>
      <Sequence DisplayName="MainSequence" sap2010:WorkflowViewState.IdRef="Sequence_30">
        <Sequence.Variables>
          <Variable x:TypeArguments="x:Int32" Name="datalakeResponseCode" />
          <Variable x:TypeArguments="scg:Dictionary(x:String, x:Object)" Default="[New Dictionary(Of String, Object)]" Name="miscValues" />
          <Variable x:TypeArguments="x:Boolean" Name="GenAIPromptFileExists" />
          <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="GenAIOutput" />
          <Variable x:TypeArguments="x:String" Name="GenAIStatus" />
          <Variable x:TypeArguments="njl:JToken" Name="value" />
          <Variable x:TypeArguments="iru:ResponseObject" Name="resp" />
          <Variable x:TypeArguments="scg:List(x:String)" Name="files" />
          <Variable x:TypeArguments="x:Boolean" Name="logFileExist" />
          <Variable x:TypeArguments="x:Int32" Default="5" Name="numberOfParts" />
          <Variable x:TypeArguments="scg:List(x:String)" Default="[New List(Of String)]" Name="ListPromptFiles" />
        </Sequence.Variables>
        <Sequence DisplayName="Download Config File" sap2010:WorkflowViewState.IdRef="Sequence_32">
          <Sequence.Variables>
            <Variable x:TypeArguments="x:Int32" Name="intCounter" />
            <Variable x:TypeArguments="iru:ResponseObject" Name="GenAIresponse" />
            <Variable x:TypeArguments="x:Int32" Name="GenAIresponseCode" />
            <Variable x:TypeArguments="njl:JToken" Name="GenAIrespToken" />
          </Sequence.Variables>
          <ias:File_Delete ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Delete File" sap2010:WorkflowViewState.IdRef="File_Delete_13" Source="[ConfigurationFolder + &quot;\RPAConfig.xlsx&quot;]" />
          <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" QueryParameters="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request to download RPAConfig File" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_3" Response="[GenAIresponse]" StatusCode="[GenAIresponseCode]" Url="[tenantID+&quot;IDM/api/items/search/item?%24query=%2FGenAIPrompt%5B%40RESOURCENAME%20%3D%20%22RPAConfig.xlsx%22%5D%20SORTBY(%40LASTCHANGEDTS%20DESCENDING)&quot;]">
            <iai:IONAPIRequestWizard.Headers>
              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>Accept</x:String>
                </scg:List>
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>application/json</x:String>
                </scg:List>
              </scg:List>
            </iai:IONAPIRequestWizard.Headers>
          </iai:IONAPIRequestWizard>
          <If Condition="[GenAIresponseCode=200]" DisplayName="Check If ION API Success" sap2010:WorkflowViewState.IdRef="If_36">
            <If.Then>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_33">
                <Sequence.Variables>
                  <Variable x:TypeArguments="x:String" Name="variable2" />
                  <Variable x:TypeArguments="njl:JToken" Name="version" />
                  <Variable x:TypeArguments="njl:JToken" Name="GenAIPromptUrl" />
                  <Variable x:TypeArguments="x:String" Name="url" />
                  <Variable x:TypeArguments="x:String" Name="ConfigPath" />
                </Sequence.Variables>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_37">
                  <Assign.To>
                    <OutArgument x:TypeArguments="njl:JToken">[GenAIrespToken]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="njl:JToken">[GenAIresponse.readasjson]</InArgument>
                  </Assign.Value>
                </Assign>
                <ias:JQTransform ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="JQ Transform" sap2010:WorkflowViewState.IdRef="JQTransform_3" JQ=".key|.item|.resrs|.res[0]|.url" JSON="[GenAIPromptUrl]" Raw="False">
                  <ias:JQTransform.Text>
                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>key</x:String>
                      </scg:List>
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>GenAIrespToken</x:String>
                      </scg:List>
                    </scg:List>
                  </ias:JQTransform.Text>
                </ias:JQTransform>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_42">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[url]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[GenAIPromptUrl.tostring]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_43">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[url]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[url.trim().substring(1,url.length()-2)]</InArgument>
                  </Assign.Value>
                </Assign>
                <ias:DownloadFile_URL ErrorCode="{x:Null}" Async="False" ContinueOnError="True" DisplayName="Download File By URL" sap2010:WorkflowViewState.IdRef="DownloadFile_URL_1" Name="RPAConfig.xlsx" OutputFile="[ConfigPath]" Target="[configurationFolder]" URL="[url]" />
              </Sequence>
            </If.Then>
            <If.Else>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_34">
                <Assign sap2010:WorkflowViewState.IdRef="Assign_44">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[GenAIStatus]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">failure</InArgument>
                  </Assign.Value>
                </Assign>
                <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" Title="{x:Null}" ContinueOnError="True" DisplayName="Message Box" sap2010:WorkflowViewState.IdRef="MessageBox_3" Selection="OK" Text="[&quot;Exception raised while downloading Gen AI Prompt file:&quot;+GenAIresponse.readAsText]" />
                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_24" Line="[&quot;Exception raised while downloading Gen AI Prompt file:&quot;+GenAIresponse.readAsText]" Source="[logFile]" />
              </Sequence>
            </If.Else>
          </If>
          <iae:ReadRange ErrorCode="{x:Null}" ContinueOnError="False" DataTable="[dtConfig]" DisplayName="Read Range" HasHeaderRow="True" sap2010:WorkflowViewState.IdRef="ReadRange_1" WorkbookPath="[ConfigurationFolder + &quot;\RPAConfig.xlsx&quot;]" WorksheetName="ProcessArguments">
            <iae:ReadRange.CellRange>
              <InArgument x:TypeArguments="x:String">
                <Literal x:TypeArguments="x:String" Value="" />
              </InArgument>
            </iae:ReadRange.CellRange>
          </iae:ReadRange>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_32">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Int32">[intCounter]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:Int32">0</InArgument>
            </Assign.Value>
          </Assign>
          <ForEach x:TypeArguments="x:Int32" DisplayName="ForEach Config Row" sap2010:WorkflowViewState.IdRef="ForEach`1_2" Values="[Enumerable.Range(intCounter,dtConfig.Rows.Count)]">
            <ActivityAction x:TypeArguments="x:Int32">
              <ActivityAction.Argument>
                <DelegateInArgument x:TypeArguments="x:Int32" Name="item" />
              </ActivityAction.Argument>
              <Switch x:TypeArguments="x:String" DisplayName="Assign Key and Values" Expression="[dtConfig.Rows(item)(2).ToString]" sap2010:WorkflowViewState.IdRef="Switch`1_4">
                <Sequence x:Key="Boolean" DisplayName="Boolean Sequence" sap2010:WorkflowViewState.IdRef="Sequence_39">
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_52">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:Object">[miscValues(dtConfig.Rows(item)(0).ToString)]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:Boolean">[CType(dtConfig.Rows(item)(1).ToString,Boolean)]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_53">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:Int32">[intCounter]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:Int32">[intCounter+1]</InArgument>
                    </Assign.Value>
                  </Assign>
                </Sequence>
                <Sequence x:Key="String" DisplayName="String Sequence" sap2010:WorkflowViewState.IdRef="Sequence_40">
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_54">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:Object">[miscValues(dtConfig.Rows(item)(0).ToString)]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[CType(dtConfig.Rows(item)(1).ToString,String)]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_55">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:Int32">[intCounter]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:Int32">[intCounter+1]</InArgument>
                    </Assign.Value>
                  </Assign>
                </Sequence>
                <Sequence x:Key="Int32" DisplayName="Int32 Sequence" sap2010:WorkflowViewState.IdRef="Sequence_41">
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_56">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:Object">[miscValues(dtConfig.Rows(item)(0).ToString)]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:Int32">[CType(dtConfig.Rows(item)(1).ToString,Integer)]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_57">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:Int32">[intCounter]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:Int32">[intCounter+1]</InArgument>
                    </Assign.Value>
                  </Assign>
                </Sequence>
              </Switch>
            </ActivityAction>
          </ForEach>
        </Sequence>
        <iaw:InvokeWorkflow ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;configurationFolder&quot;,configurationFolder},{&quot;directoriesNames&quot;,miscValues(&quot;directoriesNames&quot;).ToString.split(&quot;|&quot;c).ToList},{&quot;logFolderName&quot;,miscValues(&quot;logFolderName&quot;).ToString}}]" ContinueOnError="False" DisplayName="CreateDirectories Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_18" OutputArguments="[CDOutput]" WorkflowFile="[projectPath+&quot;\CreateDirectories.xaml&quot;]" />
        <Assign sap2010:WorkflowViewState.IdRef="Assign_21">
          <Assign.To>
            <OutArgument x:TypeArguments="x:String">[logFile]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:String">[CDOutput("logFile").ToString]</InArgument>
          </Assign.Value>
        </Assign>
        <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Empty Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_7" Line="------------------------------------------------------------------------------------------------" Source="[logFile]" />
        <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Start time Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_8" Line="[&quot;Start Time : &quot; +System.DateTime.Now.ToString(&quot;yyyy/MM/dd HH:mm:ss&quot;)]" Source="[logFile]" />
        <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="CreateDirectories Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_19" Line="CreateDirectories Workflow execution completed." Source="[logFile]" />
        <TryCatch DisplayName="TryCatch - ION API file" sap2010:WorkflowViewState.IdRef="TryCatch_2">
          <TryCatch.Try>
            <iai:IONAPIRequestWizard FileAttachments="{x:Null}" Headers="{x:Null}" PostData="{x:Null}" QueryParameters="{x:Null}" ResponseCode="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="False" DisplayName="IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_2" Response="[resp]" Url="[miscValues(&quot;IONAPI&quot;).ToString]" />
          </TryCatch.Try>
          <TryCatch.Catches>
            <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_2">
              <ActivityAction x:TypeArguments="s:Exception">
                <ActivityAction.Argument>
                  <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                </ActivityAction.Argument>
                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line Catch Block IFS ION API" sap2010:WorkflowViewState.IdRef="Append_Line_9" Line="ION API connection is missing." Source="[logFile]" />
              </ActivityAction>
            </Catch>
          </TryCatch.Catches>
        </TryCatch>
        <Assign DisplayName="AuthUser Assign" sap2010:WorkflowViewState.IdRef="Assign_45">
          <Assign.To>
            <OutArgument x:TypeArguments="x:Object">[miscValues("authUser")]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:String">[If(miscValues("authUser").ToString="NA","",miscValues("authUser").ToString)]</InArgument>
          </Assign.Value>
        </Assign>
        <Assign DisplayName="handleCashDiscount Assign" sap2010:WorkflowViewState.IdRef="Assign_71">
          <Assign.To>
            <OutArgument x:TypeArguments="x:Object">[miscValues("handleCashDiscount")]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:Boolean">[handleCashDiscount]</InArgument>
          </Assign.Value>
        </Assign>
        <Sequence DisplayName="PromptsDicitonary Sequence" sap2010:WorkflowViewState.IdRef="Sequence_31">
          <Assign sap2010:WorkflowViewState.IdRef="Assign_29">
            <Assign.To>
              <OutArgument x:TypeArguments="scg:List(x:String)">[ListPromptFiles]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="scg:List(x:String)">[miscValues("strPromptFilesMapping").ToString.Split("|"c).Select(Function(x) x.Split(";"c)(0)).ToList]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_30">
            <Assign.To>
              <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:String)">[PromptsDicitonary]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="scg:Dictionary(x:String, x:String)" xml:space="preserve">[miscValues("strPromptFilesMapping").ToString.Split("|"c).
    Select(Function(part) part.Split(";"c)).
    ToDictionary(Function(parts) parts(0), Function(parts) parts(1))]</InArgument>
            </Assign.Value>
          </Assign>
        </Sequence>
        <Switch x:TypeArguments="x:Boolean" DisplayName="IfUseGenAIExtraction - Get Prompt Files" Expression="[CType(miscValues(&quot;useGenAIExtraction&quot;).ToString,Boolean)]" sap2010:WorkflowViewState.IdRef="Switch`1_1">
          <ForEach x:TypeArguments="x:String" x:Key="True" DisplayName="ForEach prompt file" sap2010:WorkflowViewState.IdRef="ForEach`1_1" Values="[ListPromptFiles]">
            <ActivityAction x:TypeArguments="x:String">
              <ActivityAction.Argument>
                <DelegateInArgument x:TypeArguments="x:String" Name="promptfile" />
              </ActivityAction.Argument>
              <Sequence DisplayName="get Prompt Files - Sequence" sap2010:WorkflowViewState.IdRef="Sequence_25">
                <Sequence.Variables>
                  <Variable x:TypeArguments="x:String" Name="GenAIPromptFileURL" />
                  <Variable x:TypeArguments="x:String" Name="strFIleName" />
                </Sequence.Variables>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_31">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[strFIleName]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[PromptsDicitonary(promptfile)]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign DisplayName="Assign Updated" sap2010:WorkflowViewState.IdRef="Assign_22">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[GenAIPromptFileURL]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[tenantID+"IDM/api/items/search/item?%24query=%2FGenAIPrompt%5B%40RESOURCENAME%20%3D%20%22" + promptfile + ".txt%22%5D%20SORTBY(%40LASTCHANGEDTS%20DESCENDING)"]</InArgument>
                  </Assign.Value>
                </Assign>
                <ias:File_Delete ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Delete File" sap2010:WorkflowViewState.IdRef="File_Delete_12" Source="[configurationFolder+&quot;\&quot;+promptfile+&quot;.txt&quot;]" />
                <iaw:InvokeWorkflow ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;configurationFolder&quot;,configurationFolder},{&quot;logFile&quot;,logFile},{&quot;GenAIPromptFileURL&quot;,GenAIPromptFileURL},{&quot;fileName&quot;,strFIleName}}]" ContinueOnError="False" DisplayName="Invoke getGenAIPrompt download" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_19" OutputArguments="[GenAIOutput]" WorkflowFile="[projectPath+&quot;\getGenAIPrompt.xaml&quot;]" />
                <Assign sap2010:WorkflowViewState.IdRef="Assign_20">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[GenAIStatus]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[CType(GenAIOutput("GenAIStatus"), String)]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_24">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[model]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[CType(GenAIOutput("GenAIModel"), String)]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_25">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[version]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[CType(GenAIOutput("GenAIModelVersion"), String)]</InArgument>
                  </Assign.Value>
                </Assign>
                <If Condition="[GenAIModel = &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_34">
                  <If.Then>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_26">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[GenAIModel]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">[CType(GenAIOutput("GenAIModel"), String)]</InArgument>
                      </Assign.Value>
                    </Assign>
                  </If.Then>
                </If>
                <If Condition="[GenAIModelVersion = &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_35">
                  <If.Then>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_27">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[GenAIModelVersion]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">[CType(GenAIOutput("GenAIModelVersion"), String)]</InArgument>
                      </Assign.Value>
                    </Assign>
                  </If.Then>
                </If>
              </Sequence>
            </ActivityAction>
          </ForEach>
          <ias:Append_Line ErrorCode="{x:Null}" x:Key="False" ContinueOnError="True" DisplayName="Log useGenAIExtraction False" sap2010:WorkflowViewState.IdRef="Append_Line_10" Line="[&quot;useGenAIExtraction value is &quot;+miscValues(&quot;useGenAIExtraction&quot;).ToString+Environment.NewLine+&quot;notificationFailureCount Prompt file is downloaded.&quot;]" Source="[logFile]" />
        </Switch>
        <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Log useGenAIExtraction Status" sap2010:WorkflowViewState.IdRef="Append_Line_11" Line="[&quot;GenAIStatus (Prompt files download) value is &quot;+GenAIStatus.ToString]" Source="[logFile]" />
        <Sequence DisplayName="Add Values to miscValues" sap2010:WorkflowViewState.IdRef="Sequence_42">
          <Assign DisplayName="GenAIModel Assign" sap2010:WorkflowViewState.IdRef="Assign_58">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Object">[miscValues("GenAIModel")]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[GenAIModel]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="GenAIModelVersion Assign" sap2010:WorkflowViewState.IdRef="Assign_60">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Object">[miscValues("GenAIModelVersion")]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:Object">[GenAIModelVersion]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="Assign configurationFolder" sap2010:WorkflowViewState.IdRef="Assign_61">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Object">[miscValues("configurationFolder")]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:Object">[configurationFolder]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="Assign emailAccount" sap2010:WorkflowViewState.IdRef="Assign_62">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Object">[miscValues("emailAccount")]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:Object">[emailAccount]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="Assign emailFolder" sap2010:WorkflowViewState.IdRef="Assign_63">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Object">[miscValues("emailFolder")]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:Object">[emailFolder]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="Assign numberOfEmails" sap2010:WorkflowViewState.IdRef="Assign_64">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Object">[miscValues("numberOfEmails")]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:Object">[numberOfEmails]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="Assign userIdentifier" sap2010:WorkflowViewState.IdRef="Assign_65">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Object">[miscValues("userIdentifier")]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:Object">[userIdentifier]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="Assign distributionType" sap2010:WorkflowViewState.IdRef="Assign_66">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Object">[miscValues("distributionType")]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:Object">[distributionType]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="Assign division" sap2010:WorkflowViewState.IdRef="Assign_67">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Object">[miscValues("division")]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:Object">[division]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="Assign tenantID" sap2010:WorkflowViewState.IdRef="Assign_68">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Object">[miscValues("tenantID")]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:Object">[tenantID]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="Assign projectPath" sap2010:WorkflowViewState.IdRef="Assign_69">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Object">[miscValues("projectPath")]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:Object">[projectPath]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="Assign datalakeAPILogicalId" sap2010:WorkflowViewState.IdRef="Assign_70">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Object">[miscValues("datalakeAPILogicalId")]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:Object">[datalakeAPILogicalId]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="Assign Match3Way" sap2010:WorkflowViewState.IdRef="Assign_72">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Object">[miscValues("Match3Way")]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:Object">[Match3Way]</InArgument>
            </Assign.Value>
          </Assign>
        </Sequence>
        <iad:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_1">
          <iad:CommentOut.Activities>
            <Assign DisplayName="Assign - Misc Values" sap2010:WorkflowViewState.IdRef="Assign_23">
              <Assign.To>
                <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[miscValues]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)" xml:space="preserve">[New Dictionary(Of String, Object) From {
    {"ERP", ERP},
    {"useGenAIExtraction", useGenAIExtraction},
    {"processDeliveryNote", processDeliveryNote},
	{"businessRuleForTolerance", businessRuleForTolerance},
    {"businessRuleForGUID", businessRuleForGUID},
	{"division", division},
    {"company", company},
    {"includeDistribution", includeDistribution},
	{"businessRuleForDivisionGLCode", businessRuleForDivisionGLCode},
    {"useBusinessRuleForTelerance", useBusinessRuleForTelerance},
	{"enableStagingForExpenseInException",enableStagingForExpenseInException},
	{"mandateGEOC",mandateGEOC},
	{"SplittingDoc",SplittingDoc},
	{"groupByTransDate",groupByTransDate},
	{"createInvoiceIrrespectiveTolerance",createInvoiceIrrespectiveTolerance},
	{"autoAllocateOpenLines",autoAllocateOpenLines},
	{"AutomateValidation",AutomateValidation},
	{"vatCodeConfig",vatCodeConfig},
	{"AutomateApproval",AutomateApproval},
	{"SendEmail",SendEmail},
	{"BusinessRuleAPResp",BusinessRuleAPResp},
	{"GenAIModel",GenAIModel},
	{"GenAIModelVersion",GenAIModelVersion}
}]</InArgument>
              </Assign.Value>
            </Assign>
          </iad:CommentOut.Activities>
        </iad:CommentOut>
        <If Condition="[Ctype(miscValues(&quot;processEmails&quot;).ToString,Boolean) and (miscValues(&quot;invoiceSource&quot;).ToString =&quot;OutlookClientEmail&quot; or  miscValues(&quot;invoiceSource&quot;).ToString =&quot;OutlookGraphEmail&quot;)]" DisplayName="IfProcessEmails" sap2010:WorkflowViewState.IdRef="If_30">
          <If.Then>
            <iaw:InvokeWorkflow OutputArguments="{x:Null}" ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;configurationFolder&quot;,configurationFolder},{&quot;emailAccount&quot;,emailAccount},{&quot;emailFolder&quot;,emailFolder},{&quot;numberOfEmails&quot;,numberOfEmails},{&quot;logFile&quot;,logFile},{&quot;tenantID&quot;,tenantID},&#xA;{&quot;colemanAPI&quot;,miscValues(&quot;colemanAPI&quot;).ToString},{&quot;userIdentifier&quot;,userIdentifier},{&quot;distributionType&quot;,distributionType},{&quot;projectPath&quot;,projectPath},{&quot;enableMessageBoxes&quot;,enableMessageBoxes},&#xA;{&quot;poFilterValues&quot;,miscValues(&quot;poFilterValues&quot;).ToString},{&quot;poFilterCondition&quot;,miscValues(&quot;poFilterCondition&quot;).ToString},{&quot;datalakeAPILogicalId&quot;,miscValues(&quot;datalakeAPILogicalId&quot;).ToString},&#xA;{&quot;chargeCode&quot;,miscValues(&quot;chargeCode&quot;).ToString},{&quot;discountCode&quot;,miscValues(&quot;discountCode&quot;).ToString},{&quot;authUser&quot;,miscValues(&quot;authUser&quot;).ToString},{&quot;imsAPIUrl&quot;,miscValues(&quot;imsAPIUrl&quot;).ToString},&#xA;{&quot;extractNumericFromPO&quot;,CType(miscValues(&quot;extractNumericFromPO&quot;).ToString,Boolean)},{&quot;vatCodeConfig&quot;,miscValues(&quot;vatCodeConfig&quot;).ToString},&#xA;{&quot;poDiscountsHandlingConfig&quot;,CType(miscValues(&quot;poDiscountsHandlingConfig&quot;).ToString,Boolean)},{&quot;matchVendorItemCode&quot;,CType(miscValues(&quot;matchVendorItemCode&quot;).ToString,Boolean)},&#xA;{&quot;invoiceSource&quot;,miscValues(&quot;invoiceSource&quot;).ToString},{&quot;extractFromWidgetDatalake&quot;,CType(miscValues(&quot;extractFromWidgetDatalake&quot;).ToString,Boolean)},&#xA;{&quot;approvalRequired&quot;,CType(miscValues(&quot;approvalRequired&quot;).ToString,Boolean)},{&quot;approvalWorkflow&quot;,miscValues(&quot;approvalWorkflow&quot;).ToString},&#xA;{&quot;checkAmountBussinessRule&quot;,miscValues(&quot;checkAmountBussinessRule&quot;).ToString},{&quot;processExpenseInvoice&quot;,CType(miscValues(&quot;processExpenseInvoice&quot;).ToString,Boolean)},&#xA;{&quot;miscValues&quot;,miscValues},{&quot;MasterDownloads&quot;,configurationFolder+&quot;\OutlookDownloads\MasterDownloads&quot;}}]" ContinueOnError="False" DisplayName="GetOutlookEmails Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_23" WorkflowFile="[projectPath+&quot;\Outlook_ProcessNew.xaml&quot;]" />
          </If.Then>
          <If.Else>
            <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Log ProcessEmails False" sap2010:WorkflowViewState.IdRef="Append_Line_12" Line="[&quot;ProcessEmails value is &quot;+miscValues(&quot;processEmails&quot;).ToString +&quot;and invoiceSource value is &quot;+miscValues(&quot;invoiceSource&quot;).ToString]" Source="[logFile]" />
          </If.Else>
        </If>
        <If Condition="[Ctype(miscValues(&quot;processFolders&quot;).ToString,Boolean) and (miscValues(&quot;invoiceFolderPath&quot;).ToString &lt;&gt; &quot;&quot; and miscValues(&quot;invoiceFolderPath&quot;).ToString &lt;&gt; &quot;NA&quot;)]" DisplayName="IfProcessFolders" sap2010:WorkflowViewState.IdRef="If_31">
          <If.Then>
            <iaw:InvokeWorkflow OutputArguments="{x:Null}" ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;configurationFolder&quot;,configurationFolder},{&quot;logFile&quot;,logFile},{&quot;tenantID&quot;,tenantID},{&quot;colemanAPI&quot;,miscValues(&quot;colemanAPI&quot;).ToString},{&quot;userIdentifier&quot;,userIdentifier},{&quot;distributionType&quot;,distributionType},{&quot;projectPath&quot;,projectPath},{&quot;enableMessageBoxes&quot;,enableMessageBoxes},{&quot;poFilterValues&quot;,miscValues(&quot;poFilterValues&quot;).ToString},{&quot;poFilterCondition&quot;,miscValues(&quot;poFilterCondition&quot;).ToString},{&quot;datalakeAPILogicalId&quot;,miscValues(&quot;datalakeAPILogicalId&quot;).ToString},{&quot;chargeCode&quot;,miscValues(&quot;chargeCode&quot;).ToString},{&quot;discountCode&quot;,miscValues(&quot;discountCode&quot;).ToString},{&quot;authUser&quot;,miscValues(&quot;authUser&quot;).ToString},{&quot;imsAPIUrl&quot;,miscValues(&quot;imsAPIUrl&quot;).ToString},{&quot;invoiceFolderPath&quot;,miscValues(&quot;invoiceFolderPath&quot;).ToString},{&quot;vatCodeConfig&quot;,miscValues(&quot;vatCodeConfig&quot;).ToString},{&quot;poDiscountsHandlingConfig&quot;,miscValues(&quot;poDiscountsHandlingConfig&quot;).ToString},{&quot;matchVendorItemCode&quot;,CType(miscValues(&quot;matchVendorItemCode&quot;).ToString,Boolean)},{&quot;extractNumericFromPO&quot;,CType(miscValues(&quot;extractNumericFromPO&quot;).ToString,Boolean)},{&quot;extractFromWidgetDatalake&quot;,CType(miscValues(&quot;extractFromWidgetDatalake&quot;).ToString,Boolean)},{&quot;approvalRequired&quot;,CType(miscValues(&quot;approvalRequired&quot;).ToString,Boolean)},{&quot;approvalWorkflow&quot;,miscValues(&quot;approvalWorkflow&quot;).ToString},{&quot;checkAmountBussinessRule&quot;,miscValues(&quot;checkAmountBussinessRule&quot;).ToString},{&quot;miscValues&quot;,miscValues},{&quot;processExpenseInvoice&quot;,CType(miscValues(&quot;processExpenseInvoice&quot;).ToString,Boolean)}}]" ContinueOnError="False" DisplayName="Process Folders" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_21" WorkflowFile="[projectPath+&quot;\ReadFilesFromFolder.xaml&quot;]" />
          </If.Then>
          <If.Else>
            <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Log processFoldersFalse" sap2010:WorkflowViewState.IdRef="Append_Line_13" Line="[&quot;processFolders value is &quot;+miscValues(&quot;processFolders&quot;).ToString +&quot; and invoiceFolderPath value is &quot;+miscValues(&quot;invoiceFolderPath&quot;).ToString]" Source="[logFile]" />
          </If.Else>
        </If>
        <If Condition="[Ctype(miscValues(&quot;extractFromWidgetDatalake&quot;).ToString,Boolean) AND Ctype(miscValues(&quot;reprocess&quot;).ToString,Boolean)]" DisplayName="extractFromWidgetDatalake If" sap2010:WorkflowViewState.IdRef="If_33">
          <If.Then>
            <Sequence DisplayName="extractFromWidgetDatalake Sequence" sap2010:WorkflowViewState.IdRef="Sequence_29">
              <iaw:InvokeWorkflow OutputArguments="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;configurationFolder&quot;,configurationFolder},{&quot;tenantID&quot;,tenantID},{&quot;colemanAPI&quot;,miscValues(&quot;colemanAPI&quot;).ToString},{&quot;logFile&quot;,logFile},{&quot;distributionType&quot;,distributionType},{&quot;userIdentifier&quot;,userIdentifier},{&quot;projectPath&quot;,projectPath},{&quot;miscValues&quot;,miscValues},{&quot;maxNotReceivedCount&quot;,CType(miscValues(&quot;maxNotReceivedCount&quot;).ToString,Integer)},{&quot;datalakeAPILogicalId&quot;,miscValues(&quot;datalakeAPILogicalId&quot;).ToString},{&quot;chargeCode&quot;,miscValues(&quot;chargeCode&quot;).ToString},{&quot;discountCode&quot;,miscValues(&quot;discountCode&quot;).ToString},{&quot;authUser&quot;,miscValues(&quot;authUser&quot;).ToString},{&quot;imsAPIUrl&quot;,miscValues(&quot;imsAPIUrl&quot;).ToString},{&quot;extractFromWidgetDatalake&quot;,CType(miscValues(&quot;extractFromWidgetDatalake&quot;).ToString,Boolean)},{&quot;approvalRequired&quot;,CType(miscValues(&quot;approvalRequired&quot;).ToString,Boolean)},{&quot;approvalWorkflow&quot;,miscValues(&quot;approvalWorkflow&quot;).ToString},{&quot;checkAmountBussinessRule&quot;,miscValues(&quot;checkAmountBussinessRule&quot;).ToString},{&quot;matchVendorItemCode&quot;,CType(miscValues(&quot;matchVendorItemCode&quot;).ToString,Boolean)},{&quot;enableMessageBoxes&quot;,enableMessageBoxes}}]" ContinueOnError="True" DisplayName="Read Datalake" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_22" ResponseCode="[datalakeResponseCode]" WorkflowFile="[projectPath+&quot;\ExtractFromDatalake - Copy.xaml&quot;]" />
              <If Condition="[datalakeResponseCode&lt;&gt; 200]" sap2010:WorkflowViewState.IdRef="If_32">
                <If.Then>
                  <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Datalake error Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_14" Line="Error occured while retriving the data from the Datalake." Source="[logFile]" />
                </If.Then>
              </If>
            </Sequence>
          </If.Then>
          <If.Else>
            <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Log extractFromWidgetDatalake and reprocess in Else block" sap2010:WorkflowViewState.IdRef="Append_Line_18" Line="[&quot;extractFromWidgetDatalake value is &quot;+miscValues(&quot;extractFromWidgetDatalake&quot;).ToString +&quot;and reprocess value is &quot;+miscValues(&quot;reprocess&quot;).ToString]" Source="[logFile]" />
          </If.Else>
        </If>
      </Sequence>
    </TryCatch.Try>
    <TryCatch.Catches>
      <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_3">
        <ActivityAction x:TypeArguments="s:Exception">
          <ActivityAction.Argument>
            <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
          </ActivityAction.Argument>
          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Main Catch Block Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_23" Line="[&quot;&quot;+Environment.NewLine+&quot;M3 Vendor Invoice Procesing Ended with below exception - &quot;+Environment.NewLine+Exception.Message]" Source="[logFile]" />
        </ActivityAction>
      </Catch>
    </TryCatch.Catches>
    <TryCatch.Finally>
      <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="End Time Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_25" Line="[&quot;End Time : &quot; +System.DateTime.Now.ToString(&quot;yyyy/MM/dd HH:mm:ss&quot;)]" Source="[logFile]" />
    </TryCatch.Finally>
    <sads:DebugSymbol.Symbol>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</sads:DebugSymbol.Symbol>
  </TryCatch>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="File_Delete_13" sap:VirtualizedContainerService.HintSize="553,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_3" sap:VirtualizedContainerService.HintSize="553,22" />
      <sap2010:ViewStateData Id="Assign_37" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="JQTransform_3" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_42" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_43" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="DownloadFile_URL_1" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_33" sap:VirtualizedContainerService.HintSize="264,508">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_44" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="MessageBox_3" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Append_Line_24" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_34" sap:VirtualizedContainerService.HintSize="264,308">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_36" sap:VirtualizedContainerService.HintSize="553,656">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ReadRange_1" sap:VirtualizedContainerService.HintSize="553,22" />
      <sap2010:ViewStateData Id="Assign_32" sap:VirtualizedContainerService.HintSize="553,60" />
      <sap2010:ViewStateData Id="Assign_52" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_53" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_39" sap:VirtualizedContainerService.HintSize="264,288">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_54" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_55" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_40" sap:VirtualizedContainerService.HintSize="264,288">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_56" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_57" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_41" sap:VirtualizedContainerService.HintSize="264,288">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_4" sap:VirtualizedContainerService.HintSize="476.666666666667,526">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_2" sap:VirtualizedContainerService.HintSize="553,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_32" sap:VirtualizedContainerService.HintSize="476.666666666667,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeWorkflow_18" sap:VirtualizedContainerService.HintSize="476.666666666667,22" />
      <sap2010:ViewStateData Id="Assign_21" sap:VirtualizedContainerService.HintSize="476.666666666667,62" />
      <sap2010:ViewStateData Id="Append_Line_7" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Append_Line_8" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Append_Line_19" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_2" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Append_Line_9" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Catch`1_2" sap:VirtualizedContainerService.HintSize="404.666666666667,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_2" sap:VirtualizedContainerService.HintSize="464,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_45" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Assign_71" sap:VirtualizedContainerService.HintSize="476.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_29" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_30" sap:VirtualizedContainerService.HintSize="242,65.3333333333333" />
      <sap2010:ViewStateData Id="Sequence_31" sap:VirtualizedContainerService.HintSize="476.666666666667,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_31" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_22" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="File_Delete_12" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="InvokeWorkflow_19" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_20" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_24" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_25" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_26" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_34" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_27" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="If_35" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_25" sap:VirtualizedContainerService.HintSize="264,903.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_1" sap:VirtualizedContainerService.HintSize="294.666666666667,1056">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_10" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Switch`1_1" sap:VirtualizedContainerService.HintSize="476.666666666667,168.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_11" sap:VirtualizedContainerService.HintSize="476.666666666667,22" />
      <sap2010:ViewStateData Id="Assign_58" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_60" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_61" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_62" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_63" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_64" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_65" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_66" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_67" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_68" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_69" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_70" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_72" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_42" sap:VirtualizedContainerService.HintSize="476.666666666667,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_23" sap:VirtualizedContainerService.HintSize="242,65.3333333333333" />
      <sap2010:ViewStateData Id="CommentOut_1" sap:VirtualizedContainerService.HintSize="476.666666666667,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeWorkflow_23" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Append_Line_12" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_30" sap:VirtualizedContainerService.HintSize="476.666666666667,214">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeWorkflow_21" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Append_Line_13" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_31" sap:VirtualizedContainerService.HintSize="476.666666666667,214">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeWorkflow_22" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Append_Line_14" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_32" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_29" sap:VirtualizedContainerService.HintSize="222,238.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_18" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_33" sap:VirtualizedContainerService.HintSize="464,392.666666666667" />
      <sap2010:ViewStateData Id="Sequence_30" sap:VirtualizedContainerService.HintSize="498.666666666667,2318">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_23" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Catch`1_3" sap:VirtualizedContainerService.HintSize="490.666666666667,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_25" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="TryCatch_3" sap:VirtualizedContainerService.HintSize="517.333333333333,2716" />
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="557.333333333333,2956" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>