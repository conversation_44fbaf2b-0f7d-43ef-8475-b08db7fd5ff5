{"name": "M3InvoiceProcessingGenAIV3", "description": "M3InvoiceProcessingGenAIV3Merged 1.0.3", "main": "MainSequence.xaml", "dependencies": {"mscorlib": "[*******]", "PresentationFramework": "[*******]", "System.Xaml": "[*******]", "System": "[*******]", "System.Activities": "[*******]", "System.Activities.Presentation": "[*******]", "YDock": "[*******]", "System.Windows.Forms": "[*******]", "WindowsBase": "[*******]", "NLog": "[*******]", "PresentationCore": "[*******]", "Newtonsoft.Json": "[********]", "System.Drawing": "[*******]", "System.Workflow.Activities": "[*******]", "System.Core": "[*******]", "UiRecorder": "[*******]", "System.IO.Compression": "[*******]", "Microsoft.WindowsAPICodePack.Shell": "[*******]", "Microsoft.Web.WebView2.Core": "[1.0.818.41]", "Microsoft.Web.WebView2.WinForms": "[1.0.818.41]", "System.Net.Http": "[*******]", "System.Configuration": "[*******]", "System.Activities.Core.Presentation": "[*******]", "Microsoft.CSharp": "[*******]", "System.Xml": "[*******]", "System.Workflow.ComponentModel": "[*******]", "System.Xml.Linq": "[*******]", "System.IO.Compression.FileSystem": "[*******]", "RestSharp": "[106.12.0.0]"}, "packages": {"Infor.RPA.Utilities": "[*******]", "Infor.RPA.Utility.Editor": "[*******]", "Infor.Activities.Web": "[*******]", "Infor.Activities.Desktop": "[*******]", "Infor.Activities.Debug": "[*******]", "Infor.Activities.Email": "[*******]", "Infor.Activities.Excel": "[*******]", "Infor.Activities.HTTPRequests": "[*******]", "Infor.Activities.IONAPI": "[*******]", "Infor.Activities.OCR": "[*******]", "Infor.RPA.OCR": "[*******]", "Infor.Activities.OneDrive": "[*******]", "Infor.Activities.Sys": "[*******]", "Infor.Activities.Datatable": "[*******]", "Infor.Activities.SharePointList": "[*******]", "Infor.Activities.Workflow": "[*******]", "Infor.RPA.Security": "[*******]", "Infor.RPA.Commons": "[*******]"}, "schemaVersion": "1.0", "studioVersion": "2025.06.1\r\n.689", "sourceFiles": [{"fileName": "AddCharge.xaml", "filePath": "M3InvoiceProcessingGenAIV3"}, {"fileName": "AddHead.xaml", "filePath": "M3InvoiceProcessingGenAIV3"}, {"fileName": "AddLine.xaml", "filePath": "M3InvoiceProcessingGenAIV3"}, {"fileName": "approval.xaml", "filePath": "M3InvoiceProcessingGenAIV3"}, {"fileName": "ApprovalGUID.xaml", "filePath": "M3InvoiceProcessingGenAIV3"}, {"fileName": "APResp.xaml", "filePath": "M3InvoiceProcessingGenAIV3"}, {"fileName": "BulkFileHandling.xaml", "filePath": "M3InvoiceProcessingGenAIV3"}, {"fileName": "CallColeman.xaml", "filePath": "M3InvoiceProcessingGenAIV3"}, {"fileName": "CheckApproval.xaml", "filePath": "M3InvoiceProcessingGenAIV3"}, {"fileName": "Classification_Split.xaml", "filePath": "M3InvoiceProcessingGenAIV3"}, {"fileName": "CreateDirectories.xaml", "filePath": "M3InvoiceProcessingGenAIV3"}, {"fileName": "DebitNoteCreation 2.xaml", "filePath": "M3InvoiceProcessingGenAIV3"}, {"fileName": "DebitNoteCreation _Updated.xaml", "filePath": "M3InvoiceProcessingGenAIV3"}, {"fileName": "DebitNoteCreation.xaml", "filePath": "M3InvoiceProcessingGenAIV3"}, {"fileName": "DivisionInfo.xaml", "filePath": "M3InvoiceProcessingGenAIV3"}, {"fileName": "DivisiontoGLCode.xaml", "filePath": "M3InvoiceProcessingGenAIV3"}, {"fileName": "DivisionVendorTolerance.xaml", "filePath": "M3InvoiceProcessingGenAIV3"}, {"fileName": "ExportMI.xaml", "filePath": "M3InvoiceProcessingGenAIV3"}, {"fileName": "ExtractFromDatalake - Copy.xaml", "filePath": "M3InvoiceProcessingGenAIV3"}, {"fileName": "GenAI.xaml", "filePath": "M3InvoiceProcessingGenAIV3"}, {"fileName": "GenAI_UnExpInv.xaml", "filePath": "M3InvoiceProcessingGenAIV3"}, {"fileName": "getGenAIPrompt.xaml", "filePath": "M3InvoiceProcessingGenAIV3"}, {"fileName": "GetOCRValuesNew.xaml", "filePath": "M3InvoiceProcessingGenAIV3"}, {"fileName": "getvendordetails.xaml", "filePath": "M3InvoiceProcessingGenAIV3"}, {"fileName": "LinesExtractionWithPO.xaml", "filePath": "M3InvoiceProcessingGenAIV3"}, {"fileName": "MainSequence.xaml", "filePath": "M3InvoiceProcessingGenAIV3"}, {"fileName": "MoveFileToSuccessFailureFolder.xaml", "filePath": "M3InvoiceProcessingGenAIV3"}, {"fileName": "OneDeliveryNotFound.xaml", "filePath": "M3InvoiceProcessingGenAIV3"}, {"fileName": "Outlook_preprocess.xaml", "filePath": "M3InvoiceProcessingGenAIV3"}, {"fileName": "Outlook_ProcessNew.xaml", "filePath": "M3InvoiceProcessingGenAIV3"}, {"fileName": "PO3WayMatching1 (2).xaml", "filePath": "M3InvoiceProcessingGenAIV3"}, {"fileName": "PO3WayMatching1.xaml", "filePath": "M3InvoiceProcessingGenAIV3"}, {"fileName": "PO3WayMatching_Reference.xaml", "filePath": "M3InvoiceProcessingGenAIV3"}, {"fileName": "ProcessDeliveryNote.xaml", "filePath": "M3InvoiceProcessingGenAIV3"}, {"fileName": "ProcessDocument.xaml", "filePath": "M3InvoiceProcessingGenAIV3"}, {"fileName": "ProcessExpenseInvoice - Copy.xaml", "filePath": "M3InvoiceProcessingGenAIV3"}, {"fileName": "ProcessExpenseInvoice.xaml", "filePath": "M3InvoiceProcessingGenAIV3"}, {"fileName": "ProcessExpenseWithPO.xaml", "filePath": "M3InvoiceProcessingGenAIV3"}, {"fileName": "ProcessPOInvoice.xaml", "filePath": "M3InvoiceProcessingGenAIV3"}, {"fileName": "ProcessPOInvoiceAPI.xaml", "filePath": "M3InvoiceProcessingGenAIV3"}, {"fileName": "ProcessPOInvoiceAPI_Ind.xaml", "filePath": "M3InvoiceProcessingGenAIV3"}, {"fileName": "ProcessPOInvoiceAPI_Ind_old.xaml", "filePath": "M3InvoiceProcessingGenAIV3"}, {"fileName": "ReadFilesFromFolder.xaml", "filePath": "M3InvoiceProcessingGenAIV3"}, {"fileName": "RowsFromDeliveryNote.xaml", "filePath": "M3InvoiceProcessingGenAIV3"}, {"fileName": "SendNotification.xaml", "filePath": "M3InvoiceProcessingGenAIV3"}, {"fileName": "SendtoApprovalWorkflow.xaml", "filePath": "M3InvoiceProcessingGenAIV3"}, {"fileName": "SendToIDM.xaml", "filePath": "M3InvoiceProcessingGenAIV3"}, {"fileName": "SendToWidgetIDM.xaml", "filePath": "M3InvoiceProcessingGenAIV3"}, {"fileName": "SentToInvoiceProcessingResults_Attributes.xaml", "filePath": "M3InvoiceProcessingGenAIV3"}, {"fileName": "SentToInvoiceProcessingResults_Headers.xaml", "filePath": "M3InvoiceProcessingGenAIV3"}, {"fileName": "SentToInvoiceProcessingResults_LineData.xaml", "filePath": "M3InvoiceProcessingGenAIV3"}, {"fileName": "SentToRPAReports.xaml", "filePath": "M3InvoiceProcessingGenAIV3"}, {"fileName": "SupplierInfo.xaml", "filePath": "M3InvoiceProcessingGenAIV3"}, {"fileName": "temp-response.json", "filePath": "M3InvoiceProcessingGenAIV3"}, {"fileName": "vatConfiguration.xaml", "filePath": "M3InvoiceProcessingGenAIV3"}, {"fileName": "vendorID.xaml", "filePath": "M3InvoiceProcessingGenAIV3"}, {"fileName": "VendorIDAddressMatch.xaml", "filePath": "M3InvoiceProcessingGenAIV3"}, {"fileName": "VerifyInvoiceExists.xaml", "filePath": "M3InvoiceProcessingGenAIV3"}, {"fileName": "temp-response.json", "filePath": "M3InvoiceProcessingGenAIV3\\Temp\\f69e5a30-0b3c-4f29-9d8a-1e6947a44cd8"}], "resources": [], "projectType": "standard", "expressionLanguage": "VBscript", "backgroundProcess": false, "attended": true, "tenantMetadata": [{"tenantId": "MANDALA_DEM", "processId": "F830A5F0-6A69-458B-A1DF-12F9FEF3D488", "projectVersion": "1.0.3", "globalPackageNamespace": null, "iprRegistered": false}, {"tenantId": "M3CEDEVAPPAIS_DEV", "processId": "1886725C-390D-4BAC-8D0A-616A32B3F6B4", "projectVersion": "1.0.1", "globalPackageNamespace": null, "iprRegistered": false}, {"tenantId": "OLDCASTLE_DEV", "processId": "DFDF13FA-A711-4021-AB51-BCAC0F0FEBC2", "projectVersion": "1.0.26", "globalPackageNamespace": null, "iprRegistered": false}, {"tenantId": "YXH9SKXAKYFXSCVF_TST", "processId": "6D98713B-BEB3-45C6-BE73-77CC0BFBA429", "projectVersion": "1.0.12", "globalPackageNamespace": null, "iprRegistered": false}, {"tenantId": "ZGG6FMGYXSUKG7CA_TST", "processId": "4203367F-BF05-4059-8918-0E50DD4C1F83", "projectVersion": "1.0.4", "globalPackageNamespace": null, "iprRegistered": false}, {"tenantId": "ZGG6FMGYXSUKG7CA_PRD", "processId": "F13A4C4A-561F-4A9D-A014-E3597EE6BF3D", "projectVersion": "1.0.8", "globalPackageNamespace": null, "iprRegistered": false}]}